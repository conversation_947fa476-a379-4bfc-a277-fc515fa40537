<extend name="Mpanel@Base/admin_base" />

<block name="breadcrumb">
    <ol class="breadcrumb">
        <li><a href="{:U('Mpanel/Index/index')}"><i class="fa fa-home"></i> 首页</a></li>
        <li><a href="{:U('OA/Audit/onlineAudit', array('reg_id' => $auditRecord['reg_id']))}"><i class="fa fa-search"></i> 查账详情</a></li>
        <li class="active"><i class="fa fa-file-text-o"></i> 申请发票</li>
    </ol>
</block>

<block name="content">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title"><i class="fa fa-file-text-o"></i> 发票申请表单</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <!-- 左侧：查账信息 -->
                        <div class="col-md-4">
                            <div class="panel panel-info">
                                <div class="panel-heading">
                                    <h4 class="panel-title"><i class="fa fa-info-circle"></i> 查账信息</h4>
                                </div>
                                <div class="panel-body">
                                    <table class="table table-bordered table-striped">
                                        <tbody>
                                            <tr>
                                                <th style="width:40%;"><i class="fa fa-barcode"></i> OA订单号</th>
                                                <td><code>{$auditRecord.oa_id}</code></td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-tag"></i> 会议简称</th>
                                                <td>{$auditRecord.event}</td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-file-text"></i> 论文ID</th>
                                                <td>{$auditRecord.paper_id}</td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-money"></i> 金额</th>
                                                <td>
                                                    <strong class="text-danger">{$auditRecord.total}</strong>
                                                    <span class="label label-default">{$auditRecord.currency}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-calendar"></i> 汇款时间</th>
                                                <td>{$auditRecord.remit_time}</td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-user"></i> 汇款人</th>
                                                <td>{$auditRecord.remitter_info}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧：发票表单 -->
                        <div class="col-md-8">
                            <form id="invoiceForm" action="{:U('OA/Invoice/submitInvoice')}" method="post" class="form-horizontal">
                                <!-- 基本信息 -->
                                <fieldset>
                                    <legend><i class="fa fa-info-circle"></i> 基本信息</legend>

                                    <div class="form-group">
                                        <label for="invoiceTitle" class="col-sm-3 control-label">发票抬头 <span class="text-danger">*</span></label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="invoiceTitle" name="invoiceTitle" placeholder="请输入发票抬头" required>
                                            <p class="help-block">请填写完整的发票抬头，如公司名称或个人姓名</p>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="invoiceType" class="col-sm-3 control-label">发票类型 <span class="text-danger">*</span></label>
                                        <div class="col-sm-9">
                                            <php>
                                                $invoiceTypes = \Common\Lib\InvoiceTypeConstants::getAllTypes();
                                                $invoiceTypeTexts = [];
                                                $invoiceTypeDescriptions = \Common\Lib\InvoiceTypeConstants::getTypeDescriptionMap();
                                                foreach ($invoiceTypes as $type) {
                                                    $invoiceTypeTexts[$type] = \Common\Lib\InvoiceTypeConstants::getTypeText($type);
                                                }
                                            </php>
                                            <foreach name="invoiceTypeTexts" item="text" key="type">
                                                <div class="radio">
                                                    <label>
                                                        <input type="radio" name="invoiceType" id="invoiceType{$type|ucfirst}" value="{$type}" <if condition="$type eq 'pc'">checked</if>>
                                                        {$text}（{$invoiceTypeDescriptions[$type]}）
                                                    </label>
                                                </div>
                                            </foreach>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="amount" class="col-sm-3 control-label">开票金额 <span class="text-danger">*</span></label>
                                        <div class="col-sm-9">
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="amount" name="amount" value="{$invoiceData.amount}" required>
                                                <span class="input-group-addon">{$invoiceData.currency}</span>
                                            </div>
                                            <p class="help-block">开票金额不能超过查账金额</p>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="buyerTaxNum" class="col-sm-3 control-label">税号</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="buyerTaxNum" name="buyerTaxNum" placeholder="请输入税号">
                                            <p class="help-block">专用发票必填，普通发票选填</p>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 联系信息 -->
                                <fieldset>
                                    <legend><i class="fa fa-phone"></i> 联系信息</legend>

                                    <div class="form-group">
                                        <label for="buyerPhone" class="col-sm-3 control-label">联系电话</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="buyerPhone" name="buyerPhone" placeholder="请输入联系电话">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="buyerEmail" class="col-sm-3 control-label">电子邮箱 <span class="text-danger">*</span></label>
                                        <div class="col-sm-9">
                                            <input type="email" class="form-control" id="buyerEmail" name="buyerEmail" placeholder="请输入电子邮箱" required>
                                            <p class="help-block">用于接收电子发票</p>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 专票信息（默认隐藏） -->
                                <fieldset id="bsFields" style="display:none;">
                                    <legend><i class="fa fa-building"></i> 专票信息</legend>

                                    <div class="form-group">
                                        <label for="buyerAddress" class="col-sm-3 control-label">注册地址</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="buyerAddress" name="buyerAddress" placeholder="请输入注册地址">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="buyerAccount" class="col-sm-3 control-label">开户银行</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="buyerAccount" name="buyerAccount" placeholder="请输入开户银行">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="buyerAccountName" class="col-sm-3 control-label">银行账号</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="buyerAccountName" name="buyerAccountName" placeholder="请输入银行账号">
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 商品信息 -->
                                <fieldset>
                                    <legend><i class="fa fa-shopping-cart"></i> 商品信息</legend>

                                    <div class="form-group">
                                        <label for="goodsInfo" class="col-sm-3 control-label">商品信息 <span class="text-danger">*</span></label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" id="goodsInfo" name="goodsInfo" rows="3" required>{$invoiceData.goodsInfo}</textarea>
                                            <p class="help-block">请填写商品或服务名称，如"会议注册费"</p>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 备注信息 -->
                                <fieldset>
                                    <legend><i class="fa fa-comment"></i> 备注信息</legend>

                                    <div class="form-group">
                                        <label for="remark" class="col-sm-3 control-label">备注</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" id="remark" name="remark" rows="3" placeholder="请输入备注信息（选填）"></textarea>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 隐藏字段 -->
                                <input type="hidden" name="oa_id" value="{$auditRecord.oa_id}">
                                <input type="hidden" name="reg_id" value="{$auditRecord.reg_id}">
                                <input type="hidden" name="pay_id" value="{$auditRecord.pay_id}">
                                <input type="hidden" name="cid" value="{$auditRecord.cid}">
                                <input type="hidden" name="event" value="{$auditRecord.event}">
                                <input type="hidden" name="paper_id" value="{$auditRecord.paper_id}">
                                <input type="hidden" name="currency" value="{$auditRecord.currency}">

                                <!-- 提交按钮 -->
                                <div class="form-group">
                                    <div class="col-sm-offset-3 col-sm-9">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fa fa-check"></i> 提交发票申请
                                        </button>
                                        <a href="{:U('OA/Audit/onlineAudit', array('reg_id' => $auditRecord['reg_id']))}" class="btn btn-default btn-lg">
                                            <i class="fa fa-arrow-left"></i> 返回查账详情
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</block>

<block name="scripts">
<script>
    $(document).ready(function() {
        // 发票类型切换
        $('input[name="invoiceType"]').change(function() {
            if ($(this).val() === 'bs') {
                $('#bsFields').slideDown();
                $('#buyerTaxNum').attr('required', true);
                $('#buyerAddress').attr('required', true);
                $('#buyerAccount').attr('required', true);
                $('#buyerAccountName').attr('required', true);
            } else {
                $('#bsFields').slideUp();
                $('#buyerTaxNum').removeAttr('required');
                $('#buyerAddress').removeAttr('required');
                $('#buyerAccount').removeAttr('required');
                $('#buyerAccountName').removeAttr('required');
            }
        });

        // 表单验证
        $('#invoiceForm').validate({
            errorElement: 'div',
            errorClass: 'help-block',
            highlight: function(element) {
                $(element).closest('.form-group').addClass('has-error');
            },
            unhighlight: function(element) {
                $(element).closest('.form-group').removeClass('has-error');
            },
            errorPlacement: function(error, element) {
                if (element.parent('.input-group').length) {
                    error.insertAfter(element.parent());
                } else if (element.parent('.radio').length || element.parent('.checkbox').length) {
                    error.insertAfter(element.parent().parent());
                } else {
                    error.insertAfter(element);
                }
            },
            rules: {
                invoiceTitle: {
                    required: true,
                    minlength: 2
                },
                amount: {
                    required: true,
                    number: true,
                    min: 0.01,
                    max: {$auditRecord.total}
                },
                buyerEmail: {
                    required: true,
                    email: true
                },
                goodsInfo: {
                    required: true,
                    minlength: 2
                }
            },
            messages: {
                invoiceTitle: {
                    required: "请输入发票抬头",
                    minlength: "发票抬头至少需要2个字符"
                },
                amount: {
                    required: "请输入开票金额",
                    number: "请输入有效的数字",
                    min: "开票金额必须大于0",
                    max: "开票金额不能超过查账金额"
                },
                buyerEmail: {
                    required: "请输入电子邮箱",
                    email: "请输入有效的电子邮箱地址"
                },
                goodsInfo: {
                    required: "请输入商品信息",
                    minlength: "商品信息至少需要2个字符"
                }
            },
            submitHandler: function(form) {
                // 显示加载状态
                $('button[type="submit"]').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 提交中...');
                form.submit();
            }
        });
    });
</script>
</block>
