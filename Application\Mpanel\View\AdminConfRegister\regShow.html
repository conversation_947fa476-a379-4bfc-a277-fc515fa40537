<extend name="Base/admin_base" />
<block name="title">
  <title>注册详情</title>
</block>

<block name="breadcrumb">
  <ol class="breadcrumb">
    <li><a href="{:U('Index/index')}">首页</a></li>
    <li><a href="{:U('AdminConfRegister/index')}">注册列表</a></li>
    <li>注册详情</li>
  </ol>
</block>
<block name="content">
  <!-- 顶部下载按钮区域 -->
  <div style="border-bottom: 1px solid #ddd; padding-bottom: 10px; margin-bottom: 20px;">
    <php>if(!empty($info["lastpaper"])) { </php>
      <a href="{:U('downflieReg',array('id'=>$info['id'],'type'=>'pdf'))}" class="btn btn-info" style="margin-right: 10px;">
        <i class="fa fa-cloud-download"></i> 下载最终稿件
      </a>
    <php>}</php>
    <php>if(!empty($info["copyright"])) { </php>
      <a href="__ROOT__/Uploads/{$info.copyright}" class="btn btn-success">
        <i class="fa fa-copyright"></i> 下载版权证明
      </a>
    <php>}</php>
  </div>

  <div class="panel panel-default">
    <div class="panel-heading">
      <h3 class="panel-title">注册详情</h3>
    </div>
    <div class="panel-body">
      <!-- 重要信息摘要区域 -->
      <div class="panel panel-primary" style="border-radius: 0; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
        <div class="panel-heading" style="border-radius: 0;">
          <h3 class="panel-title">
            <i class="fa fa-star"></i> 重要信息摘要
            <button type="button" class="btn btn-xs btn-warning pull-right" onclick="showEditModal()" style="margin-top: -2px;">
              <i class="fa fa-edit"></i> 修改会议简称和文章ID
            </button>
          </h3>
        </div>
        <div class="panel-body" style="background-color: #f9f9f9; padding: 15px 20px;">
          <div class="row">
            <div class="col-md-3">
              <div style="margin-bottom: 10px;">
                <span style="color: #666; font-size: 13px;">会议简称</span>
                <div style="font-size: 16px; font-weight: 500; margin-top: 3px;">{$info.event}</div>
              </div>
            </div>
            <div class="col-md-3">
              <div style="margin-bottom: 10px;">
                <span style="color: #666; font-size: 13px;">Paper ID</span>
                <div style="font-size: 16px; font-weight: 500; margin-top: 3px;">{$info.paperid}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div style="margin-bottom: 10px;">
                <span style="color: #666; font-size: 13px;">文章标题</span>
                <div style="font-size: 16px; font-weight: 500; margin-top: 3px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{$info.paper_title}</div>
              </div>
            </div>
          </div>
          <div class="row" style="margin-top: 5px;">
            <div class="col-md-6">
              <div>
                <span style="color: #666; font-size: 13px;">邮箱</span>
                <div style="font-size: 15px; margin-top: 3px;">{$info.email}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div>
                <span style="color: #666; font-size: 13px;">作者</span>
                <div style="font-size: 15px; margin-top: 3px;">{$info.firstname} {$info.middlename} {$info.lastname}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <!-- 左侧信息区域 -->
        <div class="col-md-7">
          <!-- 注册费用与发票信息 - 合并优化版本 -->
          <div class="panel panel-info" style="border-radius: 0; box-shadow: 0 1px 3px rgba(0,0,0,0.1); margin-bottom: 20px;">
            <div class="panel-heading" style="border-radius: 0; background-color: #d9edf7; border-color: #bce8f1;">
              <h3 class="panel-title"><i class="fa fa-money"></i> 注册费用明细</h3>
            </div>
            <div class="panel-body" style="padding: 0;">
              <!-- 注册费用表格 - 优化版 -->
              <div class="table-responsive">
                <table class="table table-bordered" style="margin-bottom: 0; border: 1px solid #ddd;">
                  <thead>
                    <tr style="background-color: #e8f4f8;">
                      <th style="border-bottom: 2px solid #ddd; width: 40%; text-align: center; color: #31708f; font-size: 14px;">项目</th>
                      <th style="border-bottom: 2px solid #ddd; width: 15%; text-align: center; color: #31708f; font-size: 14px;">数量</th>
                      <!-- 根据是否有美元和人民币金额动态显示列 -->
                      <php>if(!empty($total["usd"]) && $total["usd"] > 0) { </php>
                        <th style="border-bottom: 2px solid #ddd; width: 22.5%; text-align: center; color: #31708f; font-size: 14px;">美元</th>
                      <php>}</php>
                      <php>if(!empty($total["cny"]) && $total["cny"] > 0) { </php>
                        <th style="border-bottom: 2px solid #ddd; width: 22.5%; text-align: center; color: #31708f; font-size: 14px;">人民币</th>
                      <php>}</php>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- 注册项目 -->
                    <volist name="type" id="t">
                      <php>if($t["no"] >= 1) { </php>
                        <tr>
                          <td style="font-weight: 500; padding: 8px 10px; background-color: #f9f9f9;">{$t.name}</td>
                          <td style="padding: 8px 10px; text-align: center;">{$t.no}</td>
                          <!-- 根据是否有美元和人民币金额动态显示单元格 -->
                          <php>if(!empty($total["usd"]) && $total["usd"] > 0) { </php>
                            <td style="padding: 8px 10px; font-weight: 500; text-align: center;">
                              <span style="color: #3c763d;">${$t.usd}</span>
                            </td>
                          <php>}</php>
                          <php>if(!empty($total["cny"]) && $total["cny"] > 0) { </php>
                            <td style="padding: 8px 10px; font-weight: 500; text-align: center;">
                              <span style="color: #3c763d;">¥{$t.cny}</span>
                            </td>
                          <php>}</php>
                        </tr>
                      <php>}</php>
                    </volist>

                    <!-- 额外项目 -->
                    <volist name="extras" id="e">
                      <php>if($e["no"] >= 1) { </php>
                        <tr>
                          <td style="font-weight: 500; padding: 8px 10px; background-color: #f9f9f9;">{$e.name}</td>
                          <td style="padding: 8px 10px; text-align: center;">{$e.no}</td>
                          <!-- 根据是否有美元和人民币金额动态显示单元格 -->
                          <php>if(!empty($total["usd"]) && $total["usd"] > 0) { </php>
                            <td style="padding: 8px 10px; font-weight: 500; text-align: center;">
                              <span style="color: #31708f;">${$e.usd}</span>
                            </td>
                          <php>}</php>
                          <php>if(!empty($total["cny"]) && $total["cny"] > 0) { </php>
                            <td style="padding: 8px 10px; font-weight: 500; text-align: center;">
                              <span style="color: #31708f;">¥{$e.cny}</span>
                            </td>
                          <php>}</php>
                        </tr>
                      <php>}</php>
                    </volist>

                    <!-- 额外费用 -->
                    <php>if(!empty($other["num"])) { </php>
                      <tr>
                        <td style="font-weight: 500; padding: 8px 10px; background-color: #f9f9f9;">
                          额外费用
                          <php>if(!empty($other["name"])) { </php>
                            <a tabindex="0" role="button" data-toggle="popover" data-trigger="focus" data-content="{$other.name}" style="margin-left: 5px;">
                              <i class="fa fa-question-circle"></i>
                            </a>
                          <php>}</php>
                        </td>
                        <td style="padding: 8px 10px; text-align: center;">1</td>
                        <!-- 根据额外费用类型显示在对应的列中 -->
                        <php>if(!empty($total["usd"]) && $total["usd"] > 0) { </php>
                          <td style="padding: 8px 10px; font-weight: 500; text-align: center;">
                            <php>if($other["type"] == "usd") { </php>
                              <span style="color: #8a6d3b;">${$other.num}</span>
                            <php>} else { </php>
                              -
                            <php>}</php>
                          </td>
                        <php>}</php>
                        <php>if(!empty($total["cny"]) && $total["cny"] > 0) { </php>
                          <td style="padding: 8px 10px; font-weight: 500; text-align: center;">
                            <php>if($other["type"] == "cny") { </php>
                              <span style="color: #8a6d3b;">¥{$other.num}</span>
                            <php>} else { </php>
                              -
                            <php>}</php>
                          </td>
                        <php>}</php>
                      </tr>
                    <php>}</php>
                  </tbody>

                  <!-- 总计行 -->
                  <tfoot>
                    <tr style="background-color: #f5f5f5;">
                      <td style="text-align: right; font-weight: bold; border-top: 2px solid #ddd; padding: 10px;">合计</td>
                      <td style="border-top: 2px solid #ddd; padding: 10px; text-align: center;">-</td>

                      <!-- 美元总计 -->
                      <php>if(!empty($total["usd"]) && $total["usd"] > 0) { </php>
                        <td style="border-top: 2px solid #ddd; padding: 10px; font-weight: bold; text-align: center;">
                          <span style="color: #d9534f; font-size: 16px; font-family: 'Arial', sans-serif;">$<span>{$total.usd}</span></span>
                        </td>
                      <php>}</php>

                      <!-- 人民币总计 -->
                      <php>if(!empty($total["cny"]) && $total["cny"] > 0) { </php>
                        <td style="border-top: 2px solid #ddd; padding: 10px; font-weight: bold; text-align: center;">
                          <span style="color: #d9534f; font-size: 16px; font-family: 'Arial', sans-serif;">¥<span>{$total.cny}</span></span>
                        </td>
                      <php>}</php>
                    </tr>
                  </tfoot>
                </table>
              </div>

              <!-- 发票/收据信息 - 压缩版 -->
              <div style="background-color: #f5f9ff; border-top: 1px solid #ddd; padding: 10px 15px; display: flex; flex-wrap: wrap; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center; flex: 1;">
                  <span style="background-color: #5bc0de; color: white; padding: 4px 8px; border-radius: 3px; margin-right: 10px;">
                    <i class="fa fa-file-o"></i> 发票信息
                  </span>
                  <span style="color: #333; font-size: 13px;">
                    <strong>Bill to:</strong> {$info.bill}
                    <php>if(!empty($info.remarks)) { </php>
                      <span style="margin-left: 15px;"><strong>Remarks:</strong> {$info.remarks}</span>
                    <php>}</php>
                  </span>
                </div>

                <!-- 电子发票链接 -->
                <php>if($invoice_info["c_url"] != "") { </php>
                  <div>
                    <a href="{$invoice_info.c_url}" target="_blank" class="btn btn-xs btn-primary" style="border-radius: 3px;">
                      <i class="fa fa-file-pdf-o"></i> 查看电子发票
                    </a>
                  </div>
                <php>}</php>
              </div>
            </div>
          </div>

          <!-- 注册信息 -->
          <div class="panel panel-default" style="border-radius: 0; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <div class="panel-heading" style="border-radius: 0; background-color: #f5f5f5;">
              <h3 class="panel-title"><i class="fa fa-user"></i> 注册信息</h3>
            </div>
            <div class="panel-body" style="padding: 0;">
              <table class="table table-striped" style="margin-bottom: 0;">
                <tbody>
                  <tr>
                    <th width="25%" style="border-top: none; background-color: #f9f9f9;">Submit Time</th>
                    <td style="border-top: none;">{$info.datetime|date="Y-m-d H:i:s",###}</td>
                  </tr>
                  <php>if(!empty($info["updatetime"])) { </php>
                    <tr>
                      <th width="25%" style="background-color: #f9f9f9;">Update Time</th>
                      <td>{$info.updatetime|date="Y-m-d H:i:s",###}</td>
                    </tr>
                  <php>}</php>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">First Name</th>
                    <td>{$info.firstname}</td>
                  </tr>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">Middlename</th>
                    <td>{$info.middlename}</td>
                  </tr>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">Lastname</th>
                    <td>{$info.lastname}</td>
                  </tr>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">Position</th>
                    <td>{$info.position}</td>
                  </tr>
                  <php>if(!empty($info["otherposition"])) { </php>
                    <tr>
                      <th width="25%" style="background-color: #f9f9f9;">Other position</th>
                      <td>{$info.otherposition}</td>
                    </tr>
                  <php>}</php>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">Affiliation</th>
                    <td>{$info.affiliation}</td>
                  </tr>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">Country</th>
                    <td>{$info.country}</td>
                  </tr>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">Telephone Number</th>
                    <td>{$info.telenumber}</td>
                  </tr>
                  <php>if(!empty($info["presentation"])) { </php>
                    <tr>
                      <th width="25%" style="background-color: #f9f9f9;">Presentation Type</th>
                      <td>{$info.presentation}</td>
                    </tr>
                  <php>}</php>
                  <php>if(!empty($info["attendence"])) { </php>
                    <tr>
                      <th width="25%" style="background-color: #f9f9f9;">Presentation:</th>
                      <td>{$info.attendence}</td>
                    </tr>
                  <php>}</php>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">All_authors_names</th>
                    <td>{$info.all_authors_names}</td>
                  </tr>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">The Attendee's Name</th>
                    <td>{$info.attendeename}</td>
                  </tr>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">Attendee's Position</th>
                    <td>{$info.attendee_position}</td>
                  </tr>
                  <php>if(!empty($info["photo"])) { </php>
                    <tr>
                      <th width="25%" style="background-color: #f9f9f9;">Photo</th>
                      <td><a href="{:U('downflieReg',array('id'=>$info['id'],'type'=>'jpg'))}" class="btn btn-xs btn-info"><i class="fa fa-eye"></i><span style="margin-left:5px;">Download</span></a></td>
                    </tr>
                  <php>}</php>
                  <php>if(!empty($info["cbeesid"])) { </php>
                    <tr>
                      <th width="25%" style="background-color: #f9f9f9;">Membership ID</th>
                      <td>{$info.cbeesid}</td>
                    </tr>
                  <php>}</php>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">Diet</th>
                    <td>{$info.diet}</td>
                  </tr>
                  <php>if(!empty($more)) { </php>
                    <volist name="more" id="mo">
                      <tr>
                        <th width="25%" style="background-color: #f9f9f9;">{$key}</th>
                        <td>{$mo}</td>
                      </tr>
                    </volist>
                  <php>}</php>
                  <tr>
                    <th width="25%" style="background-color: #f9f9f9;">Comments</th>
                    <td>{$info.comments}</td>
                  </tr>
                  <php>if(!empty($info["media"])) { </php>
                    <tr>
                      <th width="25%" style="background-color: #f9f9f9;">How did you hear about us</th>
                      <td>{$info.media}</td>
                    </tr>
                  <php>}</php>
                </tbody>
              </table>
            </div>
          </div>


        </div>

        <!-- 右侧信息区域 -->
        <div class="col-md-5">

          <!-- 付款信息 -->
          <div class="panel panel-default" style="border-radius: 0; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <div class="panel-heading" style="border-radius: 0; background-color: #f5f5f5; border-bottom: 2px solid #ddd;">
              <h3 class="panel-title"><i class="fa fa-credit-card"></i> 付款信息</h3>
            </div>
            <div class="panel-body" style="padding: 15px;">
              <php>if(!empty($pay_info) && $pay_info["status"] == 20) { </php>
                <div style="margin-bottom: 15px; text-align: center; background-color: #dff0d8; padding: 10px; border-radius: 3px; border-left: 4px solid #3c763d;">
                  <strong style="font-size: 20px; color: #d9534f; font-family: 'Arial', sans-serif;">{$paytotal}</strong>
                </div>
              <php>}</php>

              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                <span style="color: #555; font-weight: 500;">付款状态</span>
                <php>
                switch($info["pay_status"]) {
                  case "0":
                    echo '<span class="label label-default" style="font-size: 12px; padding: 4px 8px;">等待付款</span>';
                    break;
                  case "1":
                    echo '<span class="label label-success" style="font-size: 12px; padding: 4px 8px;">支付成功</span>';
                    break;
                  case "2":
                    echo '<span class="label label-warning" style="font-size: 12px; padding: 4px 8px;">等待审核</span>';
                    break;
                  case "3":
                    echo '<span class="label label-danger" style="font-size: 12px; padding: 4px 8px;">支付失败</span>';
                    break;
                }
                </php>
              </div>

              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                <span style="color: #555; font-weight: 500;">付款方式</span>
                <php>
                switch($info["pay_type"]) {
                  case "1":
                    echo '<span class="label label-primary" style="font-size: 12px; padding: 4px 8px;">在线支付</span>';
                    break;
                  case "2":
                    echo '<span class="label label-info" style="font-size: 12px; padding: 4px 8px;">转账汇款</span>';
                    break;
                }
                </php>
              </div>

              <php>if($info["pay_type"] == 1) { </php>
                <hr style="margin: 10px 0;">
                <p><strong>订单号：</strong> <a href="javascript:void(0);" onclick="redirectToOrderPage('{$pay_info.orderid}')">{$pay_info.orderid}</a></p>
                <p><strong>支付时间：</strong>
                  <php>
                    // 检查时间戳格式并正确显示
                    if(!empty($pay_info["addtime"])) {
                      if(is_numeric($pay_info["addtime"]) && $pay_info["addtime"] > 946656000) { // 2000年以后的时间戳
                        echo date("Y-m-d H:i:s", $pay_info["addtime"]);
                      } else if(!empty($pay_info["ordetime"]) && is_numeric($pay_info["ordetime"]) && $pay_info["ordetime"] > 946656000) {
                        // 如果addtime不正确，尝试使用ordetime
                        echo date("Y-m-d H:i:s", $pay_info["ordetime"]);
                      } else {
                        echo $pay_info["addtime"];
                      }
                    } else if(!empty($pay_info["ordetime"])) {
                      // 如果没有addtime，尝试使用ordetime
                      if(is_numeric($pay_info["ordetime"]) && $pay_info["ordetime"] > 946656000) {
                        echo date("Y-m-d H:i:s", $pay_info["ordetime"]);
                      } else {
                        echo $pay_info["ordetime"];
                      }
                    } else {
                      echo "未知";
                    }
                  </php>
                </p>
              <php>}</php>

              <php>if($is_new_transfer) { </php>
                <!-- 新版本转账信息显示 -->
                <hr style="margin: 10px 0;">
                <div class="alert alert-info">
                  <i class="fa fa-info-circle"></i> 此为新版本转账信息
                </div>

                <div class="well well-sm" style="margin-top: 10px; margin-bottom: 10px;">
                  <p><strong>订单号：</strong> {$transfer_info.order_id}</p>
                  <p><strong>转账人姓名：</strong> {$transfer_info.transfer_name}</p>
                  <p><strong>转账账户：</strong> {$transfer_info.transfer_account}</p>
                  <p><strong>转账金额：</strong> <span class="text-danger">{$transfer_info.total} {$transfer_info.currency_text}</span></p>
                  <p><strong>转账时间：</strong> {$transfer_info.transfer_time_text}</p>
                  <php>if(!empty($transfer_info["transfer_pic"])) { </php>
                    <p>
                      <strong>转账凭证：</strong>
                      <a href="__ROOT__/Uploads/{$transfer_info.transfer_pic}" class="btn btn-xs btn-default" target="_blank">
                        <i class="fa fa-file-image-o"></i> 查看
                      </a>
                    </p>
                  <php>}</php>
                  <php>if(!empty($transfer_info["remark"])) { </php>
                    <p><strong>备注：</strong> {$transfer_info.remark}</p>
                  <php>}</php>
                  <p><strong>创建时间：</strong> {$transfer_info.create_time_text}</p>
                  <php>if(!empty($transfer_info["update_time"])) { </php>
                    <p><strong>更新时间：</strong> {$transfer_info.update_time_text}</p>
                  <php>}</php>
                </div>

                <!-- 新版本不显示确认收到付款按钮，因为需要发送到OA后自动审核 -->
                <div class="alert alert-warning">
                  <i class="fa fa-exclamation-triangle"></i> 新版本转账信息将通过OA系统自动审核，无需人工确认
                </div>
              <php>} else { </php>
                <!-- 旧版本转账信息显示 -->
                <php>if(!empty($info["proof"])) { </php>
                  <hr style="margin: 10px 0;">
                  <p>
                    <strong>付款截图：</strong>
                    <a href="__ROOT__/Uploads/{$proof.pic}" class="btn btn-xs btn-default" target="_blank">
                      <i class="fa fa-file-image-o"></i> 查看
                    </a>
                  </p>

                  <div class="well well-sm" style="margin-top: 10px; margin-bottom: 10px;">
                    <p><strong>Account Name:</strong> {$proof.name}</p>
                    <p><strong>Account Number:</strong> {$proof.number}</p>
                    <p><strong>Transfer Amount:</strong> {$proof.amount}</p>
                    <p><strong>Transfer Date:</strong> {$proof.date}</p>
                  </div>
                <php>}</php>

                <php>if($info["pay_type"] == 2) { </php>
                  <div style="margin-top: 15px;">
                    <php>if($info["pay_status"] != 1) { </php>
                      <a class="btn btn-info btn-block" href="javascript:void(0);" onclick="confirmPayment({$info.id})" role="button">
                        <i class="fa fa-check-circle"></i> 确认收到付款
                      </a>
                    <php>} else { </php>
                      <button class="btn btn-success btn-block" disabled>
                        <i class="fa fa-check-circle"></i> 付款已确认
                      </button>
                    <php>}</php>
                  </div>
                <php>}</php>
              <php>}</php>
            </div>
          </div>

          <!-- 发票申请信息面板 -->
          <notempty name="invoices">
          <div class="panel panel-info" style="border-radius: 0; box-shadow: 0 1px 3px rgba(0,0,0,0.1); margin-top: 15px; border-color: #bce8f1;">
            <div class="panel-heading" style="border-radius: 0; background-color: #d9edf7; border-color: #bce8f1;">
              <h3 class="panel-title"><i class="fa fa-file-text-o"></i> 发票申请信息</h3>
            </div>
            <div class="panel-body" style="padding: 0;">
              <div class="table-responsive">
                <table class="table table-bordered table-striped" style="margin-bottom: 0;">
                  <thead>
                    <tr>
                      <th style="text-align: center; width: 40%;">发票抬头</th>
                      <th style="text-align: center; width: 20%;">金额</th>
                      <th style="text-align: center; width: 20%;">状态</th>
                      <th style="text-align: center; width: 20%;">申请日期</th>
                    </tr>
                  </thead>
                  <tbody>
                    <foreach name="invoices" item="invoice">
                      <tr>
                        <td style="vertical-align: middle;">{$invoice.invoice_title}</td>
                        <td style="vertical-align: middle; text-align: center;">
                          <strong class="text-danger">{$invoice.amount}</strong>
                        </td>
                        <td style="vertical-align: middle; text-align: center;">
                          <span class="label label-{$invoice.status_class|default='default'}">{$invoice.status_text|default="状态$invoice[status]"}</span>
                        </td>
                        <td style="vertical-align: middle; text-align: center;">
                          <php>
                            echo !empty($invoice['create_time']) ? (is_numeric($invoice['create_time']) ? date('Y-m-d', $invoice['create_time']) : $invoice['create_time']) : '-';
                          </php>
                        </td>
                      </tr>
                    </foreach>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          </notempty>

          <!-- 查账信息面板 -->
          <div class="panel panel-info" id="auditInfoPanel" style="border-radius: 0; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border-color: #bce8f1;">
            <div class="panel-heading" style="border-radius: 0; background-color: #d9edf7; border-color: #bce8f1;">
              <h3 class="panel-title"><i class="fa fa-search"></i> 查账信息</h3>
            </div>
            <div class="panel-body" style="padding: 15px;">
              <php>if(!$is_new_transfer && $info["pay_type"] == 2 && !empty($info["proof"])) { </php>
                <!-- 旧版本转账信息提示 -->
                <div style="background-color: #fcf8e3; border-left: 4px solid #f0ad4e; padding: 10px; margin-bottom: 15px; color: #8a6d3b; font-size: 13px;">
                  <i class="fa fa-exclamation-triangle"></i> 该转账信息为老版本，不支持自动查账
                </div>
              <php>}</php>

              <div id="auditInfoLoading" class="text-center" style="padding: 20px 0;">
                <i class="fa fa-spinner fa-spin fa-2x" style="color: #5bc0de;"></i>
                <p style="margin-top: 10px; color: #777;">正在加载查账信息...</p>
              </div>

              <div id="auditInfoContent" style="display: none;">
                <!-- 查账信息将通过 JavaScript 动态加载 -->
              </div>

              <div id="auditInfoEmpty" class="text-center" style="display: none; padding: 20px 0;">
                <i class="fa fa-info-circle fa-2x" style="color: #999;"></i>
                <p style="margin-top: 10px; color: #777;">暂无查账记录</p>
              </div>

              <div id="auditInfoError" class="text-center" style="display: none; padding: 20px 0;">
                <i class="fa fa-exclamation-triangle fa-2x" style="color: #d9534f;"></i>
                <p style="margin-top: 10px; color: #d9534f;"><span id="auditInfoErrorMessage">加载查账信息失败</span></p>
              </div>

              <!-- 查账与开票按钮 -->
              <div class="text-center" style="margin-top: 15px;">
                <php>if($is_new_transfer) { </php>
                  <!-- 新版本转账信息 - 显示查账与开票按钮 -->
                  <a href="javascript:void(0);" onclick="redirectToAuditPage({$info.id}, '{$info.event}', {$info.pay_type})" class="btn btn-primary" role="button" style="font-weight: 500; padding: 8px 16px; border-radius: 3px;">
                    <i class="fa fa-file-text"></i> 查账与开票
                  </a>
                <php>} elseif($info["pay_type"] == 1) { </php>
                  <!-- 在线支付 - 显示查账与开票按钮 -->
                  <a href="javascript:void(0);" onclick="redirectToAuditPage({$info.id}, '{$info.event}', {$info.pay_type})" class="btn btn-primary" role="button" style="font-weight: 500; padding: 8px 16px; border-radius: 3px;">
                    <i class="fa fa-file-text"></i> 查账与开票
                  </a>
                <php>} else { </php>
                  <!-- 旧版本转账信息 - 不显示任何按钮 -->
                <php>}</php>
              </div>
            </div>
          </div>

          <!-- 发票信息 -->
          <php>if($info["reg_id"] != "") { </php>
            <div class="panel panel-info" style="border-radius: 0; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border-color: #bce8f1; margin-top: 15px;">
              <div class="panel-heading" style="border-radius: 0; background-color: #d9edf7; border-color: #bce8f1;">
                <h3 class="panel-title"><i class="fa fa-file-text-o"></i> 发票信息</h3>
              </div>
              <div class="panel-body" style="padding: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                  <span style="color: #555; font-weight: 500;">发票状态</span>
                  <php>
                  switch($info["invoice_status"]) {
                    case "1":
                      echo '<span class="label label-default" style="font-size: 12px; padding: 4px 8px;">等待开票</span>';
                      break;
                    case "20":
                      echo '<span class="label label-primary" style="font-size: 12px; padding: 4px 8px;">开票成功</span>';
                      break;
                    case "30":
                      echo '<span class="label label-danger" style="font-size: 12px; padding: 4px 8px;">开票失败</span>';
                      break;
                  }
                  </php>
                </div>

                <div style="margin-bottom: 12px;">
                  <div style="display: flex; margin-bottom: 8px;">
                    <span style="color: #555; font-weight: 500; width: 80px;">创建时间</span>
                    <span style="color: #333;">{$info.create_time|date="Y-m-d H:i:s",###}</span>
                  </div>
                  <php>if($info["complete_time"] != "") { </php>
                    <div style="display: flex;">
                      <span style="color: #555; font-weight: 500; width: 80px;">完成时间</span>
                      <span style="color: #333;">{$info.complete_time|date="Y-m-d H:i:s",###}</span>
                    </div>
                  <php>}</php>
                </div>

                <php>if($invoice_info["c_url"] != "") { </php>
                  <div style="margin-top: 15px; text-align: center;">
                    <a href="{$invoice_info.c_url}" target="_blank" class="btn btn-primary" style="border-radius: 3px; padding: 6px 12px;">
                      <i class="fa fa-file-pdf-o"></i> 查看电子发票
                    </a>
                  </div>
                <php>}</php>
              </div>
            </div>
          <php>}</php>

          <!-- 会员信息 -->
          <div class="panel panel-default" style="border-radius: 0; box-shadow: 0 1px 3px rgba(0,0,0,0.1); margin-top: 15px;">
            <div class="panel-heading" style="border-radius: 0; background-color: #f5f5f5; border-bottom: 1px solid #ddd;">
              <h3 class="panel-title"><i class="fa fa-user-circle"></i> 会员信息</h3>
            </div>
            <div class="panel-body" style="padding: 0;">
              <table class="table" style="margin-bottom: 0;">
                <tbody>
                  <tr>
                    <th style="width: 30%; border-top: none; background-color: #f9f9f9;">邮箱</th>
                    <td style="border-top: none;">{$user.email}</td>
                  </tr>
                  <tr>
                    <th style="background-color: #f9f9f9;">姓名</th>
                    <td>{$user.firstname} {$user.lastname}</td>
                  </tr>
                  <tr>
                    <th style="background-color: #f9f9f9;">头衔</th>
                    <td>{$user.title}</td>
                  </tr>
                  <tr>
                    <th style="background-color: #f9f9f9;">注册IP</th>
                    <td><a href="http://ip.chinaz.com/{$user.ip}" target="_blank" style="color: #337ab7;">{$user.ip}</a></td>
                  </tr>
                  <tr>
                    <th style="background-color: #f9f9f9;">注册时间</th>
                    <td>{$user.regtime|date="Y-m-d H:i:s",###}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 修改会议简称和文章ID的模态框 -->
  <div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header" style="background-color: #f0ad4e; color: white;">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="color: white; opacity: 0.8;">
            <span aria-hidden="true">&times;</span>
          </button>
          <h4 class="modal-title" id="editModalLabel"><i class="fa fa-edit"></i> 修改文章ID和会议简称</h4>
        </div>
        <div class="modal-body">
          <div class="alert alert-info" style="font-size: 13px; border-left: 3px solid #5bc0de;">
            <i class="fa fa-info-circle"></i> 修改这些信息将同时更新多个相关表的数据，包括注册信息、转账信息和发票信息。
          </div>
          <form id="editForm">
            <div class="form-group">
              <label for="paper_id"><i class="fa fa-file-text-o"></i> 文章ID</label>
              <input type="text" class="form-control" id="paper_id" name="paper_id" value="{$info.paperid}" placeholder="请输入新的文章ID">
              <p class="help-block"><small>格式：字母、数字、连字符组成，多个ID用逗号分隔</small></p>
            </div>
            <div class="form-group">
              <label for="event"><i class="fa fa-calendar"></i> 会议简称</label>
              <input type="text" class="form-control" id="event" name="event" value="{$info.event}" placeholder="请输入新的会议简称">
              <p class="help-block"><small>格式：字母、数字、连字符加四位年份，多个简称用逗号分隔</small></p>
            </div>
            <input type="hidden" id="reg_id" name="reg_id" value="{$info.id}">
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-warning" id="saveChangesBtn"><i class="fa fa-save"></i> 保存修改</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    /**
     * 显示编辑模态框
     */
    function showEditModal() {
      // 设置当前值
      $('#paper_id').val('{$info.paperid}');
      $('#event').val('{$info.event}');
      $('#reg_id').val('{$info.id}');

      // 显示模态框
      $('#editModal').modal('show');
    }

    /**
     * 查账与开票跳转函数 - 根据支付类型跳转到相应的查账页面
     * @param {number} regId - 注册ID
     * @param {string} event - 会议简称
     * @param {number} payType - 支付类型（1:在线支付, 2:线下转账）
     */
    function redirectToAuditPage(regId, event, payType) {
      // 根据支付类型确定跳转URL
      var url = '';

      if (payType == 1) {
        // 在线支付 - 跳转到 onlineAudit
        url = '{:U("OA/OnlineChain/confirmInfo/")}' + '?reg_id=' + regId;
      } else if (payType == 2) {
        // 线下转账 - 跳转到 offlineAudit
        url = '{:U("OA/OfflineChain/confirmInfo/")}' + '?reg_id=' + regId;
      } else {
        // 未知支付类型
        Swal.fire({
          icon: 'error',
          title: '错误',
          text: '未知的支付类型'
        });
        return;
      }

      // 添加会议简称参数
      if (event) {
        url += '&event=' + encodeURIComponent(event);
      }

      // 跳转到查账页面
      window.location.href = url;
    }

    /**
     * 订单跳转函数 - 根据订单号格式跳转到相应的订单查询页面
     * @param {string} orderId - 订单号
     */
    function redirectToOrderPage(orderId) {
      // 将订单号按 - 分割
      var parts = orderId.split('-');

      if (parts.length >= 2) {
        var secondPart = parts[1];

        if (secondPart === 'AWX') {
          // AWX支付系统订单
          window.location.href = '/awxpay/checkingForm?order_id=' + orderId;
        } else if (/^\d+$/.test(secondPart)) {
          // 标准支付系统订单（第二部分为纯数字）
          window.location.href = '/Pays/inquiry/?order_id=' + orderId;
        } else {
          alert('订单号格式不正确');
        }
      } else {
        alert('订单号格式不正确');
      }
    }

    // 引入SweetAlert2库
    if (typeof Swal === 'undefined') {
      document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">');
      document.write('<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"><\/script>');
    }

    /**
     * 加载查账信息
     */
    function loadAuditInfo() {
      var regId = {$info.id};

      // 显示加载中状态
      $('#auditInfoLoading').show();
      $('#auditInfoContent').hide();
      $('#auditInfoEmpty').hide();
      $('#auditInfoError').hide();

      // 发送AJAX请求获取查账信息
      $.ajax({
        url: '{:U("OA/AuditAccount/queryAuditRecord")}',
        type: 'GET',
        dataType: 'json',
        data: {
          reg_id: regId
        },
        success: function(response) {
          // 隐藏加载中状态
          $('#auditInfoLoading').hide();

          if (response.status && response.data) {
            // 有查账记录，显示查账信息
            var record = response.data;
            var statusInfo = response.status_info;

            // 构建查账信息HTML
            var html = '<div class="table-responsive">' +
                       '<table class="table table-striped table-bordered">' +
                       '<tbody>' +
                       '<tr>' +
                       '<th width="30%">OA订单号</th>' +
                       '<td>' + record.oa_id + '</td>' +
                       '</tr>' +
                       '<tr>' +
                       '<th>提交日期</th>' +
                       '<td>' + formatTimestamp(record.create_time) + '</td>' +
                       '</tr>' +
                       '<tr>' +
                       '<th>查账金额</th>' +
                       '<td><strong class="text-danger">' + formatCurrency(record.total, record.currency) + '</strong></td>' +
                       '</tr>' +
                       '<tr>' +
                       '<th>查账状态</th>' +
                       '<td><span class="label label-' + getStatusClass(record.status) + '">' + statusInfo.name + '</span></td>' +
                       '</tr>';

            // 如果有更新时间，显示更新时间
            if (record.update_time) {
              html += '<tr>' +
                      '<th>更新时间</th>' +
                      '<td>' + formatTimestamp(record.update_time) + '</td>' +
                      '</tr>';
            }

            // 如果有备注，显示备注
            if (record.remark) {
              html += '<tr>' +
                      '<th>备注</th>' +
                      '<td>' + record.remark + '</td>' +
                      '</tr>';
            }

            html += '</tbody></table></div>';

            // 显示查账信息
            $('#auditInfoContent').html(html).show();

            // 根据查账状态调整按钮文本和样式
            updateAuditButton(record.status, statusInfo.name);
          } else {
            // 没有查账记录，显示空状态
            $('#auditInfoEmpty').show();

            // 显示"创建查账"按钮
            updateAuditButton(null, null);
          }
        },
        error: function(xhr, status, error) {
          // 显示错误信息
          $('#auditInfoLoading').hide();
          $('#auditInfoErrorMessage').text('加载查账信息失败: ' + error);
          $('#auditInfoError').show();
        }
      });
    }

    /**
     * 格式化时间戳为日期时间字符串
     * @param {number} timestamp - 时间戳
     * @return {string} 格式化后的日期时间字符串
     */
    function formatTimestamp(timestamp) {
      if (!timestamp) return '';

      var date = new Date(timestamp * 1000);
      return date.getFullYear() + '-' +
             padZero(date.getMonth() + 1) + '-' +
             padZero(date.getDate()) + ' ' +
             padZero(date.getHours()) + ':' +
             padZero(date.getMinutes()) + ':' +
             padZero(date.getSeconds());
    }

    /**
     * 数字补零
     * @param {number} num - 需要补零的数字
     * @return {string} 补零后的字符串
     */
    function padZero(num) {
      return (num < 10 ? '0' : '') + num;
    }

    /**
     * 确认付款函数 - 显示确认弹窗并处理付款确认
     * @param {number} id - 注册ID
     */
    function confirmPayment(id) {
      // 确保SweetAlert2已加载
      if (typeof Swal === 'undefined') {
        // 如果SweetAlert2未加载，先加载它
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css';
        document.head.appendChild(link);

        var script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js';
        script.onload = function() {
          // SweetAlert2加载完成后显示确认弹窗
          showConfirmDialog(id);
        };
        document.head.appendChild(script);
      } else {
        // 如果SweetAlert2已加载，直接显示确认弹窗
        showConfirmDialog(id);
      }
    }

    /**
     * 显示确认弹窗
     * @param {number} id - 注册ID
     */
    function showConfirmDialog(id) {
      Swal.fire({
        title: '确认付款',
        text: '是否已经确认收到了付款？',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then((result) => {
        if (result.isConfirmed) {
          // 用户点击了确认按钮，跳转到确认付款的URL
          window.location.href = '{:U("Index/confirm_pay")}' + '?id=' + id;
        }
      });
    }

    /**
     * 格式化货币
     * @param {number} amount - 金额
     * @param {string} currency - 货币类型
     * @return {string} 格式化后的货币字符串
     */
    function formatCurrency(amount, currency) {
      if (!amount) return '0.00';

      var symbol = currency === 'USD' ? '$' : '¥';
      return symbol + parseFloat(amount).toFixed(2);
    }

    /**
     * 获取状态对应的Bootstrap样式类
     * @param {number} status - 状态码
     * @return {string} Bootstrap样式类
     */
    function getStatusClass(status) {
      // 根据状态码返回对应的Bootstrap样式类
      switch (parseInt(status)) {
        case 0: return 'warning';  // 待提交查账
        case 5: return 'default';  // 草稿状态
        case 10: return 'info';    // 查账提交成功（OA审核中）
        case 20: return 'success'; // 查账成功
        case 30: return 'danger';  // 查账失败
        case 40: return 'warning'; // 查账驳回
        default: return 'default';
      }
    }

    /**
     * 根据查账状态更新查账按钮
     * @param {number|null} status - 查账状态码
     * @param {string|null} statusText - 查账状态文本
     */
    function updateAuditButton(status, statusText) {
      // 获取查账按钮
      var $auditBtn = $('.panel-info #auditInfoPanel .btn-primary');

      if (status === null) {
        // 没有查账记录，显示"创建查账"按钮
        $auditBtn.html('<i class="fa fa-plus-circle"></i> 创建查账记录');
        $auditBtn.removeClass('btn-success btn-info btn-warning btn-danger').addClass('btn-primary');
      } else {
        // 根据状态调整按钮
        switch (parseInt(status)) {
          case 0: // 待提交查账
          case 5: // 草稿状态
            $auditBtn.html('<i class="fa fa-paper-plane"></i> 提交查账');
            $auditBtn.removeClass('btn-primary btn-success btn-info btn-danger').addClass('btn-warning');
            break;
          case 10: // 查账提交成功（OA审核中）
            $auditBtn.html('<i class="fa fa-clock-o"></i> 查看查账状态');
            $auditBtn.removeClass('btn-primary btn-success btn-warning btn-danger').addClass('btn-info');
            break;
          case 20: // 查账成功
            $auditBtn.html('<i class="fa fa-file-text-o"></i> 申请开票');
            $auditBtn.removeClass('btn-primary btn-info btn-warning btn-danger').addClass('btn-success');
            break;
          case 30: // 查账失败
            $auditBtn.html('<i class="fa fa-refresh"></i> 重新查账');
            $auditBtn.removeClass('btn-primary btn-success btn-info btn-warning').addClass('btn-danger');
            break;
          case 40: // 查账驳回
            $auditBtn.html('<i class="fa fa-edit"></i> 修改查账信息');
            $auditBtn.removeClass('btn-primary btn-success btn-info btn-danger').addClass('btn-warning');
            break;
          default:
            $auditBtn.html('<i class="fa fa-file-text"></i> 查账与开票');
            $auditBtn.removeClass('btn-success btn-info btn-warning btn-danger').addClass('btn-primary');
        }
      }
    }

    $(document).ready(function() {
      // 加载查账信息
      loadAuditInfo();
      // 保存修改按钮点击事件
      $('#saveChangesBtn').click(function() {
        // 获取表单数据
        var paperId = $('#paper_id').val();
        var event = $('#event').val();
        var regId = $('#reg_id').val();

        // 表单验证
        if (!paperId) {
          Swal.fire({
            icon: 'error',
            title: '错误',
            text: '文章ID不能为空'
          });
          return;
        }

        if (!event) {
          Swal.fire({
            icon: 'error',
            title: '错误',
            text: '会议简称不能为空'
          });
          return;
        }

        // 确认修改
        Swal.fire({
          title: '确认修改',
          text: '您确定要修改文章ID和会议简称吗？此操作将同时更新多个相关表的数据。',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#f0ad4e',
          cancelButtonColor: '#d33',
          confirmButtonText: '确认修改',
          cancelButtonText: '取消'
        }).then((result) => {
          if (result.isConfirmed) {
            // 显示加载状态
            Swal.fire({
              title: '正在处理',
              text: '请稍候...',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
              }
            });

            // 发送AJAX请求
            $.ajax({
              url: '{:U("OA/BatchUpdate/updateByRegId")}',
              type: 'POST',
              dataType: 'json',
              data: {
                reg_id: regId,
                paper_id: paperId,
                event: event
              },
              success: function(response) {
                if (response.status) {
                  // 更新成功
                  Swal.fire({
                    icon: 'success',
                    title: '更新成功',
                    text: response.message,
                    showConfirmButton: true
                  }).then(() => {
                    // 刷新页面
                    window.location.reload();
                  });
                } else {
                  // 更新失败
                  Swal.fire({
                    icon: 'error',
                    title: '更新失败',
                    text: response.message || '未知错误'
                  });
                }
              },
              error: function(xhr, status, error) {
                // 请求错误
                Swal.fire({
                  icon: 'error',
                  title: '请求错误',
                  text: '发生错误: ' + error
                });
              }
            });
          }
        });
      });
    });
  </script>
</block>
