<extend name="Base/admin_base" />
<block name="title">
  <title>订单列表</title>

</block>

<block name="breadcrumb">
  <ol class="breadcrumb">
    <li><a href="{:U('Index/index')}">首页</a></li>
    <li>订单列表</li>
  </ol>
</block>
<block name="content">

  <div class="panel panel-default">
    <div class="panel-heading">
      <h3 class="panel-title">所有订单</h3>
    </div>
    <div class="panel-body">
      <div class="card">
        <div class="row margin-bottom-15">
          <div class="col-md-2">
            <if condition="($grade eq 1)">
              <a class="btn btn-success" href="{:U('order_list')}" role="button">
                <i class="fa fa-list-ul"></i> 全部支付信息
              </a>
              <a class="btn btn-default" href="{:U('pay_info')}" role="button">
                <i class="fa fa-filter"></i> 按会议查看
              </a>
            </if>
          </div>
          <div class="col-md-10" style="text-align: right">
            <form class="form-inline" action="" method="get">
              <div class="form-group">
                <div class="input-group">
                  <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                  <input type="text" class="form-control" name="event_acronym" placeholder="会议名称">
                </div>
              </div>
              <div class="form-group">
                <div class="input-group">
                  <span class="input-group-addon"><i class="fa fa-hashtag"></i></span>
                  <input type="text" class="form-control" name="orderid" placeholder="订单编号">
                </div>
              </div>
              <div class="form-group">
                <div class="input-group">
                  <span class="input-group-addon"><i class="fa fa-envelope"></i></span>
                  <input type="text" class="form-control" name="email" placeholder="邮箱">
                </div>
              </div>
              <div class="form-group">
                <div class="input-group">
                  <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                  <input type="date" class="form-control" name="btime" value="">
                </div>
              </div>
              <button type="submit" class="btn btn-info">
                <i class="fa fa-search"></i> 搜索
              </button>
            </form>
          </div>
        </div>
        
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead>
              <tr>
                <th><i class="fa fa-university"></i> 会议</th>
                <th><i class="fa fa-barcode"></i> 订单编号</th>
                <th><i class="fa fa-money"></i> 总额</th>
                <th><i class="fa fa-envelope"></i> 邮箱</th>
                <th><i class="fa fa-info-circle"></i> 状态</th>
                <th><i class="fa fa-clock-o"></i> 付款时间</th>
                <th><i class="fa fa-cogs"></i> 操作</th>
              </tr>
            </thead>
            <tbody>
              <volist name="list" id="r">
                <tr>
                  <td>
                    <if condition="$r[cid] eq 0 ">{$r.event_acronym}</if>
                    <if condition="$r[cid] neq 0">
                      <a href="{:U('AdminConfRegister/regInfo',array('cid'=>$r[cid]))}" class="text-primary">
                        <b>{$r.event_acronym}</b>
                      </a>
                    </if>
                  </td>
                  <td>
                    {$r[orderid]}
                    <if condition="$r[cid] neq 0">
                      <a href="{:U('AdminConfRegister/regShow',array('id'=>$r[regid]))}" target="_blank" class="btn btn-xs btn-default">
                        <i class="fa fa-external-link"></i> 详情
                      </a>
                    </if>
                  </td>
                  <td>
                    <strong class="text-danger">{$r.total}</strong>
                    <switch name="r.moneytype">
                      <case value="1"><span class="text-muted">USD</span></case>
                      <case value="0"><span class="text-muted">RMB</span></case>
                    </switch>
                  </td>
                  <td>{$r.email}</td>
                  <td>
                    <switch name="r.status">
                      <case value="1">
                        <span class="label label-default">
                          <i class="fa fa-clock-o"></i> 等待付款
                        </span>
                      </case>
                      <case value="20">
                        <span class="label label-primary">
                          <i class="fa fa-check"></i> 付款成功
                        </span>
                      </case>
                      <case value="30">
                        <span class="label label-danger">
                          <i class="fa fa-times"></i> 付款失败
                        </span>
                      </case>
                    </switch>
                  </td>
                  <td>{$r.ordetime|date="Y/m/d H:i:s",###}</td>
                  <td>
                    <a href="javascript:if(confirm('即将连接到易支付平台进行核对，如遇延时，请耐心等待'))location='{:U('/Pays/inquiry',array('orderid'=>$r['orderid']))}'" 
                       class="btn btn-info btn-sm">
                      <i class="fa fa-refresh"></i> 对账
                    </a>
                  </td>
                </tr>
              </volist>
            </tbody>
          </table>
        </div>
        <div class="paging text-right">{$page}</div>
      </div>
    </div>
    </div>

</block>