<extend name="public:member_base" />


<block name="main">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header pb-0">
                        <h6>Edit Payment Record</h6>
                        <php>
                            $isRejected = \Common\Lib\TransferStatusConstants::isRejected($transfer['status']);
                        </php>
                        <if condition="$isRejected">
                            <div class="alert alert-danger" role="alert">
                                <strong>Rejection Reason:</strong> {$transfer.refusal}
                            </div>
                        </if>
                    </div>
                    <div class="card-body px-0 pt-0 pb-2">
                        <div class="container">
                            <form action="{:U('resubmit')}" method="post" class="needs-validation" novalidate>
                                <input type="hidden" name="id" value="{$transfer.id}">

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="conference" class="form-label">Conference</label>
                                        <input type="text" class="form-control" id="conference"
                                            value="{$transfer.conference_name}" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="paper_id" class="form-label">Paper ID</label>
                                        <input type="text" class="form-control" id="paper_id"
                                            value="{$transfer.paper_id}" readonly>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="currency" class="form-label">Currency</label>
                                        <select class="form-control" id="currency" name="currency">
                                            <option value="0" <if condition="$transfer.currency eq 0">selected</if>>CNY (Chinese Yuan)</option>
                                            <option value="1" <if condition="$transfer.currency eq 1">selected</if>>USD (US Dollar)</option>
                                        </select>
                                        <input type="hidden" name="receiver_id" value="{$transfer.receiver_id}">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="total" class="form-label">Payment Amount</label>
                                        <div class="input-group">
                                            <span class="input-group-text" id="currency-symbol">{$transfer.currency == 1 ? '$' : '¥'}</span>
                                            <input type="number" step="0.01" class="form-control" id="total"
                                                name="total" value="{$transfer.total}" required>
                                            <div class="invalid-feedback">Please enter the payment amount</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="transfer_name" class="form-label">Payer Name</label>
                                        <input type="text" class="form-control" id="transfer_name" name="transfer_name"
                                            value="{$transfer.transfer_name}" required>
                                        <div class="invalid-feedback">Please enter the payer's name</div>
                                        <small class="form-text text-muted">Full name of the person or organization making the payment</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="transfer_account" class="form-label">Payer Account</label>
                                        <input type="text" class="form-control" id="transfer_account"
                                            name="transfer_account" value="{$transfer.transfer_account}" required>
                                        <div class="invalid-feedback">Please enter the payer's account</div>
                                        <small class="form-text text-muted">Bank account number, card number, or digital wallet ID used for the payment</small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="transfer_time" class="form-label">Payment Date</label>
                                        <input type="date" class="form-control" id="transfer_time" name="transfer_time"
                                            value="{:date('Y-m-d', $transfer['transfer_time'])}" required>
                                        <div class="invalid-feedback">Please select the payment date</div>
                                        <small class="form-text text-muted">Date when the payment was initiated or completed</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="transfer_pic" class="form-label">Payment Proof</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="transfer_pic"
                                                name="transfer_pic" value="{$transfer.transfer_pic}" readonly>
                                            <button class="btn btn-outline-secondary" type="button"
                                                id="upload-btn">Upload</button>
                                        </div>
                                        <div class="mt-2" id="proof-preview">
                                            <if condition="$transfer.transfer_pic neq ''">
                                                <a href="<?php echo ATTACHMENT_URL; ?>{$transfer.transfer_pic}" target="_blank" class="proof-link">
                                                    <img src="<?php echo ATTACHMENT_URL; ?>{$transfer.transfer_pic}" class="img-thumbnail"
                                                        style="max-height: 150px;">
                                                </a>
                                            </if>
                                        </div>
                                        <small class="form-text text-muted">Upload a screenshot or photo of your bank transfer, payment confirmation, or transaction receipt</small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-12">
                                        <label for="remark" class="form-label">Note (Optional)</label>
                                        <textarea class="form-control" id="remark" name="remark"
                                            rows="3" placeholder="Any additional information about your payment">{$transfer.remark}</textarea>
                                        <small class="form-text text-muted">Include transaction reference numbers, special circumstances, or any other relevant details</small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Resubmit Payment</button>
                                        <a href="{:U('index')}" class="btn btn-secondary">Back to List</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</block>

<block name="script">    <script>
        $(document).ready(function () {
            // 币种选择变化时更新符号
            $('#currency').change(function() {
                var currency = $(this).val();
                var symbol = currency == '1' ? '$' : '¥';
                $('#currency-symbol').text(symbol);
            });
            // 确保SweetAlert2可用
            if (typeof Swal === 'undefined') {
                console.warn('SweetAlert2 is not loaded. Using browser alerts instead.');
                window.Swal = {
                    fire: function(options) {
                        alert(options.title + '\n\n' + options.text);
                        return { then: function(callback) { callback(); } };
                    }
                };
            }
            
            // 表单验证
            (function () {
                'use strict';
                window.addEventListener('load', function () {
                    var forms = document.getElementsByClassName('needs-validation');
                    var validation = Array.prototype.filter.call(forms, function (form) {
                        form.addEventListener('submit', function (event) {
                            if (form.checkValidity() === false) {
                                event.preventDefault();
                                event.stopPropagation();
                            }
                            form.classList.add('was-validated');
                        }, false);
                    });
                }, false);
            })();

            // 上传凭证
            $('#upload-btn').click(function () {
                // 创建文件上传输入
                var fileInput = $('<input type="file" accept="image/*" style="display:none">');
                $('body').append(fileInput);

                // 监听文件选择
                fileInput.change(function () {
                    if (this.files && this.files[0]) {
                        var formData = new FormData();
                        formData.append('file', this.files[0]);
                        // 添加场景和关联类型参数，用于记录日志
                        formData.append('scene', 'register');
                        formData.append('related_type', 'transfer_proof');
                        // 获取转账记录ID
                        var transferId = $('input[name="id"]').val();
                        // 通过AJAX获取关联的注册ID
                        $.ajax({
                            url: "{:U('getRegIdByTransferId')}",
                            type: 'GET',
                            data: { id: transferId },
                            async: false,
                            success: function(response) {
                                if (response.status === 1 && response.data) {
                                    formData.append('related_id', response.data);
                                }
                            }
                        });

                        $.ajax({
                            url: "{:U('/uploadOSS/uploadTransferProof')}",
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function (response) {
                                if (response.status === 'success') {
                                    $('#transfer_pic').val(response.url);
                                    $('#proof-preview').empty();
                                    
                                    // 创建链接和图片
                                    var fullUrl = "<?php echo ATTACHMENT_URL?>" + response.url;
                                    var $link = $('<a>')
                                        .attr('href', fullUrl)
                                        .attr('target', '_blank')
                                        .addClass('proof-link');
                                        
                                    var $img = $('<img>')
                                        .attr('src', fullUrl)
                                        .addClass('img-thumbnail')
                                        .css('max-height', '150px');
                                        
                                    $link.append($img);
                                    $('#proof-preview').append($link);
                                    
                                    // 显示成功提示
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Upload Successful',
                                        text: 'Your payment proof has been uploaded successfully',
                                        confirmButtonText: 'OK'
                                    });
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Upload Failed',
                                        text: response.info || 'Failed to upload image',
                                        confirmButtonText: 'OK'
                                    });
                                }
                            },
                            error: function () {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Upload Failed',
                                    text: 'Network error. Please try again.',
                                    confirmButtonText: 'OK'
                                });
                            }
                        });
                    }

                    // 移除文件输入
                    fileInput.remove();
                });

                // 触发文件选择
                fileInput.click();
            });
        });
    </script>
</block>