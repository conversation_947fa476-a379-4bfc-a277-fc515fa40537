<?php

namespace OA\Controller;

use Think\Controller;
use OA\Common\ResponseHelper;
use OA\Common\StatusConstants;

/**
 * OA模块基础控制器
 * 所有OA模块的控制器都应继承此控制器
 */
class BaseController extends Controller
{
    /**
     * 初始化方法
     * 子类可以重写此方法进行自定义初始化
     */
    protected function _initialize()
    {
        // 检查环境配置
        if (empty(C('OA_ENVIRONMENT'))) {
            \Think\Log::write('OA环境配置缺失，请检查配置文件', 'ERROR');
        }

        // 检查用户是否已登录
        $this->isLogin = !empty(session('userid'));

        $this->remittanceService = new \OA\Service\RemittanceService();
        $this->OrderNumservice = new \OA\Service\OrderNumservice();
        $this->invoiceService = new \OA\Service\InvoiceService();
        $this->userbindingService = new \OA\Service\UserbindingService();
        $this->oaRemittanceDataService = new \OA\Service\OaRemittanceDataService();
        $this->oaAuditAccountService = new \OA\Service\OaAuditAccountService();
        $this->checkOaService = new \OA\Service\CheckOaservice();
        $this->buildOaDataService = new \OA\Service\BuildOaDataService();
        //发送到OA服务类
        $this->sendAuditToOAService = new \OA\Service\SendAuditToOAService();
        //发票数据构造服务类
        $this->BuildInvoiceDataService = new \OA\Service\BuildInvoiceDataService();
        //获取用户的OA信息，并初始化，供其他继承的控制器使用
        $this->initUserOaInfo();
    }

    /**
     * 检查注册信息是否存在且已付款
     *
     * @param int $regId 注册ID
     * @return array 注册信息数组，如果检查通过
     * @throws \Common\Exception\LocalOperationException 如果注册信息不存在或未付款
     */
    protected function checkRegisterPayment($regId)
    {
        if (empty($regId)) {
            throw new \Common\Exception\LocalOperationException('参数缺失，注册ID编号号不能为空');
        }

        // 查询注册信息
        $registerModel = M('SubRegister');
        $registerInfo = $registerModel->where(['id' => $regId])->find();

        // 检查注册信息是否存在
        if (!$registerInfo) {
            throw new \Common\Exception\LocalOperationException('没有找到注册信息');
        }

        // 检查是否已付款
        if ($registerInfo['pay_type'] == 0) {
            throw new \Common\Exception\LocalOperationException('该注册信息尚未支付，无法进行查账');
        }

        return $registerInfo;
    }
    // tp3.2 检查是否为游客付款模式( 付款成功，但是无关联的注册信息)
    protected function checkGuestPayment($payId, $post)
    {

        if (empty($payId)) {
            throw new \Common\Exception\LocalOperationException('参数缺失，支付ID编号不能为空');
        }

        // 查询支付信息
        $payModel = D('Pay');
        $payInfo = $payModel->relation(true)->where(['id' => $payId])->find();



        // 检查支付信息是否存在
        if (!$payInfo) {
            throw new \Common\Exception\LocalOperationException('没有找到支付信息');
        }

        // 检查支付状态是否为20（代表付款成功）
        if ($payInfo['status'] != 20) {
            throw new \Common\Exception\LocalOperationException('支付状态不正确，无法进行游客付款模式检查');
        }

        // 检查regid是否为空，为空才是游客付款模式
        if (empty($payInfo['regid'])) {
            return $payInfo; // 返回支付信息记录
        }

        throw new \Common\Exception\LocalOperationException('该支付信息已关联注册信息，不属于游客付款模式');
    }

    /**
     * 初始化用户OA信息
     * 获取当前登录用户的OA绑定信息，并将其存储在控制器属性中
     * 所有继承自BaseController的控制器都可以通过$this->userOaInfo访问这些信息
     */
    protected function initUserOaInfo()
    {
        try {
            // 记录开始初始化的日志
            \Think\Log::write("开始初始化用户OA信息", 'DEBUG');

            // 默认初始化用户OA信息为空
            $this->userOaInfo = null;
            $this->isOaBound = false;

            // 获取当前登录用户ID
            $userId = session('userid');
            \Think\Log::write("获取到的用户ID: " . (empty($userId) ? '空' : $userId), 'DEBUG');

            if (empty($userId)) {
                \Think\Log::write("用户未登录，终止OA信息初始化", 'DEBUG');
                return; // 用户未登录，不进行后续处理
            }

            // 查询用户信息
            $adminModel = D('Admin');
            \Think\Log::write("开始查询用户信息，用户ID: {$userId}", 'DEBUG');

            $user = $adminModel->where(array('id' => $userId))->find();

            // 记录查询结果
            if (empty($user)) {
                \Think\Log::write("未找到用户信息，用户ID: {$userId}", 'WARN');
            } else {
                // 记录用户基本信息，但不包含敏感数据
                \Think\Log::write("用户信息查询结果: ID={$user['id']}, 用户名={$user['username']}, " .
                    "工号=" . (empty($user['work_id']) ? '空' : $user['work_id']) . ", " .
                    "OA信息=" . (empty($user['oa_info']) ? '空' : '非空(长度:' . strlen($user['oa_info']) . ')'), 'DEBUG');
            }

            // 检查用户是否已绑定OA信息
            $hasWorkId = !empty($user) && !empty($user['work_id']);
            $hasOaInfo = !empty($user) && !empty($user['oa_info']);

            \Think\Log::write("绑定检查: 用户存在=" . (!empty($user) ? '是' : '否') .
                ", 工号存在=" . ($hasWorkId ? '是' : '否') .
                ", OA信息存在=" . ($hasOaInfo ? '是' : '否'), 'DEBUG');

            if ($hasWorkId && $hasOaInfo) {
                $this->isOaBound = true;
                \Think\Log::write("用户已绑定OA信息，工号: {$user['work_id']}", 'DEBUG');

                // 解析OA信息JSON
                \Think\Log::write("开始解析OA信息JSON: " . substr($user['oa_info'], 0, 100) .
                    (strlen($user['oa_info']) > 100 ? '...(已截断)' : ''), 'DEBUG');

                $oaInfo = json_decode($user['oa_info'], true);

                if ($oaInfo === null) {
                    \Think\Log::write("OA信息JSON解析失败，JSON错误: " . json_last_error_msg(), 'ERROR');
                    // 尝试修复可能的JSON格式问题
                    $fixedJson = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $user['oa_info']);
                    \Think\Log::write("尝试修复JSON: " . substr($fixedJson, 0, 100), 'DEBUG');
                    $oaInfo = json_decode($fixedJson, true);

                    if ($oaInfo === null) {
                        \Think\Log::write("修复后的JSON仍然解析失败", 'ERROR');
                        // 最后尝试使用addslashes处理
                        $escapedJson = addslashes($user['oa_info']);
                        $oaInfo = json_decode($escapedJson, true);
                    }
                }

                if ($oaInfo) {
                    \Think\Log::write("OA信息JSON解析成功: " . json_encode($oaInfo, JSON_UNESCAPED_UNICODE), 'DEBUG');

                    // 记录原始字段
                    \Think\Log::write("原始OA信息字段: " . implode(", ", array_keys($oaInfo)), 'DEBUG');

                    // 标准化字段名称，确保后续处理使用统一的字段名
                    $this->userOaInfo = array(
                        'name' => isset($oaInfo['name']) ? $oaInfo['name'] : $user['fullname'],
                        'workId' => isset($oaInfo['workId']) ? $oaInfo['workId'] : $user['work_id'],
                        'deptName' => isset($oaInfo['deptName']) ? $oaInfo['deptName'] : '',
                        'deptId' => isset($oaInfo['deptId']) ? $oaInfo['deptId'] : '',
                        'userId' => isset($oaInfo['userId']) ? $oaInfo['userId'] : $userId
                    );

                    // 记录标准化后的OA信息
                    \Think\Log::write("标准化后的用户OA信息: " . json_encode($this->userOaInfo, JSON_UNESCAPED_UNICODE), 'DEBUG');
                } else {
                    \Think\Log::write("OA信息JSON解析失败，但用户有work_id，使用基本信息", 'WARN');
                    // 即使JSON解析失败，也尝试使用基本信息
                    $this->userOaInfo = array(
                        'name' => $user['fullname'],
                        'workId' => $user['work_id'],
                        'deptName' => '',
                        'deptId' => '',
                        'userId' => $userId
                    );
                }

                // 将OA信息传递给视图，方便在视图中使用
                $this->assign('userOaInfo', $this->userOaInfo);
                $this->assign('isOaBound', $this->isOaBound);
                \Think\Log::write("OA信息已分配给视图变量", 'DEBUG');
            } else {
                \Think\Log::write("用户未绑定OA信息，缺少工号或OA信息", 'WARN');
                if (!empty($user)) {
                    if (empty($user['work_id'])) {
                        \Think\Log::write("缺少工号信息", 'WARN');
                    }
                    if (empty($user['oa_info'])) {
                        \Think\Log::write("缺少OA信息", 'WARN');
                    }
                }
            }

            // 记录最终状态
            \Think\Log::write("用户ID:{$userId} 最终OA绑定状态:{$this->isOaBound}", 'DEBUG');
        } catch (\Exception $e) {
            // 记录详细的异常信息
            \Think\Log::write("初始化用户OA信息异常: " . $e->getMessage() . "\n" .
                "文件: " . $e->getFile() . "\n" .
                "行号: " . $e->getLine() . "\n" .
                "堆栈: " . $e->getTraceAsString(), 'ERROR');
        }
    }

    /**
     * 返回成功响应
     * @param mixed $data 响应数据
     * @param string $message 成功消息
     * @param int $code 状态码
     * @param string $source 响应来源
     */
    protected function responseSuccess($data = null, $message = '操作成功', $code = 200, $source = 'local')
    {
        $response = ResponseHelper::success($data, $message, $code, $source);
        $this->ajaxReturn($response);
    }

    /**
     * 返回错误响应
     * @param string $message 错误消息
     * @param int $code 状态码
     * @param mixed $data 响应数据
     * @param string $source 响应来源
     */
    protected function responseError($message = '操作失败', $code = 400, $data = null, $source = 'local')
    {
        $response = ResponseHelper::error($message, $code, $data, $source);
        $this->ajaxReturn($response);
    }

    /**
     * 返回表单验证错误响应
     * @param array $errors 表单验证错误
     * @param string $message 错误消息
     */
    protected function responseValidationError($errors, $message = '表单验证失败')
    {
        $response = ResponseHelper::error($message, C('OA_RESPONSE_CODE.VALIDATION_ERROR'), null, $errors);
        $this->ajaxReturn($response);
    }

    /**
     * 返回OA API响应
     * @param array $oaResponse OA API响应
     * @param string $successMessage 成功消息
     * @param string $errorMessage 错误消息
     */
    protected function responseOaApi($oaResponse, $successMessage = null, $errorMessage = null)
    {
        $response = ResponseHelper::convertOaResponse($oaResponse, $successMessage, $errorMessage);
        $this->ajaxReturn($response);
    }

    /**
     * 执行控制器方法并捕获异常
     * @param string $method 方法名
     * @return mixed
     */
    public function executeActionWithTryCatch($method)
    {
        try {
            return $this->$method();
        } catch (\Common\Exception\RemoteApiException $e) {
            // 处理远程API异常
            return $this->ajaxReturn([
                'status' => false,
                'message' => '远程服务处理失败：' . $e->getMessage(),
                'error_source' => 'remote',
                'error_code' => $e->getCode()
            ]);
        } catch (\Common\Exception\LocalOperationException $e) {
            // 处理本地操作异常
            return $this->ajaxReturn([
                'status' => false,
                'message' => '本地操作失败：' . $e->getMessage(),
                'error_source' => 'local',
                'error_code' => $e->getCode()
            ]);
        } catch (\Exception $e) {
            // 处理其他所有异常
            return $this->ajaxReturn([
                'status' => false,
                'message' => '系统错误：' . $e->getMessage(),
                'error_source' => 'system',
                'error_code' => $e->getCode()
            ]);
        }
    }
}
