<?php

namespace Home\Service;

use Think\Controller;
use Common\Lib\TransferStatusConstants as Status;

/**
 * 会员转账服务类
 * 处理会员转账相关的业务逻辑
 */
class MemberTransferService extends Controller
{
    /**
     * 检查注册记录是否已有转账记录
     * @param int $regId 注册ID
     * @param int $status 状态码，默认为空，表示查询所有状态
     * @return bool 如果已有转账记录返回true，否则返回false
     */
    public function hasTransferRecord($regId, $status = null)
    {
        if (empty($regId)) {
            return false;
        }

        $transferModel = D('SubTransfer');
        $condition = ['reg_id' => $regId];

        // 如果指定了状态，则添加状态条件
        if ($status !== null) {
            $condition['status'] = $status;
        }

        $count = $transferModel->where($condition)->count();
        return $count > 0;
    }

    /**
     * 通过注册ID获取银行信息
     * @param int $regId 注册ID
     * @param int $userId 用户ID，用于验证权限
     * @return array 包含银行信息的数组，如果出错则返回错误信息
     */
    public function getBankInfoByRegId($regId, $userId)
    {
        // 验证参数
        if (empty($regId)) {
            return ['status' => 0, 'message' => '参数错误，注册ID不能为空'];
        }

        // 获取注册记录并验证是否属于当前用户
        $registerModel = D('SubRegister');
        $register = $registerModel->where(['id' => $regId, 'userid' => $userId])->find();

        if (!$register) {
            return ['status' => 0, 'message' => '注册记录不存在或不属于当前用户'];
        }

        // 从注册记录中获取会议ID
        $cid = $register['cid'];

        // 获取会议信息
        $conferenceModel = D('SubConference');
        $conference = $conferenceModel->where(['id' => $cid])->find();

        if (!$conference) {
            return ['status' => 0, 'message' => '会议信息不存在'];
        }

        // 检查会议是否设置了收款账户
        if (empty($conference['bank_cn']) && empty($conference['bank_en'])) {
            return ['status' => 0, 'message' => '该会议暂未设置收款账户，请联系管理员'];
        }

        // 获取会议对应的收款账户信息
        $bankModel = D('SubBank');
        $bankCn = '';
        $bankEn = '';

        // 安全地获取银行信息，并进行存在性检查
        if (!empty($conference['bank_cn'])) {
            $bankCnInfo = $bankModel->find(intval($conference['bank_cn']));
            if ($bankCnInfo) {
                // 处理特殊字符
                if (isset($bankCnInfo['bank'])) {
                    $bankCnInfo['bank_info'] = str_replace(array('{br}'), array('<br>'), $bankCnInfo['bank']);
                }
                $bankCn = $bankCnInfo;
            }
        }

        if (!empty($conference['bank_en'])) {
            $bankEnInfo = $bankModel->find(intval($conference['bank_en']));
            if ($bankEnInfo) {
                // 处理特殊字符
                if (isset($bankEnInfo['bank'])) {
                    $bankEnInfo['bank_info'] = str_replace(array('{br}'), array('<br>'), $bankEnInfo['bank']);
                }
                $bankEn = $bankEnInfo;
            }
        }

        // 如果两种账户都不可用，提示错误
        if (empty($bankCn) && empty($bankEn)) {
            return ['status' => 0, 'message' => '该会议的收款账户信息无效，请联系管理员'];
        }

        // 返回成功结果
        return [
            'status' => 1,
            'data' => [
                'register' => $register,
                'conference' => $conference,
                'bank_cn' => $bankCn,
                'bank_en' => $bankEn,
                'cid' => $cid,
                'reg_id' => $regId
            ]
        ];
    }

    /**
     * 根据注册ID和币种获取收款账户ID
     * @param int $regId 注册ID
     * @param int $userId 用户ID，用于验证权限
     * @param int $currency 币种：0-CNY，1-USD
     * @return array 包含收款账户ID的数组，如果出错则返回错误信息
     */
    public function getReceiverIdByRegId($regId, $userId, $currency)
    {
        // 获取银行信息
        $result = $this->getBankInfoByRegId($regId, $userId);

        if ($result['status'] == 0) {
            return $result; // 返回错误信息
        }

        $conference = $result['data']['conference'];

        // 根据币种选择相应的收款账户
        if ($currency == 1) { // 美元
            if (empty($conference['bank_en'])) {
                return ['status' => 0, 'message' => '该会议暂未设置美元收款账户，请联系管理员'];
            }
            $receiverId = $conference['bank_en'];
        } else { // 人民币
            if (empty($conference['bank_cn'])) {
                return ['status' => 0, 'message' => '该会议暂未设置人民币收款账户，请联系管理员'];
            }
            $receiverId = $conference['bank_cn'];
        }

        // 验证收款账户是否有效
        $bankModel = D('SubBank');
        $bankInfo = $bankModel->find(intval($receiverId));

        if (!$bankInfo) {
            return ['status' => 0, 'message' => '收款账户信息无效，请联系管理员'];
        }

        // 处理特殊字符
        if (isset($bankInfo['bank'])) {
            $bankInfo['bank_info'] = str_replace(array('{br}'), array('<br>'), $bankInfo['bank']);
        }

        // 返回成功结果
        return [
            'status' => 1,
            'data' => [
                'receiver_id' => $receiverId,
                'bank_info' => $bankInfo
            ]
        ];
    }
    /**
     * 创建转账记录
     * @param array $data 转账数据
     * @return array 包含状态和消息的数组
     */
    public function createTransfer($data)
    {
        // 验证数据
        if (empty($data['cid'])) {
            return ['status' => 0, 'message' => 'Conference ID cannot be empty'];
        }

        if (empty($data['reg_id'])) {
            return ['status' => 0, 'message' => 'Registration ID cannot be empty'];
        }

        if (empty($data['transfer_name'])) {
            return ['status' => 0, 'message' => 'Transferor name cannot be empty'];
        }

        if (empty($data['transfer_account'])) {
            return ['status' => 0, 'message' => 'Transfer account cannot be empty'];
        }

        if (empty($data['total']) || !is_numeric($data['total']) || floatval($data['total']) <= 0) {
            return ['status' => 0, 'message' => 'Transfer amount must be a positive number'];
        }

        if (empty($data['transfer_time'])) {
            return ['status' => 0, 'message' => 'Transfer time cannot be empty'];
        }

        if (empty($data['transfer_pic'])) {
            return ['status' => 0, 'message' => 'Please upload a transfer receipt'];
        }

        // 检查用户ID是否已设置
        if (empty($data['user_id'])) {
            $data['user_id'] = str_auth(cookie('userid'), 'DECODE');
        }

        // 检查默认值是否已设置
        if (!isset($data['status'])) {
            $data['status'] = 0; // 待审核
        }
        if (!isset($data['create_time'])) {
            $data['create_time'] = time();
        }
        if (!isset($data['update_time'])) {
            $data['update_time'] = time();
        }

        // 创建转账记录
        $transferModel = D('SubTransfer');

        // 生成订单号
        if (empty($data['order_id'])) {
            // 币种标识：C=CNY, U=USD
            $currencyCode = (isset($data['currency']) && $data['currency'] == 1) ? 'U' : 'C';
            $data['order_id'] = 'TR' . $currencyCode . date('YmdHis') . rand(100000, 999999);
        }

        // 调试信息
        $this->logTransferData($data);

        // 直接添加数据
        $result = $transferModel->add($data);
        if ($result) {
            // 记录成功日志
            $this->logTransferResult(true, $result);

            // 使用状态同步服务更新注册表的支付状态
            $statusSyncService = new \Common\Service\StatusSyncService();
            $syncResult = $statusSyncService->syncTransferToPaymentStatus($result, \Common\Lib\TransferStatusConstants::TRANSFER_PENDING);

            // 记录同步结果
            $logFile = './Uploads/transfer_debug.log';
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 状态同步结果: ' . json_encode($syncResult) . "\n", FILE_APPEND);

            return array('status' => 1, 'message' => 'Transfer record submitted successfully. Please wait for review.', 'data' => array('id' => $result));
        } else {
            // 记录失败日志
            $error = $transferModel->getError() ?: '未知错误';
            $this->logTransferResult(false, $error);
            return array('status' => 0, 'message' => '转账记录提交失败：' . $error);
        }
    }

    /**
     * 获取用户的转账记录
     * @param int $userId 用户ID
     * @param array $condition 附加条件
     * @return array 转账记录列表
     */
    public function getUserTransfers($userId, $condition = [])
    {
        $transferModel = D('SubTransfer');

        // 直接查询数据库，而不是调用模型中的方法
        $map = array('user_id' => $userId);
        if (!empty($condition)) {
            $map = array_merge($map, $condition);
        }
        $transfers = $transferModel->where($map)->order('id desc')->select();

        // 处理转账记录，添加会议名称、注册信息等
        if (!empty($transfers)) {
            $conferenceModel = D('SubConference');
            $registerModel = D('SubRegister');

            foreach ($transfers as &$transfer) {
                // 获取会议信息
                $conference = $conferenceModel->where(['id' => $transfer['cid']])->find();
                $transfer['conference_name'] = $conference ? $conference['title'] : '未知会议';
                $transfer['event'] = $conference ? $conference['event'] : '';

                // 获取注册信息
                $register = $registerModel->where(['id' => $transfer['reg_id']])->find();
                $transfer['register_info'] = $register ? $register['firstname'] . ' ' . $register['lastname'] : '未知注册';

                // 格式化时间
                $transfer['transfer_time_format'] = date('Y-m-d H:i:s', intval($transfer['transfer_time']));
                $transfer['create_time_format'] = date('Y-m-d H:i:s', intval($transfer['create_time']));

                // 格式化状态
                // 强制将状态转换为整数
                $transfer['status'] = intval($transfer['status']);

                // 直接使用状态常量类的方法获取状态文本和样式类
                $transfer['status_text'] = Status::getStatusText($transfer['status']);
                $transfer['status_class'] = Status::getStatusClass($transfer['status']);

                // 格式化币种
                $transfer['currency_text'] = $transfer['currency'] == 1 ? 'USD' : 'CNY';
            }
        }

        return $transfers;
    }

    /**
     * 获取转账记录详情
     * @param int $id 转账记录ID
     * @param int $userId 用户ID，用于验证权限
     * @return array|bool 转账记录详情或false
     */
    public function getTransferDetail($id, $userId)
    {
        $transferModel = D('SubTransfer');
        // 直接查询数据库
        $transfer = $transferModel->where(array('id' => $id))->find();

        // 验证记录是否存在且属于当前用户
        if (!$transfer || $transfer['user_id'] != $userId) {
            return false;
        }

        // 处理转账记录，添加会议名称、注册信息等
        $conferenceModel = D('SubConference');
        $registerModel = D('SubRegister');

        // 获取会议信息
        $conference = $conferenceModel->where(['id' => $transfer['cid']])->find();
        $transfer['conference_name'] = $conference ? $conference['title'] : '未知会议';
        $transfer['event'] = $conference ? $conference['event'] : '';

        // 获取注册信息
        $register = $registerModel->where(['id' => $transfer['reg_id']])->find();
        $transfer['register_info'] = $register ? $register['firstname'] . ' ' . $register['lastname'] : '未知注册';

        // 获取收款账户信息
        if (!empty($transfer['receiver_id'])) {
            $accountModel = D('ConfBankAccount');
            $account = $accountModel->find($transfer['receiver_id']);
            if ($account) {
                $transfer['receiver_name'] = $account['bank_name'] . ' - ' . $account['account_name'] . ' - ' . $account['account_number'];
            } else {
                $transfer['receiver_name'] = '未知账户';
            }
        } else {
            $transfer['receiver_name'] = '未选择账户';
        }

        // 格式化时间
        $transfer['transfer_time_format'] = date('Y-m-d H:i:s', intval($transfer['transfer_time']));
        $transfer['create_time_format'] = date('Y-m-d H:i:s', intval($transfer['create_time']));
        $transfer['update_time_format'] = date('Y-m-d H:i:s', intval($transfer['update_time']));

        // 格式化状态
        // 强制将状态转换为整数
        $transfer['status'] = intval($transfer['status']);

        // 直接使用状态常量类的方法获取状态文本和样式类
        $transfer['status_text'] = Status::getStatusText($transfer['status']);
        $transfer['status_class'] = Status::getStatusClass($transfer['status']);

        // 格式化币种
        $transfer['currency_text'] = $transfer['currency'] == 1 ? 'USD' : 'CNY';

        return $transfer;
    }

    /**
     * 取消转账记录（仅限待审核状态）
     * @param int $id 转账记录ID
     * @param int $userId 用户ID，用于验证权限
     * @return array 包含状态和消息的数组
     */
    public function cancelTransfer($id, $userId)
    {
        // 记录日志
        $logFile = './Uploads/transfer_debug.log';
        file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 开始取消转账记录，ID: ' . $id . ', 用户ID: ' . $userId . "\n", FILE_APPEND);

        $transferModel = D('SubTransfer');
        // 直接查询数据库
        $transfer = $transferModel->where(array('id' => $id))->find();

        // 记录查询结果
        file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 查询结果: ' . json_encode($transfer) . "\n", FILE_APPEND);

        // 验证记录是否存在且属于当前用户
        if (!$transfer) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 转账记录不存在' . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'Transfer record does not exist'];
        }

        if ($transfer['user_id'] != $userId) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 您无权操作此转账记录, 记录用户ID: ' . $transfer['user_id'] . ', 当前用户ID: ' . $userId . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'You do not have permission to operate this transfer record'];
        }

        // 验证状态是否可取消
        if (!Status::isDeletable($transfer['status'])) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 只能取消待审核或审核未通过的转账记录, 当前状态: ' . $transfer['status'] . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'Only pending or rejected transfer records can be cancelled'];
        }

        // 更新状态为取消
        $data = [
            'status' => Status::TRANSFER_FAILED, // 转账失败
            'remark' => 'User cancelled',
            'update_time' => time()
        ];

        file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 准备更新数据: ' . json_encode($data) . "\n", FILE_APPEND);

        $result = $transferModel->where(array('id' => $id))->save($data);

        file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 更新结果: ' . ($result ? '成功' : '失败') . "\n", FILE_APPEND);

        if ($result) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 成功: 转账记录已取消' . "\n", FILE_APPEND);
            return ['status' => 1, 'message' => 'Transfer record has been cancelled'];
        } else {
            $error = $transferModel->getError() ?: 'Unknown error';
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 取消失败: ' . $error . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'Cancellation failed: ' . $error];
        }
    }

    /**
     * 重新提交转账
     * @param int $id 转账记录ID
     * @param int $userId 用户ID
     * @param array $data 更新的数据
     * @return array 操作结果
     */
    public function resubmitTransfer($id, $userId, $data) {
        // 创建日志文件
        $logFile = LOG_PATH . 'transfer_resubmit_' . date('Ymd') . '.log';
        file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 开始重新提交转账记录, ID: ' . $id . ', 用户ID: ' . $userId . "\n", FILE_APPEND);

        // 获取转账记录
        $transferModel = D('SubTransfer');
        $transfer = $transferModel->find($id);

        // 检查转账记录是否存在
        if (!$transfer) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 转账记录不存在, ID: ' . $id . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'Transfer record does not exist'];
        }

        // 检查转账记录是否属于当前用户
        if ($transfer['user_id'] != $userId) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 转账记录不属于当前用户, 记录用户ID: ' . $transfer['user_id'] . ', 当前用户ID: ' . $userId . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'You can only resubmit your own transfer records'];
        }

        // 强制将状态转换为整数
        $transfer['status'] = intval($transfer['status']);

        // 检查转账记录状态是否可编辑
        if (!Status::isEditable($transfer['status'])) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 错误: 只能重新提交待审核或审核未通过的转账记录, 当前状态: ' . $transfer['status'] . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'Only pending or rejected transfer records can be resubmitted'];
        }

        // 准备更新数据
        $updateData = [
            'status' => Status::TRANSFER_PENDING, // 重置为待审核状态
            'update_time' => time()
        ];

        // 合并用户提交的数据
        if (!empty($data['transfer_name'])) {
            $updateData['transfer_name'] = $data['transfer_name'];
        }

        if (!empty($data['transfer_account'])) {
            $updateData['transfer_account'] = $data['transfer_account'];
        }

        if (!empty($data['total'])) {
            $updateData['total'] = $data['total'];
        }

        if (!empty($data['transfer_time'])) {
            $updateData['transfer_time'] = $data['transfer_time'];
        }

        if (!empty($data['transfer_pic'])) {
            $updateData['transfer_pic'] = $data['transfer_pic'];
        }

        if (!empty($data['remark'])) {
            $updateData['remark'] = $data['remark'];
        }

        file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 准备更新数据: ' . json_encode($updateData) . "\n", FILE_APPEND);

        $result = $transferModel->where(array('id' => $id))->save($updateData);

        if ($result) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 重新提交成功, ID: ' . $id . "\n", FILE_APPEND);
            return ['status' => 1, 'message' => 'Transfer resubmitted successfully'];
        } else {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 重新提交失败, ID: ' . $id . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'Failed to resubmit transfer'];
        }
    }

    /**
     * 上传转账凭证
     *
     * 注意：此方法已弃用，请使用 UploadOSSController::uploadTransferProof 方法
     * 保留此方法是为了向后兼容
     *
     * @param array $file 上传的文件信息
     * @return array 包含状态和消息的数组
     * @deprecated 使用 UploadOSSController::uploadTransferProof 替代
     */
    public function uploadTransferProof($file)
    {
        // 验证文件是否存在
        if (empty($file) || $file['error'] != 0) {
            return ['status' => 0, 'message' => 'File upload failed'];
        }

        // 设置上传参数
        $upload = new \Think\Upload();
        $upload->maxSize = 5 * 1024 * 1024; // 5MB
        $upload->exts = array('jpg', 'jpeg', 'png', 'gif');
        $upload->rootPath = './Uploads/';
        $upload->savePath = 'transfer/';

        // 上传文件
        $info = $upload->uploadOne($file);

        if ($info) {
            $filePath = $info['savepath'] . $info['savename'];

            // 记录日志
            \Think\Log::write('使用旧方法上传转账凭证: ' . $filePath, 'WARN');

            return ['status' => 1, 'message' => 'File uploaded successfully', 'data' => ['path' => $filePath]];
        } else {
            return ['status' => 0, 'message' => 'File upload failed: ' . $upload->getError()];
        }
    }

    /**
     * 从表单数据创建转账记录
     * @param int $regId 注册ID
     * @param int $userId 用户ID
     * @param array $postData 表单提交的数据
     * @return array 包含状态和消息的数组
     */
    public function createTransferFromPost($regId, $userId, $postData)
    {
        // 检查注册记录是否已有转账记录
        if ($this->hasTransferRecord($regId)) {
            return ['status' => 0, 'message' => 'This registration record already has a transfer record, cannot submit again'];
        }

        // 获取用户选择的币种
        $currency = isset($postData['currency']) ? intval($postData['currency']) : 0;

        // 通过服务类获取收款账户ID
        $result = $this->getReceiverIdByRegId($regId, $userId, $currency);

        // 如果获取失败，返回错误信息
        if ($result['status'] == 0) {
            return $result;
        }

        // 提取数据
        $receiverId = $result['data']['receiver_id'];

        // 获取银行信息，以获取cid
        $bankResult = $this->getBankInfoByRegId($regId, $userId);
        if ($bankResult['status'] == 0) {
            return $bankResult;
        }

        // 提取cid
        $cid = $bankResult['data']['cid'];

        // 获取注册记录信息，以获取event和paper_id
        $registerModel = D('SubRegister');
        $register = $registerModel->where(['id' => $regId, 'userid' => $userId])->find();

        if (!$register) {
            return ['status' => 0, 'message' => '注册记录不存在或不属于当前用户'];
        }

        // 从注册表中获取会议简称
        $event = isset($register['event']) ? $register['event'] : '';
        // 获取论文ID
        $paperId = isset($register['paperid']) ? $register['paperid'] : '';

        // 构建转账数据
        $data = [
            'cid' => $cid,
            'reg_id' => $regId,
            'user_id' => $userId,
            'event' => $event, // 会议简称
            'paper_id' => $paperId, // 论文ID
            'transfer_name' => isset($postData['transfer_name']) ? trim($postData['transfer_name']) : '',
            'transfer_account' => isset($postData['transfer_account']) ? trim($postData['transfer_account']) : '',
            'total' => isset($postData['total']) ? floatval($postData['total']) : 0,
            'currency' => $currency,
            'receiver_id' => $receiverId,
            'transfer_time' => isset($postData['transfer_time']) ? strtotime($postData['transfer_time']) : time(),
            'transfer_pic' => isset($postData['transfer_pic']) ? trim($postData['transfer_pic']) : '',
            'remark' => isset($postData['remark']) ? trim($postData['remark']) : '',
            'status' => 0,
            'create_time' => time(),
            'update_time' => time()
        ];

        // 创建转账记录
        return $this->createTransfer($data);
    }

    /**
     * 记录转账数据日志
     * @param array $data 转账数据
     */
    private function logTransferData($data)
    {
        $debugInfo = "\n\n数据字段信息：\n";
        foreach ($data as $key => $value) {
            if ($key == 'transfer_pic') {
                $debugInfo .= $key . ': ' . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value) . ' (长度: ' . strlen($value) . ')\n';
            } else {
                $debugInfo .= $key . ': ' . $value . '\n';
            }
        }

        $logFile = './Uploads/transfer_debug.log';
        file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 提交数据: ' . $debugInfo . "\n\n", FILE_APPEND);
    }

    /**
     * 记录转账结果日志
     * @param bool $success 是否成功
     * @param mixed $result 结果信息
     */
    private function logTransferResult($success, $result)
    {
        $logFile = './Uploads/transfer_debug.log';
        if ($success) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 添加成功，ID: ' . $result . "\n\n", FILE_APPEND);
        } else {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 添加失败，错误: ' . $result . "\n\n", FILE_APPEND);
        }
    }

    /**
     * 删除转账记录（仅限待审核状态）
     * @param int $id 转账记录ID
     * @param int $userId 用户ID，用于验证权限
     * @return array 包含状态和消息的数组
     */
    public function deleteTransfer($id, $userId)
    {
        // 记录日志
        $logFile = './Uploads/transfer_debug.log';
        file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 服务类: 开始删除转账记录，ID: ' . $id . ', 用户ID: ' . $userId . "\n", FILE_APPEND);

        $transferModel = D('SubTransfer');
        // 直接查询数据库
        $transfer = $transferModel->where(array('id' => $id))->find();

        // 记录查询结果
        file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 服务类: 查询结果: ' . json_encode($transfer) . "\n", FILE_APPEND);

        // 验证记录是否存在且属于当前用户
        if (!$transfer) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 服务类: 错误: 转账记录不存在' . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'Transfer record does not exist'];
        }

        if ($transfer['user_id'] != $userId) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 服务类: 错误: 您无权操作此转账记录, 记录用户ID: ' . $transfer['user_id'] . ', 当前用户ID: ' . $userId . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'You do not have permission to operate this transfer record'];
        }

        // 验证状态是否可删除
        if (!Status::isDeletable($transfer['status'])) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 服务类: 错误: 只能删除待审核或审核未通过的转账记录, 当前状态: ' . $transfer['status'] . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'Only pending or rejected transfer records can be deleted'];
        }

        // 删除转账记录
        $result = $transferModel->where(array('id' => $id))->delete();

        file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 服务类: 删除结果: ' . ($result ? '成功' : '失败') . "\n", FILE_APPEND);

        if ($result) {
            // 如果有上传的图片，删除图片文件
            if (!empty($transfer['transfer_pic'])) {
                $picPath = './Uploads/' . $transfer['transfer_pic'];
                if (file_exists($picPath)) {
                    @unlink($picPath);
                    file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 服务类: 删除图片文件: ' . $picPath . "\n", FILE_APPEND);
                }
            }

            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 服务类: 成功: 转账记录已删除' . "\n", FILE_APPEND);
            return ['status' => 1, 'message' => 'Transfer record has been deleted'];
        } else {
            file_put_contents($logFile, date('Y-m-d H:i:s') . ' - 服务类: 错误: 删除失败' . "\n", FILE_APPEND);
            return ['status' => 0, 'message' => 'Delete failed, please try again'];
        }
    }


}
