<?php
namespace Mpanel\Service;

use Common\Lib\InvoiceStatusConstants;
use Common\Lib\OaAuditStatusConstants;

/**
 * 状态服务类
 * 用于处理各种状态的获取和格式化
 */
class StatusService
{
    /**
     * 获取发票状态信息
     *
     * @param int $status 发票状态码
     * @return array 包含状态文本和CSS类的数组
     */
    public function getInvoiceStatusInfo($regId)
    {
        // 默认状态信息（未申请）
        $result = [
            'text' => '未申请',
            'class' => 'label label-invoice-default',
            'found' => false,
            'status' => null
        ];

        // 如果regId为空，直接返回默认状态
        if (empty($regId)) {
            return $result;
        }

        // 查询发票记录
        $model = D('OaInvoice', 'OA');
        $record = $model->where(['reg_id' => $regId])->order('id DESC')->find();

        // 如果找到记录，更新状态信息
        if ($record) {
            $result['found'] = true;
            $result['status'] = $record['status'];
            $result['text'] = InvoiceStatusConstants::getStatusText($record['status']);

            // 自定义发票状态样式类
            $classMap = [
                0 => 'label-invoice-warning',  // 待审核（橙色）
                10 => 'label-invoice-info',    // 初审未通过
                20 => 'label-invoice-primary', // 等待开票
                30 => 'label-invoice-success', // 开票成功
                40 => 'label-invoice-danger',  // 开票失败
                50 => 'label-invoice-void'     // 发票已冷红
            ];

            $statusClass = isset($classMap[$record['status']]) ? $classMap[$record['status']] : 'label-invoice-default';
            $result['class'] = 'label ' . $statusClass;
            $result['record'] = $record;
        }

        return $result;
    }

    /**
     * 获取查账状态信息
     *
     * @param int $regId 注册ID
     * @param int $payId 支付ID
     * @return array 包含状态文本、CSS类和是否找到记录的数组
     */
    public function getAuditStatusInfo($regId, $payId = 0)
    {
        // 默认状态信息（未查账）
        $result = [
            'found' => false,
            'text' => '未查账',
            'class' => 'label label-audit-default',
            'status' => null
        ];

        // 如果regId或payId为空，直接返回默认状态
        if (empty($regId) && empty($payId)) {
            return $result;
        }

        // 查询条件
        $condition = [];
        if (!empty($regId)) {
            $condition['reg_id'] = $regId;
        }
        if (!empty($payId)) {
            $condition['pay_id'] = $payId;
        }

        // 查询查账记录
        $model = D('OaAuditAccount', 'OA');
        $record = $model->where($condition)->find();

        // 如果找到记录，更新状态信息
        if ($record) {
            $result['found'] = true;
            $result['status'] = $record['status'];
            $result['text'] = OaAuditStatusConstants::getStatusText($record['status']);

            // 自定义查账状态样式类，使用更柔和的视觉效果
            $classMap = [
                0 => 'label-audit-default',    // 待提交查账
                5 => 'label-audit-draft',      // 草稿状态
                10 => 'label-audit-info',      // 查账提交成功（OA审核中）
                20 => 'label-audit-success',   // 查账成功
                30 => 'label-audit-danger',    // 查账失败
                40 => 'label-audit-warning'    // 查账驳回
            ];

            $statusClass = isset($classMap[$record['status']]) ? $classMap[$record['status']] : 'label-audit-default';
            $result['class'] = 'label ' . $statusClass;
            $result['record'] = $record;
        }

        return $result;
    }

    /**
     * 获取支付状态文本
     *
     * @param int $payStatus 支付状态码
     * @param int $payType 支付类型
     * @return array 包含状态文本和CSS类的数组
     */
    public function getPayStatusInfo($payStatus, $payType = null)
    {
        $text = '';
        $class = '';

        switch ($payStatus) {
            case 0:
                $text = '尚未付款';
                $class = 'label-default';
                break;
            case 1:
                $text = $payType == 1 ? '在线支付成功' : '转账汇款成功';
                $class = 'label-success';
                break;
            case 2:
                $text = '等待确认';
                $class = 'label-info';
                break;
            case 3:
                $text = '失败';
                $class = 'label-danger';
                break;
            default:
                $text = '未知状态';
                $class = 'label-default';
        }

        return [
            'text' => $text,
            'class' => 'label ' . $class
        ];
    }

    /**
     * 获取实付金额信息
     *
     * @param int $regId 注册ID
     * @param int $payStatus 支付状态
     * @param int $payType 支付类型
     * @return array 包含实付金额文本和CSS类的数组
     */
    public function getActualPaymentInfo($regId, $payStatus, $payType)
    {
        $text = '';
        // 移除未使用的变量

        switch ($payStatus) {
            case 0:
                $text = '未支付';
                break;
            case 1:
                if ($payType == 1) {
                    // 在线支付，获取支付信息
                    $db_pay = M('SubPay');
                    $sql = ['regid' => $regId, 'status' => 20];
                    $pay_info = $db_pay->where($sql)->find();

                    if ($pay_info) {
                        $moneyType = $pay_info['moneytype'] == 0 ? 'CNY' : 'USD';
                        $text = '<b>' . $pay_info['total'] . ' ' . $moneyType . '</b>';
                    } else {
                        $text = '<b>支付信息不可用</b>';
                    }
                } else {
                    $text = '请人工审核';
                }
                break;
            case 2:
                $text = '人工审核';
                break;
            case 3:
                $text = '失败';
                break;
            default:
                $text = '未知状态';
        }

        return [
            'text' => $text,
            'html' => true
        ];
    }
}
