<extend name="Mpanel@Base/admin_base" />

<block name="breadcrumb">
    <ol class="breadcrumb">
        <li><a href="{:U('Mpanel/Index/index')}"><i class="fa fa-home"></i> 首页</a></li>
        <li><a href="{:U('OA/Invoice/index')}"><i class="fa fa-list"></i> 发票列表</a></li>
        <li class="active"><i class="fa fa-file-text-o"></i> 发票管理</li>
    </ol>
</block>

<block name="style">
    <style>
        /* 基础样式 */
        .tab-content {
            padding: 20px 0;
        }
        
        .nav-tabs {
            border-bottom: 2px solid #f0f0f0;
        }
        
        .nav-tabs > li > a {
            border: none;
            margin-right: 0;
            font-weight: 600;
            padding: 10px 15px;
            color: #555;
        }
        
        .nav-tabs > li > a:hover {
            background-color: transparent;
            border: none;
            color: #337ab7;
        }
        
        .nav-tabs > li.active > a,
        .nav-tabs > li.active > a:hover,
        .nav-tabs > li.active > a:focus {
            border: none;
            border-bottom: 2px solid #337ab7;
            color: #337ab7;
            background-color: transparent;
        }
        
        /* 内容区样式 */
        .info-section {
            margin-bottom: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            position: relative;
        }
        
        .info-section-header {
            margin: -15px -15px 15px;
            padding: 10px 15px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
        }
        
        .info-section-header h4 {
            margin: 0;
            font-weight: 600;
        }
        
        .table-simple {
            margin-bottom: 0;
        }
        
        .table-simple th {
            background-color: #f9f9f9;
            width: 30%;
        }
        
        /* 加载中样式 */
        .loading-container {
            text-align: center;
            padding: 30px 0;
        }
        
        .loading-container i {
            color: #337ab7;
            margin-bottom: 10px;
        }
        
        /* 标签样式 */
        .label {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
        }
        
        /* 金额样式 */
        .text-amount {
            color: #d9534f;
            font-weight: bold;
        }
    </style>
</block>

<block name="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <!-- 查账状态面板 -->
                <include file="Common/audit_status_panel" />
                
                <!-- Tab导航 -->
                <ul class="nav nav-tabs" id="invoiceTab" role="tablist">
                    <li class="nav-item <eq name="activeTab" value="details">active</eq>">
                        <a href="#details" data-toggle="tab" role="tab" aria-controls="details" aria-selected="true">
                            <i class="fa fa-info-circle"></i> 发票详情
                        </a>
                    </li>
                    <li class="nav-item <eq name="activeTab" value="manage">active</eq>">
                        <a href="#manage" data-toggle="tab" role="tab" aria-controls="manage" aria-selected="false">
                            <i class="fa fa-cogs"></i> 发票管理
                        </a>
                    </li>
                    <if condition="$canDownload">
                        <li class="nav-item <eq name="activeTab" value="attachments">active</eq>">
                            <a href="#attachments" data-toggle="tab" role="tab" aria-controls="attachments" aria-selected="false">
                                <i class="fa fa-paperclip"></i> 发票附件
                            </a>
                        </li>
                    </if>
                    <li class="nav-item <eq name="activeTab" value="history">active</eq>">
                        <a href="#history" data-toggle="tab" role="tab" aria-controls="history" aria-selected="false">
                            <i class="fa fa-history"></i> 状态历史
                        </a>
                    </li>
                </ul>
                
                <!-- Tab内容 -->
                <div class="tab-content">
                    <!-- 发票详情 -->
                    <div class="tab-pane <eq name="activeTab" value="details">active</eq>" id="details" role="tabpanel">
                        <!-- 发票基本信息 -->
                        <div class="info-section">
                            <div class="info-section-header">
                                <h4><i class="fa fa-file-text-o"></i> 发票基本信息</h4>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-simple">
                                        <tr>
                                            <th>发票ID</th>
                                            <td>{$invoice.id}</td>
                                        </tr>
                                        <tr>
                                            <th>发票抬头</th>
                                            <td>{$invoice.invoice_title}</td>
                                        </tr>
                                        <tr>
                                            <th>发票金额</th>
                                            <td><span class="text-amount">{$invoice.amount}</span></td>
                                        </tr>
                                        <tr>
                                            <th>发票类型</th>
                                            <td><span class="label label-{$invoice.invoice_type_class}">{$invoice.invoice_type_text}</span></td>
                                        </tr>
                                        <tr>
                                            <th>支付途径</th>
                                            <td><span class="label label-{$invoice.pay_type_class}">{$invoice.pay_type_text}</span></td>
                                        </tr>
                                        <tr>
                                            <th>发票状态</th>
                                            <td><span class="label label-{$invoice.status_class}">{$invoice.status_text}</span></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-simple">
                                        <tr>
                                            <th>纳税人识别号</th>
                                            <td>{$invoice.buyer_tax_num}</td>
                                        </tr>
                                        <tr>
                                            <th>联系电话</th>
                                            <td>{$invoice.buyer_phone}</td>
                                        </tr>
                                        <tr>
                                            <th>电子邮箱</th>
                                            <td>{$invoice.buyer_email}</td>
                                        </tr>
                                        <tr>
                                            <th>商品信息</th>
                                            <td>{$invoice.goods_info_text}</td>
                                        </tr>
                                        <tr>
                                            <th>申请时间</th>
                                            <td>{$invoice.create_time_text}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- 专票特有信息 -->
                            <if condition="$invoice.invoice_type eq 'bs'">
                                <div class="row" style="margin-top: 15px;">
                                    <div class="col-md-12">
                                        <h5 style="border-bottom: 1px solid #ddd; padding-bottom: 8px; margin-bottom: 15px; font-weight: 600;">专票信息</h5>
                                        <div class="col-md-6">
                                            <table class="table table-simple">
                                                <tr>
                                                    <th>单位地址</th>
                                                    <td>{$invoice.buyer_address}</td>
                                                </tr>
                                                <tr>
                                                    <th>开户行</th>
                                                    <td>{$invoice.buyer_account_name}</td>
                                                </tr>
                                                <tr>
                                                    <th>账号</th>
                                                    <td>{$invoice.buyer_account}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </if>
                            
                            <!-- 备注信息 -->
                            <div class="row" style="margin-top: 15px;">
                                <div class="col-md-12">
                                    <h5 style="border-bottom: 1px solid #ddd; padding-bottom: 8px; margin-bottom: 15px; font-weight: 600;">备注信息</h5>
                                    <p>{$invoice.remark|default="无"}</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 查账详细信息 -->
                        <notempty name="auditInfo.has_record">
                            <div class="info-section">
                                <div class="info-section-header">
                                    <h4><i class="fa fa-list-alt"></i> 查账详细信息</h4>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-simple">
                                            <tr>
                                                <th>OA订单号</th>
                                                <td>{$auditInfo.record.oa_id}</td>
                                            </tr>
                                            <tr>
                                                <th>查账状态</th>
                                                <td><span class="label label-{$auditInfo.record.status_class}">{$auditInfo.record.status_text}</span></td>
                                            </tr>
                                            <tr>
                                                <th>提交时间</th>
                                                <td>{$auditInfo.record.create_time_text}</td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-simple">
                                            <tr>
                                                <th>金额</th>
                                                <td><span class="text-amount">{$auditInfo.record.amount} {$auditInfo.record.unit}</span></td>
                                            </tr>
                                            <tr>
                                                <th>提交人</th>
                                                <td>{$auditInfo.record.submit_name}</td>
                                            </tr>
                                            <tr>
                                                <th>备注</th>
                                                <td>{$auditInfo.record.remark|default="无"}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </notempty>
                        
                        <!-- 注册信息 -->
                        <notempty name="registerInfo">
                            <div class="info-section">
                                <div class="info-section-header">
                                    <h4><i class="fa fa-user-circle"></i> 关联注册信息</h4>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-simple">
                                            <tr>
                                                <th>注册人</th>
                                                <td>
                                                    <php>
                                                        $fullname = $registerInfo['firstname'];
                                                        if(!empty($registerInfo['middlename'])) {
                                                        $fullname .= ' ' . $registerInfo['middlename'];
                                                        }
                                                        if(!empty($registerInfo['lastname'])) {
                                                        $fullname .= ' ' . $registerInfo['lastname'];
                                                        }
                                                        echo $fullname;
                                                    </php>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>邮箱</th>
                                                <td>{$registerInfo.email}</td>
                                            </tr>
                                            <tr>
                                                <th>单位</th>
                                                <td>
                                                    <php>
                                                        if(!empty($registerInfo['affiliation'])) {
                                                        echo $registerInfo['affiliation'];
                                                        } else if(!empty($registerInfo['position'])) {
                                                        echo $registerInfo['position'];
                                                        } else {
                                                        echo "--";
                                                        }
                                                    </php>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-simple">
                                            <tr>
                                                <th>会议名称</th>
                                                <td>{$registerInfo.event}</td>
                                            </tr>
                                            <tr>
                                                <th>文章ID</th>
                                                <td>
                                                    <php>
                                                        if(!empty($registerInfo['paperid'])) {
                                                        echo $registerInfo['paperid'];
                                                        } else if(!empty($registerInfo['paper_id'])) {
                                                        echo $registerInfo['paper_id'];
                                                        } else {
                                                        echo "--";
                                                        }
                                                    </php>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>注册费</th>
                                                <td>
                                                    <php>
                                                        $fee = "--";
                                                        $currency = "";

                                                        if(!empty($registerInfo['total'])) {
                                                        $fee = $registerInfo['total'];
                                                        if(!empty($registerInfo['currency'])) {
                                                        $currency = $registerInfo['currency'];
                                                        }
                                                        } else if(!empty($registerInfo['fee'])) {
                                                        $fee = $registerInfo['fee'];
                                                        if(!empty($registerInfo['currency'])) {
                                                        $currency = $registerInfo['currency'];
                                                        }
                                                        }

                                                        echo '<span class="text-amount">'.$fee.' '.$currency.'</span>';
                                                    </php>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </notempty>
                    </div>
                    
                    <!-- 发票管理 -->
                    <div class="tab-pane <eq name="activeTab" value="manage">active</eq>" id="manage" role="tabpanel">
                        <div class="loading-container" id="manage-loading">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>加载中，请稍候...</p>
                        </div>
                        <div id="manage-content"></div>
                    </div>
                    
                    <!-- 发票附件 -->
                    <if condition="$canDownload">
                        <div class="tab-pane <eq name="activeTab" value="attachments">active</eq>" id="attachments" role="tabpanel">
                            <div class="loading-container" id="attachments-loading">
                                <i class="fa fa-spinner fa-spin fa-2x"></i>
                                <p>加载中，请稍候...</p>
                            </div>
                            <div id="attachments-content"></div>
                        </div>
                    </if>
                    
                    <!-- 状态历史 -->
                    <div class="tab-pane <eq name="activeTab" value="history">active</eq>" id="history" role="tabpanel">
                        <div class="loading-container" id="history-loading">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>加载中，请稍候...</p>
                        </div>
                        <div id="history-content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</block>

<block name="script">
<script>
    $(document).ready(function() {
        // Tab切换事件
        $('#invoiceTab a').on('click', function (e) {
            e.preventDefault();
            $(this).tab('show');
            
            // 获取当前Tab ID
            var tabId = $(this).attr('href').substring(1);
            
            // 更新URL，支持浏览器历史
            history.pushState({tab: tabId}, '', '?id={$id}&tab=' + tabId);
            
            // 按需加载内容
            loadTabContent(tabId);
        });
        
        // 初始加载当前激活的Tab内容
        var activeTab = '{$activeTab}';
        if (activeTab && activeTab !== 'details') {
            loadTabContent(activeTab);
        }
        
        // 加载Tab内容
        function loadTabContent(tabId) {
            // 如果内容已加载，则不重复加载
            if ($('#' + tabId + '-content').children().length > 0) {
                return;
            }
            
            // 显示加载中
            $('#' + tabId + '-loading').show();
            
            // 根据Tab ID加载不同内容
            switch (tabId) {
                case 'manage':
                    loadManageContent();
                    break;
                case 'attachments':
                    loadAttachmentsContent();
                    break;
                case 'history':
                    loadHistoryContent();
                    break;
            }
        }
        
        // 加载发票管理内容
        function loadManageContent() {
            $.ajax({
                url: '{:U("OA/InvoiceManager/getManageOptions")}',
                type: 'POST',
                data: {id: {$id}},
                dataType: 'json',
                success: function(response) {
                    $('#manage-loading').hide();
                    if (response.status) {
                        $('#manage-content').html(response.html);
                        // 初始化管理页面的事件
                        initManageEvents();
                    } else {
                        $('#manage-content').html('<div class="alert alert-danger">' + response.message + '</div>');
                    }
                },
                error: function() {
                    $('#manage-loading').hide();
                    $('#manage-content').html('<div class="alert alert-danger">加载失败，请刷新页面重试</div>');
                }
            });
        }
        
        // 加载发票附件内容
        function loadAttachmentsContent() {
            $.ajax({
                url: '{:U("OA/InvoiceManager/getAttachments")}',
                type: 'POST',
                data: {id: {$id}},
                dataType: 'json',
                success: function(response) {
                    $('#attachments-loading').hide();
                    if (response.status) {
                        $('#attachments-content').html(response.html);
                    } else {
                        $('#attachments-content').html('<div class="alert alert-danger">' + response.message + '</div>');
                    }
                },
                error: function() {
                    $('#attachments-loading').hide();
                    $('#attachments-content').html('<div class="alert alert-danger">加载失败，请刷新页面重试</div>');
                }
            });
        }
        
        // 加载发票历史内容
        function loadHistoryContent() {
            $.ajax({
                url: '{:U("OA/InvoiceManager/getHistory")}',
                type: 'POST',
                data: {id: {$id}},
                dataType: 'json',
                success: function(response) {
                    $('#history-loading').hide();
                    if (response.status) {
                        $('#history-content').html(response.html);
                    } else {
                        $('#history-content').html('<div class="alert alert-danger">' + response.message + '</div>');
                    }
                },
                error: function() {
                    $('#history-loading').hide();
                    $('#history-content').html('<div class="alert alert-danger">加载失败，请刷新页面重试</div>');
                }
            });
        }
        
        // 初始化管理页面的事件
        function initManageEvents() {
            // 提交到API按钮点击事件
            $('#submitToApiBtn').on('click', function() {
                var invoiceIds = $(this).data('id');
                
                // 确认对话框
                Swal.fire({
                    title: '确认提交',
                    text: '是否确认将发票提交到API？',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: '确认',
                    cancelButtonText: '取消'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // 显示加载状态
                        Swal.fire({
                            title: '提交中...',
                            text: '正在提交发票到API',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });
                        
                        // 提交请求
                        $.ajax({
                            url: '{:U("OA/InvoiceManager/submitToApi")}',
                            type: 'POST',
                            data: {invoice_ids: invoiceIds},
                            dataType: 'json',
                            success: function(response) {
                                if (response.status) {
                                    Swal.fire({
                                        title: '成功',
                                        text: response.message,
                                        icon: 'success'
                                    }).then(() => {
                                        window.location.reload();
                                    });
                                } else {
                                    Swal.fire('错误', response.message, 'error');
                                }
                            },
                            error: function() {
                                Swal.fire('错误', '服务器错误，请稍后重试', 'error');
                            }
                        });
                    }
                });
            });
            
            // 驳回按钮点击事件
            $('#rejectBtn').on('click', function() {
                var invoiceId = $(this).data('id');
                
                // 使用 SweetAlert2 显示驳回原因输入框
                Swal.fire({
                    title: '驳回发票申请',
                    text: '请输入驳回原因',
                    input: 'textarea',
                    inputPlaceholder: '请详细说明驳回原因...',
                    inputAttributes: {
                        'aria-label': '驳回原因'
                    },
                    showCancelButton: true,
                    confirmButtonText: '确认驳回',
                    cancelButtonText: '取消',
                    showLoaderOnConfirm: true,
                    inputValidator: (value) => {
                        if (!value) {
                            return '请输入驳回原因'
                        }
                    },
                    preConfirm: (refusal) => {
                        return new Promise((resolve, reject) => {
                            $.ajax({
                                url: '{:U("OA/InvoiceManager/rejectInvoice")}',
                                type: 'POST',
                                data: {
                                    invoice_id: invoiceId,
                                    refusal: refusal
                                },
                                dataType: 'json',
                                success: function(response) {
                                    if (response.status) {
                                        resolve(response);
                                    } else {
                                        Swal.showValidationMessage(response.message || '驳回失败');
                                        reject(response.message || '驳回失败');
                                    }
                                },
                                error: function(xhr, status, error) {
                                    Swal.showValidationMessage(`请求失败: ${error}`);
                                    reject(error);
                                }
                            });
                        });
                    },
                    allowOutsideClick: () => !Swal.isLoading()
                }).then((result) => {
                    if (result.isConfirmed && result.value) {
                        Swal.fire({
                            title: '成功',
                            text: result.value.message || '驳回成功',
                            icon: 'success'
                        }).then(() => {
                            window.location.reload();
                        });
                    }
                });
            });
        }
    });
</script>
</block>
