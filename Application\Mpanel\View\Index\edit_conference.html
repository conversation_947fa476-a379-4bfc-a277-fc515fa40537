<extend name="Base/admin_base" />

<block name="title">
  <title>编辑会议</title>
</block>

<block name="breadcrumb">
  <ol class="breadcrumb">
    <li><a href="{:U('Index/index')}">首页</a></li>
    <li class="active">编辑会议</li>
  </ol>
</block>

<block name="content">
  <div class="panel panel-default">
    <div class="panel-body">

      <!-- 投稿/注册开关 -->
      <form action="{:U(set_sub)}" method="post" class="form-inline mb-3">
        <div class="alert alert-warning" role="alert">
          <div class="checkbox">
            <label>
              <input type="checkbox" name="is_sub" id="is_sub" value="1" <if condition="$info['is_sub'] eq 1">
              checked </if>> 关闭投稿
            </label>
          </div>
          <div class="checkbox">
            <label>
              <input type="checkbox" name="is_reg" id="is_reg" value="1" <if condition="$info['is_reg'] eq 1">
              checked </if>> 关闭注册
            </label>
          </div>
          <input type="hidden" name="id" value="{$info.id}">
          <button type="submit" class="btn btn-sm btn-info">保存</button>
        </div>
      </form>

      <hr>

      <!-- 会议信息编辑表单 -->
      <form action="" method="post" id="editConferenceForm">
        <a class="btn btn-sm btn-primary mb-3" href="{:U('track',array('cid'=>$info['id']))}">Special 管理</a>

        <table class="table table-bordered">
          <tbody>
            <tr>
              <th class="active text-center" colspan="4">基本信息</th>
            </tr>
            <tr>
              <td colspan="4">
                <div class="form-inline">
                  <label class="mr-2">分组/账号：</label>
                  <select name="org" class="form-control input-sm mr-2">
                    <option value="">协会分组</option>
                    <volist name="org_list" id="org">
                      <option value="{$org.name}" <if condition="$org['name'] eq $info['org']">
                        selected </if>> {$org.name}
                      </option>
                    </volist>
                  </select>

                  <select name="bank_en" class="form-control input-sm mr-2">
                    <option value="">国际账户</option>
                    <volist name="en_bank_list" id="e">
                      <option value="{$e.id}" <if condition="$info['bank_en'] eq $e['id']"> selected
                        </if>> {$e.nickname}
                      </option>
                    </volist>
                  </select>

                  <select name="bank_cn" class="form-control input-sm mr-2">
                    <option value="">国内账户</option>
                    <volist name="cn_bank_list" id="c">
                      <option value="{$c.id}" <if condition="$info['bank_cn'] eq $c['id']"> selected
                        </if>> {$c.nickname}
                      </option>
                    </volist>
                  </select>
                  <select name="v_mid" class="form-control input-sm">
                    <volist name="merchantInfo" id="item">
                      <option value="{$key}" <if condition="$info['v_mid'] eq $key">selected
                        </if>> {$item.name}-{$key}
                      </option>
                    </volist>
                    <option value="1" <if condition="$info['v_mid'] eq 1">selected</if>>关闭在线支付
                    </option>
                  </select>
                </div>
              </td>
            </tr>

            <tr>
              <th class="active text-center">会议简称</th>
              <td>
                <input type="text" class="form-control input-sm" name="event" value="{$info.event}" required>
              </td>
              <th class="active text-center">会议全称</th>
              <td><input type="text" class="form-control input-sm" name="title" value="{$info.title}" required></td>
            </tr>

            <tr>
              <th class="active text-center">会议地点</th>
              <td><input type="text" class="form-control input-sm" name="place" value="{$info.place}" required></td>
              <th class="active text-center">会议网址</th>
              <td><input type="url" class="form-control input-sm" name="website" value="{$info.website}" required></td>
            </tr>

            <tr>
              <th class="active text-center">会议邮箱</th>
              <td><input type="email" class="form-control input-sm" name="email" value="{$info.email}" required></td>
              <th class="active text-center">第三方查看密码</th>
              <td>
                <input type="text" class="form-control input-sm" name="password" placeholder="非必填，长度不小于6位">
              </td>
            </tr>

            <tr>
              <th class="active text-center" colspan="4">重要日期</th>
            </tr>
            <tr>
              <th class="active text-center">会议时间</th>
              <td><input type="date" class="form-control input-sm" name="start_date"
                  value="{$info.start_date|date='Y-m-d',###}" required></td>
              <th class="active text-center">至</th>
              <td><input type="date" class="form-control input-sm" name="end_date"
                  value="{$info.end_date|date='Y-m-d',###}" required></td>
            </tr>

            <tr>
              <th class="active text-center">通知日期</th>
              <td><input type="date" class="form-control input-sm" name="notice_date"
                  value="{$info.notice_date|date='Y-m-d',###}" required></td>
              <th class="active text-center">投稿截止</th>
              <td><input type="date" class="form-control input-sm" name="deadline"
                  value="{$info.deadline|date='Y-m-d',###}" required></td>
            </tr>

            <tr>
              <th class="active text-center">注册截止</th>
              <td><input type="date" class="form-control input-sm" name="regend_date"
                  value="{$info.regend_date|date='Y-m-d',###}" required></td>
              <td colspan="2"></td>
            </tr>

            <tr>
              <th class="active text-center" colspan="4">注册费用</th>
            </tr>
            <tr>
              <td colspan="4">
                <div class="row">
                  <volist name="type" id="t">
                    <div class="col-md-6 mb-2">
                      <div class="input-group input-group-sm">
                        <span class="input-group-addon">类型</span>
                        <input type="text" class="form-control" name="type[{$key}][name]" value="{$t.name}"
                          placeholder="类型">
                        <span class="input-group-addon">USD</span>
                        <input type="number" class="form-control" name="type[{$key}][usd]" value="{$t.usd}"
                          placeholder="USD">
                        <span class="input-group-addon">CNY</span>
                        <input type="number" class="form-control" name="type[{$key}][cny]" value="{$t.cny}"
                          placeholder="CNY">
                      </div>
                    </div>
                  </volist>
                </div>
              </td>
            </tr>

            <tr>
              <th class="active text-center" colspan="4">额外费用</th>
            </tr>
            <tr>
              <td colspan="4">
                <div class="row">
                  <volist name="extras" id="e">
                    <div class="col-md-6 mb-2">
                      <div class="input-group input-group-sm">
                        <span class="input-group-addon">项目</span>
                        <input type="text" class="form-control" name="extras[{$key}][name]" value="{$e.name}"
                          placeholder="项目">
                        <span class="input-group-addon">USD</span>
                        <input type="number" class="form-control" name="extras[{$key}][usd]" value="{$e.usd}"
                          placeholder="USD">
                        <span class="input-group-addon">CNY</span>
                        <input type="number" class="form-control" name="extras[{$key}][cny]" value="{$e.cny}"
                          placeholder="CNY">
                      </div>
                    </div>
                  </volist>
                </div>
              </td>
            </tr>

            <tr>
              <th class="active text-center">增加注册信息选项</th>
              <td colspan="3">
                <div id="inputsWrapper">
                  <volist name="more" id="mo">
                    <div class="input-group input-group-sm mb-2">
                      <input type="text" class="form-control" name="more[]" value="{$mo}" placeholder="选项">
                      <span class="input-group-btn">
                        <button class="btn btn-danger remove-input" type="button">
                          <i class="fa fa-times"></i>
                        </button>
                      </span>
                    </div>
                  </volist>
                </div>
                <button id="addMoreInput" class="btn btn-sm btn-secondary" type="button">
                  <i class="fa fa-plus"></i> 增加选项
                </button>
              </td>
            </tr>

            <tr>
              <th class="active text-center">Presentation Type</th>
              <td colspan="3">
                <div class="form-inline">
                  <volist name="a_arr" id="a">
                    <label class="checkbox-inline">
                      <input type="checkbox" name="pretype[]" value="{$a}"> {$a}
                    </label>
                  </volist>
                  <volist name="b_arr" id="b">
                    <label class="checkbox-inline">
                      <input type="checkbox" name="pretype[]" value="{$b}"> {$b}
                    </label>
                  </volist>
                </div>
              </td>
            </tr>

            <tr>
              <th class="active text-center">Paper Track 设置</th>
              <td colspan="3">
                <div class="form-group">
                  <input type="text" class="form-control input-sm" name="paper_type"
                         value="{$info.paper_type}"
                         placeholder="例如：计算机科学|环境保护|生物医学|工程技术">
                  <small class="form-text text-muted">
                    <i class="fa fa-info-circle"></i>
                    使用 "|" 分隔不同的 track 选项。留空则不显示 track 选择。
                    <br>
                    <span class="text-warning">
                      <i class="fa fa-exclamation-triangle"></i>
                      <strong>注意：</strong>该功能将在后续代码中废弃，建议不再使用。
                    </span>
                  </small>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <input type="hidden" name="id" value="{$info.id}">
        <button type="submit" class="btn btn-primary btn-lg">保存</button>
      </form>
    </div>
  </div>
</block>

<block name="script">
  <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.14.0/dist/jquery.validate.min.js"></script>
  <script>
    $(document).ready(function () {
      // 添加自定义验证方法
      $.validator.addMethod("eventFormat", function (value, element) {
        return this.optional(element) || /^[A-Za-z0-9]+[0-9]{4}$/.test(value);
      }, "会议简称必须以大小写字母或数字开头，后跟4位数字，中间不要有空格");

      // 表单验证
      $("#editConferenceForm").validate({
        rules: {
          org: "required",
          event: {
            required: true,
            eventFormat: true
          },
          bank_en: "required",
          title: "required",
          place: "required",
          start_date: "required",
          end_date: "required",
          deadline: "required",
          regend_date: "required",
          notice_date: "required",
          website: "required",
          email: {
            required: true,
            email: true
          }
        },
        messages: {
          event: {
            required: "会议简称为必填！",
            eventFormat: "会议简称必须以大写字母或数字开头，后跟4位数字，中间不要有空格"
          }
        }
      });

      // 增加注册信息选项
      $('#addMoreInput').click(function () {
        $('#inputsWrapper').append(`
                <div class="input-group input-group-sm mb-2">
                    <input type="text" class="form-control" name="more[]" placeholder="选项">
                    <span class="input-group-btn">
                        <button class="btn btn-danger remove-input" type="button">
                            <i class="fa fa-times"></i>
                        </button>
                    </span>
                </div>
            `);
      });

      // 移除注册信息选项
      $(document).on('click', '.remove-input', function () {
        $(this).closest('.input-group').remove();
      });
    });
  </script>
</block>