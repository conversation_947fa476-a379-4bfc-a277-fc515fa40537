发票请求体发生变化

请求体变化
原
{
 "no": null,
 "userId": null,
 "deptId": null,
 "submitName": null,
 "submitDept": null,
 "orderNum": null,
 "confName": null,
 "paperCode": null,
 "creditSerial": null,
 "remitterInfo": null,
 "amount": null,
 "unit": null,
 "remitTime": null,
 "account": null,
 "remark": null,
 "annex": null,
 "callbackUrl": null,
 "receipt": [
 {
 "userId": null,
 "deptId": null,
 "submitName": null,
 "submitDept": null,
 "no": null,
 "orderNum": null,
 "invoiceTitle": null,
 "buyerTaxNum": null,
 "amount": null,
 "buyerPhone": null,
 "buyerEmail": null,
 "buyerAddress": null,
 "buyerAccount": null,
 "buyerAccountName": null,
 "salerCompany": null,
 "goodsInfo": null,
 "invoiceType": null,
 "submitReturnApi": null,
 "remark": null
 }
 ],
 "annexList":[ //若你目前用不到，你就设置为null，也可以设置为[] 前面不动，只是多了一个附件的list
 {
 "name":null,
 "src":null
 }
 ]
}

###  
现在

{
 "no": null,
 "userId": null,
 "deptId": null,
 "submitName": null,
 "submitDept": null,
 "orderNum": null,
 "confName": null,
 "paperCode": null,
 "creditSerial": null,
 "remitterInfo": null,
 "amount": null,
 "unit": null,
 "remitTime": null,
 "account": null,
 "remark": null,
 "annex": null,
 "callbackUrl": null,
 "receipt": [
 {
 "userId": null,
 "deptId": null,
 "submitName": null,
 "submitDept": null,
 "no": null,
 "orderNum": null,
 "invoiceTitle": null,
 "buyerTaxNum": null,
 "amount": null,
 "buyerPhone": null,#推送电话
 "buyerEmail": null,#推送邮箱
 "buyerAddress": null,
 "buyerAccount": null,
 "buyerAccountName": null,
 "salerCompany": null,
 "goodsInfo": null,
 "invoiceType": null,
 "submitReturnApi": null,
 "remark": null,
 "invoiceRemark": null,#发票备注信息用来单独存
 "buyerTel": null #买方电话，用于显示发票备注信息。
 }
 ],
 "annexList":[ 
 {
 "name":null,
 "src":null
 }
 ]
}

##  其中新增两个字段：

 -" invoiceRemark": null,#发票备注信息用来单独存
 -  "buyerTel": null #买方电话。

 需要将这两个字段，增加到最终提交到OA的请求体当中（值可以为空，但健名必须存在），而且顺序位置不能变化，否则签名会失败。