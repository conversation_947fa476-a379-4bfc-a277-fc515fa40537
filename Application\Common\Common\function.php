<?php

/**
 * 邮件发送函数
 */

function sendMail($to, $title, $content)
{

    Vendor('PHPMailer.PHPMailerAutoload');

    $mail = new PHPMailer(); //实例化

    $mail->IsSMTP(); // 启用SMTP

    $mail->Host = C('MAIL_HOST'); //smtp服务器的名称（这里以QQ邮箱为例）

    $mail->SMTPAuth = C('MAIL_SMTPAUTH'); //启用smtp认证

    $mail->Username = C('MAIL_USERNAME'); //你的邮箱名

    $mail->Password = C('MAIL_PASSWORD'); //邮箱密码

    $mail->From = C('MAIL_FROM'); //发件人地址（也就是你的邮箱地址）

    $mail->FromName = C('MAIL_FROMNAME'); //发件人姓名

    $mail->AddAddress($to, "Hello Do You Love ME?");

    $mail->WordWrap = 50; //设置每行字符长度

    $mail->IsHTML(C('MAIL_ISHTML')); // 是否HTML格式邮件

    $mail->CharSet = C('MAIL_CHARSET'); //设置邮件编码

    $mail->Subject = $title; //邮件主题

    $mail->Body = $content; //邮件内容

    $mail->AltBody = "这是一个纯文本的HTML电子邮件客户端"; //邮件正文不支持HTML的备用显示

    return ($mail->Send());
}

/**
 * 发送邮件
 * @param  string $address 需要发送的邮箱地址 发送给多个地址需要写成数组形式
 * @param  string $subject 标题
 * @param  string $content 内容
 * @return boolean       是否成功
 */

function send_email($address, $subject, $content)
{

    $email_smtp = C('EMAIL_SMTP');

    $email_username = C('EMAIL_USERNAME');

    $email_password = C('EMAIL_PASSWORD');

    $email_from_name = C('EMAIL_FROM_NAME');

    $email_smtp_secure = C('EMAIL_SMTP_SECURE');

    $email_port = C('EMAIL_PORT');

    if (empty($email_smtp) || empty($email_username) || empty($email_password) || empty($email_from_name)) {

        return array("error" => 1, "message" => '邮箱配置不完整');
    }

    require_once './ThinkPHP/Library/Org/Nx/class.phpmailer.php';

    require_once './ThinkPHP/Library/Org/Nx/class.smtp.php';

    $phpmailer = new \Phpmailer();

    // 设置PHPMailer使用SMTP服务器发送Email

    $phpmailer->IsSMTP();

    // 设置设置smtp_secure

    $phpmailer->SMTPSecure = $email_smtp_secure;

    // 设置port

    $phpmailer->Port = $email_port;

    // 设置为html格式

    $phpmailer->IsHTML(true);

    // 设置邮件的字符编码'

    $phpmailer->CharSet = 'UTF-8';

    // 设置SMTP服务器。

    $phpmailer->Host = $email_smtp;

    // 设置为"需要验证"

    $phpmailer->SMTPAuth = true;

    // 设置用户名

    $phpmailer->Username = $email_username;

    // 设置密码

    $phpmailer->Password = $email_password;

    // 设置邮件头的From字段。

    $phpmailer->From = $email_username;

    // 设置发件人名字

    $phpmailer->FromName = $email_from_name;

    // 添加收件人地址，可以多次使用来添加多个收件人

    if (is_array($address)) {

        foreach ($address as $addressv) {

            $phpmailer->AddAddress($addressv);
        }
    } else {

        $phpmailer->AddAddress($address);
    }

    // 设置邮件标题

    $phpmailer->Subject = $subject;

    // 设置邮件正文

    $phpmailer->Body = $content;

    // 发送邮件。

    if (!$phpmailer->Send()) {

        $phpmailererror = $phpmailer->ErrorInfo;

        return array("error" => 1, "message" => $phpmailererror);
    } else {

        return array("error" => 0);
    }
}

function get_event($cid)
{

    //$db = D( 'V9Conference' );

    //$info1 = $db1->where( $sql )->find();

    echo $cid;
}

//记录来源网址

function get_from_url()
{

    return $_SERVER['HTTP_REFERER'];
}

//根据id显示tarck
function show_track($id)
{
    $db = M('SubTrack');
    $sql['id'] = $id;
    $info = $db->where($sql)->find();
    if (!$info) {
        return "N/A";
    } else {
        return $info['track'];
    }
}

//删除指定的数组元素

function delByValue($arr, $value)
{

    if (!is_array($arr)) {

        return $arr;
    }

    foreach ($arr as $k => $v) {

        if ($v == $value) {

            unset($arr[$k]);
        }
    }

    return $arr;
}


function set_paper_id($cid, $id)
{
    $db = D('SubPaper');
    $sql['cid'] = $cid;
    // 统计当前cid下所有记录的数量，包括字符串类型的paper_id
    $totalRecords = $db->where($sql)->count();
    $data['paper_id'] = $totalRecords;
    $data['id'] = $id;
    // 保存更新
    $db->save($data);
}
//删除数组中的空

function array_no_empty($arr)
{

    if (is_array($arr)) {

        foreach ($arr as $k => $v) {

            if (empty($v) && $v !== 0) {
                unset($arr[$k]);
            } elseif (is_array($v)) {

                $t = array_no_empty($v);

                if ($t) {
                    $arr[$k] = $t;
                } else {
                    unset($arr[$k]);
                }
            }
        }
    }

    return $arr;
}

//获取支付的币种

function money_type($regid)
{

    $db = D('SubPay');

    $sql['regid'] = $regid;

    $info = $db->where($sql)->find();

    if (\Common\Lib\CurrencyTypeConstants::isUSD($info['moneytype'])) {

        echo 'USD';
    } else if (\Common\Lib\CurrencyTypeConstants::isCNY($info['moneytype'])) {

        echo 'CNY';
    }
}

//获取支付信息

function pay_info($regid, $w)
{

    $db = D('SubPay');

    $sql['regid'] = $regid;

    $info = $db->where($sql)->find();

    return $info[$w];
}

/**
 * cookie加密、解密函数
 * @param   string  $txt        字符串
 * @param   string  $operation  ENCODE为加密，DECODE为解密，可选参数
 * @param   string  $key        密钥：数字、字母、下划线
 * @param   string  $expiry     过期时间
 * @return  string
 */

function str_auth($string, $operation = 'ENCODE', $key = '', $expiry = 0)
{
    $cipher = "AES-256-CBC";
    $ivlen = openssl_cipher_iv_length($cipher);
    $key = md5($key != '' ? $key : 'CDYajy'); // 加密秘钥

    if ($operation == 'ENCODE') {
        $expiry_time = $expiry ? $expiry + time() : 0;
        $string = sprintf('%010d', $expiry_time) . $string;
        $iv = openssl_random_pseudo_bytes($ivlen);
        $encrypted = openssl_encrypt($string, $cipher, $key, OPENSSL_RAW_DATA, $iv);
        return base64_encode($iv . $encrypted);
    } else {
        $data = base64_decode($string);
        $iv_dec = substr($data, 0, $ivlen);
        $encrypted_data = substr($data, $ivlen);
        $decrypted = openssl_decrypt($encrypted_data, $cipher, $key, OPENSSL_RAW_DATA, $iv_dec);
        if ($decrypted === false) {
            return '';
        }
        $expiry_time = substr($decrypted, 0, 10);
        if ((int)$expiry_time == 0 || (int)$expiry_time - time() > 0) {
            return substr($decrypted, 10);
        } else {
            return '';
        }
    }
}

function clearBlank($arr)
{
    function odd($var)
    {
        return ($var != ''); //return true or false
    }
    return (array_filter($arr, "odd"));
}

function processValues($values, &$hmacSource)
{
    foreach ($values as $value) {
        if (is_array($value) || is_object($value)) {
            if (is_object($value)) {
                $arrayValue = array_filter((array) $value);
            } else {
                $arrayValue = $value;
            }
            ksort($arrayValue);
            processValues($arrayValue, $hmacSource);
        } else {
            $hmacSource .= trim($value) . '#';
        }
    }
}
function loadMerchantInfo()
{
    $merchantInfo = C('merchant_info');
    return $merchantInfo;
}

//获取商户信息
function MerchantInfo($merchantId)
{
    $path = CONF_PATH . 'merchant_info.php';
    $merchantInfo = include $path;

    if (isset($merchantInfo[$merchantId])) {
        return $merchantInfo[$merchantId];
    } else {
        return null; // 如果未找到对应终端号的商户信息，可以返回 null 或者其他适当的值
    }
}

//判断付款金额，只能为两位数的小数
function validateAmount($amount)
{
    $regex = '/^(0(\.(\d{1,2})?)?|[1-9]\d{0,4}(\.\d{1,2})?)$/';

    if (!preg_match($regex, $amount) || $amount < 0.01) {
        die('The payment amount must be greater than 0 and between 0.01 and 50000, including up to two decimal places');
    }
}
function getInvoiceStatus($status, $audit_details = null)
{
    // 从配置文件获取状态映射
    $statusText = C('INVOICE_STATUS_TEXT');
    $statusClass = C('INVOICE_STATUS_CLASS');

    // 如果配置未加载，使用默认映射
    if (empty($statusText) || empty($statusClass)) {
        $statusText = array(
            '-1'  => '审核被拒',
            '1'   => '等待审核',
            '2'   => '等待开票',
            '20'  => '开票成功',
            '-20' => '发票冲红',
            '30'  => '开票失败'
        );
        $statusClass = array(
            '-1'  => 'btn btn-danger',
            '1'   => 'btn btn-warning',
            '2'   => 'btn btn-info',
            '20'  => 'btn btn-success',
            '-20' => 'btn btn-danger',
            '30'  => 'btn btn-danger'
        );
    }

    $result = array(
        'text' => isset($statusText[$status]) ? $statusText[$status] : '未申请',
        'class' => isset($statusClass[$status]) ? $statusClass[$status] : 'btn btn-secondary'
    );

    // 如果是审核被拒且有审核详情，添加tooltip
    if ($status == -1 && !empty($audit_details)) {
        $details = json_decode($audit_details, true);
        if ($details && isset($details['reason'])) {
            $result['text'] .= ' <i class="fas fa-question-circle" style="cursor:pointer" data-bs-toggle="tooltip" title="' . htmlspecialchars($details['reason']) . '"></i>';
            $result['reason'] = $details['reason'];
        }
    }

    return $result;
}
// 判断会议简称（evnet)的格式 ：
function validateEvent($event)
{
    // 使用正则表达式验证格式：以一个或多个大写字母或数字开头，后跟4位数字
    $pattern = '/^[A-Z0-9]+[0-9]{4}$/';
    return (bool)preg_match($pattern, $event);
}
//判断paper ID 的格式
function validatePaperId($paperId)
{
    $pattern = '/^([0-9]+|[A-Z]+[0-9]+|[A-Z]+[0-9]+-[0-9]+|[A-Z]+[0-9]+-[A-Z]+|[A-Z]+[0-9]+-[A-Z]+[0-9]+|[A-Z]+-[0-9]+-[A-Z])(,([0-9]+|[A-Z]+[0-9]+|[A-Z]+[0-9]+-[0-9]+|[A-Z]+[0-9]+-[A-Z]+|[A-Z]+[0-9]+-[A-Z]+[0-9]+|[A-Z]+-[0-9]+-[A-Z]))*$/';
    return (bool)preg_match($pattern, $paperId);
}
//判断邮箱格式，如果失败返回false
function validateEmail($email)
{
    return filter_var($email, FILTER_VALIDATE_EMAIL) ? true : false;
}
//判断手机号格式，如果失败返回false
function validatePhone($phone)
{
    return preg_match('/^1[3-9]\d{9}$/', $phone) ? true : false;
}
/**
 * 格式化文件大小
 * @param int $bytes
 * @return string
 */
function format_bytes($bytes)
{
    if ($bytes >= 1073741824) {
        $bytes = round($bytes / 1073741824 * 100) / 100 . 'GB';
    } elseif ($bytes >= 1048576) {
        $bytes = round($bytes / 1048576 * 100) / 100 . 'MB';
    } elseif ($bytes >= 1024) {
        $bytes = round($bytes / 1024 * 100) / 100 . 'KB';
    } else {
        $bytes = $bytes . 'Bytes';
    }
    return $bytes;
}

/**
 * 根据会议 ID 和货币类型获取银行账户信息
 * @param int $cid 会议 ID
 * @param int $currency 货币类型（0-CNY，1-USD）
 * @return array 银行账户列表，包含 id、nickname、oa_dict 等字段
 */
function getBankInfoByCidAndCurrency($cid, $currency = null)
{
    // 参数验证
    if (empty($cid)) {
        return [];
    }

    // 获取会议信息
    $conferenceModel = M('SubConference');
    $conference = $conferenceModel->where(['id' => $cid])->find();

    if (empty($conference)) {
        return [];
    }

    // 获取银行账户信息
    $bankModel = M('SubBank');
    $bankList = [];

    // 根据货币类型获取相应的银行账户
    if ($currency === \Common\Lib\CurrencyTypeConstants::CURRENCY_USD || $currency === (string)\Common\Lib\CurrencyTypeConstants::CURRENCY_USD || $currency === 'USD') {
        // USD - 外币账户
        if (!empty($conference['bank_en'])) {
            $bankInfo = $bankModel->find(intval($conference['bank_en']));
            if ($bankInfo) {
                $bankList[] = $bankInfo;
            }
        }
    } else if ($currency === \Common\Lib\CurrencyTypeConstants::CURRENCY_CNY || $currency === (string)\Common\Lib\CurrencyTypeConstants::CURRENCY_CNY || $currency === 'CNY') {
        // CNY - 人民币账户
        if (!empty($conference['bank_cn'])) {
            $bankInfo = $bankModel->find(intval($conference['bank_cn']));
            if ($bankInfo) {
                $bankList[] = $bankInfo;
            }
        }
    } else {
        // 如果未指定货币类型，返回所有可用账户
        if (!empty($conference['bank_cn'])) {
            $bankInfo = $bankModel->find(intval($conference['bank_cn']));
            if ($bankInfo) {
                $bankList[] = $bankInfo;
            }
        }

        if (!empty($conference['bank_en'])) {
            $bankInfo = $bankModel->find(intval($conference['bank_en']));
            if ($bankInfo) {
                $bankList[] = $bankInfo;
            }
        }
    }

    // 在返回银行账户信息前进行数据清理，移除所有换行符和回车符
    if (!empty($bankList)) {
        foreach ($bankList as &$bank) {
            if (isset($bank['bank']) && is_string($bank['bank'])) {
                // 清理bank字段中的特殊字符
                $bank['bank'] = trim(str_replace(array("\r", "\n", "\t"), '', $bank['bank']));
            }
            if (isset($bank['nickname']) && is_string($bank['nickname'])) {
                // 清理nickname字段中的特殊字符
                $bank['nickname'] = trim(str_replace(array("\r", "\n", "\t"), '', $bank['nickname']));
            }
            // 清理其他可能包含文本的字段
            if (isset($bank['oa_dict']) && is_string($bank['oa_dict'])) {
                $bank['oa_dict'] = trim(str_replace(array("\r", "\n", "\t"), '', $bank['oa_dict']));
            }
        }
        unset($bank); // 释放引用
    }
    
    return $bankList;
}

/**
 * 从线上支付订单号中提取商户信息
 * @param string $orderId 订单号，格式如：********-*********-************-IC-US
 * @return array|商户信息数组，包含 name、terminalNo、tax_num、company、api_com_id、oa_dict 等字段
 */
function getCompanyInfoFromOrderId($orderId)
{
    // 参数验证
    if (empty($orderId)) {
        \Think\Log::write('订单号为空，无法提取商户信息', 'ERROR');
        return null;
    }

    // 解析订单号获取商户号
    $parts = explode('-', $orderId);
    $merchantId = isset($parts[1]) ? $parts[1] : '';

    if (empty($merchantId)) {
        \Think\Log::write('无法从订单号解析商户ID: ' . $orderId, 'ERROR');
        return null;
    }

    \Think\Log::write('从订单号解析商户ID: ' . $merchantId, 'INFO');

    // 加载商户配置
    $merchantConfig = loadMerchantInfo();
    if (empty($merchantConfig)) {
        \Think\Log::write('商户配置为空，无法获取商户信息', 'ERROR');
        return null;
    }

    // 检查商户ID是否存在于配置中
    if (isset($merchantConfig[$merchantId])) {
        $merchantInfo = $merchantConfig[$merchantId];
        \Think\Log::write('找到商户配置: ' . json_encode($merchantInfo, JSON_UNESCAPED_UNICODE), 'INFO');
        return $merchantInfo;
    } else {
        \Think\Log::write('未找到商户ID对应配置: ' . $merchantId, 'ERROR');
        return null;
    }
}

/**
 * 根据文件类型获取对应的Font Awesome图标类名
 * @param string $fileType 文件类型
 * @return string 图标类名
 */
function get_file_icon($fileType)
{
    // 转换为小写
    $fileType = strtolower($fileType);

    // 图标映射
    $iconMap = array(
        // 文档类
        'doc' => 'far fa-file-word',
        'docx' => 'far fa-file-word',
        'pdf' => 'far fa-file-pdf',
        'txt' => 'far fa-file-alt',
        'rtf' => 'far fa-file-alt',

        // 图片类
        'jpg' => 'far fa-file-image',
        'jpeg' => 'far fa-file-image',
        'png' => 'far fa-file-image',
        'gif' => 'far fa-file-image',
        'bmp' => 'far fa-file-image',

        // 压缩包类
        'zip' => 'far fa-file-archive',
        'rar' => 'far fa-file-archive',
        '7z' => 'far fa-file-archive',
        'tar' => 'far fa-file-archive',
        'gz' => 'far fa-file-archive',

        // 表格类
        'xls' => 'far fa-file-excel',
        'xlsx' => 'far fa-file-excel',
        'csv' => 'far fa-file-excel',

        // 演示文稿类
        'ppt' => 'far fa-file-powerpoint',
        'pptx' => 'far fa-file-powerpoint',

        // 代码类
        'html' => 'far fa-file-code',
        'htm' => 'far fa-file-code',
        'css' => 'far fa-file-code',
        'js' => 'far fa-file-code',
        'php' => 'far fa-file-code',

        // 音视频类
        'mp3' => 'far fa-file-audio',
        'wav' => 'far fa-file-audio',
        'mp4' => 'far fa-file-video',
        'avi' => 'far fa-file-video',
        'mov' => 'far fa-file-video'
    );

    return isset($iconMap[$fileType]) ? $iconMap[$fileType] : 'far fa-file';
}

/**
 * 获取货币代码的文本表示
 * @param mixed $currency 货币代码
 * @return string 货币文本
 */
function getCurrencyText($currency) {
    // 如果是数字，转换为文本
    if ($currency === \Common\Lib\CurrencyTypeConstants::CURRENCY_CNY || $currency === (string)\Common\Lib\CurrencyTypeConstants::CURRENCY_CNY) {
        return 'CNY';
    } else if ($currency === \Common\Lib\CurrencyTypeConstants::CURRENCY_USD || $currency === (string)\Common\Lib\CurrencyTypeConstants::CURRENCY_USD) {
        return 'USD';
    }

    // 如果已经是文本，直接返回
    if ($currency === 'CNY' || $currency === 'USD') {
        return $currency;
    }

    // 默认返回原始值
    return $currency;
}

/**
 * 获取转账状态文本
 * @param int $status 状态码
 * @return string 状态文本
 */
function getTransferStatusText($status) {
    return \Common\Lib\TransferStatusConstants::getStatusText($status);
}

/**
 * 获取转账状态CSS类
 * @param int $status 状态码
 * @return string 状态CSS类
 */
function getTransferStatusClass($status) {
    $class = \Common\Lib\TransferStatusConstants::getStatusClass($status);
    return 'label label-' . $class;
}

/**
 * 获取发票状态文本
 * @param int $status 状态码
 * @return string 状态文本
 */
function get_invoice_status_text($status) {
    return \Common\Lib\InvoiceStatusConstants::getStatusText($status);
}

/**
 * 获取发票状态CSS类
 * @param int $status 状态码
 * @return string 状态CSS类
 */
function get_invoice_status_class($status) {
    return \Common\Lib\InvoiceStatusConstants::getStatusClass($status);
}

