<extend name="M<PERSON>el@Base/admin_base" />
<block name="title">
    <title>（线下转账）确认注册与付款信息</title>
</block>

<block name="head">
    <!-- 使用原生HTML5日期选择器 -->
</block>

<block name="breadcrumb">
    <ol class="breadcrumb">
        <li><a href="{:U('Mpanel/Index/index')}"><i class="fa fa-home"></i> 首页</a></li>
        <li class="active"><i class="fa fa-link"></i> 线下查账与开票</li>
    </ol>
</block>

<block name="content">
    <style>
        .section-title {
            border-bottom: 2px solid #5cb85c;
            padding-bottom: 8px;
            margin-bottom: 20px;
            color: #333;
            font-weight: 600;
        }
        
        /* 转账凭证缩略图样式 */
        .receipt-thumbnail-container {
            position: relative;
            display: inline-block;
            margin-top: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .receipt-thumbnail-container:hover {
            box-shadow: 0 3px 6px rgba(0,0,0,0.2);
            transform: translateY(-2px);
        }
        
        .receipt-thumbnail {
            display: block;
            position: relative;
        }
        
        .receipt-img {
            display: block;
            transition: all 0.3s ease;
        }
        
        .receipt-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            color: white;
            opacity: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .receipt-overlay i {
            margin-right: 5px;
        }
        
        .receipt-thumbnail:hover .receipt-overlay {
            opacity: 1;
        }

        .form-section {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .payment-amount {
            color: #d9534f;
            font-weight: bold;
            font-size: 18px;
        }

        .next-step-btn {
            margin-top: 25px;
        }

        .edit-button {
            background-color: #5cb85c;
            color: white;
            font-size: 14px;
            padding: 8px 15px;
            border-radius: 4px;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .edit-button:hover {
            background-color: #4cae4c;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .key-field {
            font-weight: 600;
            color: #333;
        }

        .value-field {
            font-size: 15px;
        }
    </style>
    <link rel="stylesheet" href="/Public/statics/css/steps_admin_enhanced.css">

    <div class="container-fluid">
        <!-- 步骤指示器 -->
        <div class="row">
            <div class="col-md-12">
                <ul class="admin-stepped-progress">
                    <li class="admin-current"><span>确认注册与转账信息</span></li>
                    <li><span>确认查账和发票信息</span></li>
                    <li><span>提交到OA</span></li>
                </ul>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-check-circle"></i> 确认注册和转账信息</h3>
                    </div>
                    <div class="panel-body">
                        <!-- 操作说明 -->
                        <div class="alert alert-info" style="border-left: 4px solid #5bc0de; border-radius: 0;">
                            <p><i class="fa fa-info-circle fa-lg"></i>
                                请确认以下注册和转账信息。如需修改会议简称或文章ID，请点击右上角的<strong>"修改"</strong>按钮。</p>
                        </div>

                        <!-- 注册信息 -->
                        <h4 class="section-title">
                            <i class="fa fa-user"></i> 注册信息
                            <button type="button" class="edit-button pull-right" onclick="showEditModal()">
                                <i class="fa fa-edit"></i> 修改会议简称和文章ID
                            </button>
                        </h4>
                        <div class="form-section">
                            <!-- 基本信息 -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">会议简称：</label>
                                        <p class="form-control-static value-field">{$registerInfo.event}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">文章ID：</label>
                                        <p class="form-control-static value-field">{$registerInfo.paperid}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">注册人：</label>
                                        <p class="form-control-static value-field">
                                            <notempty name="registerInfo.username">
                                                {$registerInfo.username}
                                            <else />
                                                {$registerInfo.firstname} {$registerInfo.middlename} {$registerInfo.lastname}
                                            </notempty>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">单位：</label>
                                        <p class="form-control-static value-field">
                                            <notempty name="registerInfo.company">
                                                {$registerInfo.company}
                                            <else />
                                                {$registerInfo.affiliation}
                                            </notempty>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">注册邮箱：</label>
                                        <p class="form-control-static value-field">{$registerInfo.email}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">注册时间：</label>
                                        <p class="form-control-static value-field">{$registerInfo.register_time}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 费用明细 -->
                            <div class="fee-details-section" style="margin-top: 20px; border-top: 2px dashed #5cb85c; padding-top: 15px;">
                                <h5 style="font-weight: 600; color: #333; margin-bottom: 15px;">
                                    <i class="fa fa-money" style="color: #5cb85c;"></i> 注册费用明细
                                </h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>项目</th>
                                                <th>数量</th>
                                                <th>美元</th>
                                                <notempty name="registerInfo.total.cny">
                                                    <th>人民币</th>
                                                </notempty>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <notempty name="registerInfo.type">
                                                <volist name="registerInfo.type" id="t">
                                                    <if condition="$t[no] egt 1">
                                                        <tr class="success">
                                                            <th scope="row">{$t.name}</th>
                                                            <td>{$t.no}</td>
                                                            <td>${$t.usd}</td>
                                                            <notempty name="registerInfo.total.cny">
                                                                <td>¥{$t.cny}</td>
                                                            </notempty>
                                                        </tr>
                                                    </if>
                                                </volist>
                                            </notempty>

                                            <notempty name="registerInfo.extras">
                                                <volist name="registerInfo.extras" id="e">
                                                    <if condition="$e[no] egt 1">
                                                        <tr class="info">
                                                            <th scope="row">{$e.name}</th>
                                                            <td>{$e.no}</td>
                                                            <td>${$e.usd}</td>
                                                            <notempty name="registerInfo.total.cny">
                                                                <td>¥{$e.cny}</td>
                                                            </notempty>
                                                        </tr>
                                                    </if>
                                                </volist>
                                            </notempty>

                                            <notempty name="registerInfo.other.num">
                                                <tr class="warning">
                                                    <th>额外付款：<a tabindex="0" role="button" data-toggle="popover" data-trigger="focus" data-content="{$registerInfo.other.name}"><i class="fa fa-question-circle"></i></a></th>
                                                    <td>-</td>
                                                    <if condition="$registerInfo.other[type] eq usd">
                                                        <td>${$registerInfo.other.num}</td>
                                                    </if>
                                                    <if condition="$registerInfo.other[type] eq cny">
                                                        <td>¥{$registerInfo.other.num}</td>
                                                    </if>
                                                </tr>
                                            </notempty>

                                            <tr class="danger">
                                                <th>合计</th>
                                                <td>-</td>
                                                <td class="amount"><b>${$registerInfo.total.usd}</b></td>
                                                <notempty name="registerInfo.total.cny">
                                                    <td class="amount"><b>¥{$registerInfo.total.cny}</b></td>
                                                </notempty>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 转账信息 -->
                        <h4 class="section-title">
                            <i class="fa fa-credit-card"></i> 转账信息
                            <!-- 管理员修改转账信息按钮 -->
                            <button type="button" class="btn btn-primary btn-sm pull-right" onclick="showEditTransferModal()" style="margin-top: -5px;">
                                <i class="fa fa-edit"></i> 修改转账信息
                            </button>
                        </h4>
                        <div class="form-section">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">转账人：</label>
                                        <p class="form-control-static value-field">{$transferInfo.transfer_name}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">转账时间：</label>
                                        <p class="form-control-static value-field">
                                            {$displayTransferTime}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">转账账号：</label>
                                        <p class="form-control-static value-field">{$transferInfo.transfer_account|default="未提供"}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">收款账户：</label>
                                        <p class="form-control-static value-field">
                                            <if condition="!empty($receiverInfo)">
                                                {$receiverInfo.bank_name|default=""} - {$receiverInfo.account_name|default=""}
                                            <else />
                                                <if condition="$transferInfo.currency eq 0">
                                                    人民币收款账户
                                                <elseif condition="$transferInfo.currency eq 1" />
                                                    美元收款账户
                                                <else />
                                                    未指定
                                                </if>
                                            </if>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">货币类型：</label>
                                        <p class="form-control-static value-field">
                                            {$currencyLabel}
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label key-field">转账金额：</label>
                                        <p class="form-control-static payment-amount">
                                            {$formattedAmount}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 转账凭证 -->
                            <div class="row" style="margin-top: 15px;">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label key-field"><i class="fa fa-file-image-o"></i> 转账凭证：</label>
                                        {$receiptHtml}
                                    </div>
                                </div>
                            </div>
                            
                            {$remarkHtml}
                        </div>

                        <!-- 金额差异提示 -->
                        <if condition="$amountComparisonData['amountMismatch']">
                            <div class="alert alert-danger" style="border-left: 4px solid #d9534f; border-radius: 0; margin: 15px 0; font-size: 15px;">
                                <p><i class="fa fa-exclamation-circle fa-lg"></i> <strong>金额不符提示：</strong></p>
                                <p>作者填写的付款金额与系统生成的注册金额不符合，请注意核实。</p>
                                <ul style="margin-top: 10px; padding-left: 20px;">
                                    <li>作者填写的转账金额：<strong>{$amountComparisonData.currencySymbol}{$amountComparisonData.transferAmount} {$amountComparisonData.currencyName}</strong></li>
                                    
                                    <if condition="$amountComparisonData['hasFee']">
                                        <!-- 美元汇款显示预期金额（含手续费） -->
                                        <li>系统生成的注册金额：<strong>{$amountComparisonData.currencySymbol}{$amountComparisonData.registrationAmount} {$amountComparisonData.currencyName}</strong></li>
                                        <li>手续费：<strong>{$amountComparisonData.currencySymbol}{$amountComparisonData.feeAmount} {$amountComparisonData.currencyName}</strong></li>
                                        <li>预期金额（含手续费）：<strong>{$amountComparisonData.currencySymbol}{$amountComparisonData.expectedAmount} {$amountComparisonData.currencyName}</strong></li>
                                        <li>差额：<strong>{$amountComparisonData.currencySymbol}{$amountComparisonData.difference} {$amountComparisonData.currencyName}</strong></li>
                                    <else/>
                                        <!-- 人民币汇款正常显示 -->
                                        <li>系统生成的注册金额：<strong>{$amountComparisonData.currencySymbol}{$amountComparisonData.registrationAmount} {$amountComparisonData.currencyName}</strong></li>
                                        <li>差额：<strong>{$amountComparisonData.currencySymbol}{$amountComparisonData.difference} {$amountComparisonData.currencyName}</strong></li>
                                    </if>
                                </ul>
                            </div>
                        </if>

                        {$currencyWarning}

                        <!-- 操作按钮 -->
                        <div class="row next-step-btn">
                            <div class="col-md-12 text-center">
                                <!-- 根据转账状态显示不同内容 -->
                                <if condition="$transferInfo['status'] eq 40"> <!-- 已驳回状态 -->
                                    <div class="alert alert-danger" style="border-left: 4px solid #d9534f; border-radius: 0; text-align: left;">
                                        <h4><i class="fa fa-times-circle"></i> 转账申请已驳回</h4>
                                        <p>该转账申请已被驳回，请联系用户根据驳回原因修改后重新提交。</p>
                                        <if condition="!empty($transferInfo['refusal'])">
                                            <div class="well well-sm" style="margin-top: 10px; background-color: #f8f8f8;">
                                                <strong>驳回原因：</strong>{$transferInfo.refusal}
                                            </div>
                                        </if>
                                        <div class="text-right" style="margin-top: 15px;">
                                            <button id="deleteTransferBtn" data-id="{$transferInfo.id}" data-reg-id="{$registerInfo.id}" class="btn btn-danger">
                                                <i class="fa fa-trash"></i> 删除转账信息
                                            </button>
                                        </div>
                                    </div>
                                <else />
                                    <!-- 驳回按钮 -->
                                    <if condition="$transferInfo['status'] eq 0">
                                        <button id="rejectTransferBtn" data-id="{$transferInfo.id}" class="btn btn-danger btn-lg" style="margin-right: 15px; padding: 12px 30px; font-size: 16px;">
                                            <i class="fa fa-times-circle"></i> 驳回转账申请
                                        </button>
                                    </if>
                                    
                                    <!-- 下一步按钮 -->
                                    <a href="{:U('OA/OfflineChain/index', array('reg_id' => $registerInfo['id']))}" 
                                        class="btn btn-success btn-lg" style="padding: 12px 30px; font-size: 16px;">
                                        <i class="fa fa-arrow-right"></i> 下一步：确认查账和发票信息
                                    </a>
                                </if>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 修改转账信息模态框 -->
    <div class="modal fade" id="editTransferModal" tabindex="-1" role="dialog" aria-labelledby="editTransferModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #337ab7; color: white;">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="editTransferModalLabel"><i class="fa fa-edit"></i> 修改转账信息</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info" style="font-size: 13px; border-left: 3px solid #5bc0de;">
                        <i class="fa fa-info-circle"></i> 您正在修改作者提交的转账信息，请确保修改后的信息准确无误。
                    </div>
                    <form id="editTransferForm">
                        <input type="hidden" id="transfer_id" name="transfer_id" value="{$transferInfo.id}">
                        <input type="hidden" id="reg_id" name="reg_id" value="{$registerInfo.id}">
                        
                        <div class="form-group">
                            <label for="transfer_name"><i class="fa fa-user"></i> 转账人</label>
                            <input type="text" class="form-control" id="transfer_name" name="transfer_name" value="{$transferInfo.transfer_name}" placeholder="请输入转账人姓名">
                        </div>
                        
                        <div class="form-group">
                            <label for="transfer_account"><i class="fa fa-credit-card"></i> 转账账号</label>
                            <input type="text" class="form-control" id="transfer_account" name="transfer_account" value="{$transferInfo.transfer_account}" placeholder="请输入转账账号">
                        </div>
                        
                        <div class="form-group">
                            <label for="transfer_time"><i class="fa fa-calendar"></i> 转账时间</label>
                            <input type="date" class="form-control" id="transfer_time" name="transfer_time" value="{$formattedTransferTime}" placeholder="请选择转账时间">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="currency"><i class="fa fa-money"></i> 货币类型</label>
                                    <select class="form-control" id="currency" name="currency">
                                        <option value="0" <if condition="$transferInfo.currency eq 0">selected</if>>人民币 (CNY)</option>
                                        <option value="1" <if condition="$transferInfo.currency eq 1">selected</if>>美元 (USD)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="total"><i class="fa fa-calculator"></i> 转账金额</label>
                                    <input type="text" class="form-control" id="total" name="total" value="{$transferInfo.total}" placeholder="请输入转账金额">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="remark"><i class="fa fa-comment"></i> 备注信息</label>
                            <textarea class="form-control" id="remark" name="remark" rows="3" placeholder="输入备注信息">{$transferInfo.remark}</textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><i class="fa fa-times"></i> 取消</button>
                    <button type="button" class="btn btn-primary" id="saveTransferBtn"><i class="fa fa-save"></i> 保存修改</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 转账凭证模态框 -->
    <div class="modal fade" id="receiptModal" tabindex="-1" role="dialog" aria-labelledby="receiptModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #5bc0de; color: white;">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="receiptModalLabel"><i class="fa fa-file-image-o"></i> 转账凭证详情</h4>
                </div>
                <div class="modal-body text-center">
                    <img id="receiptImage" src="" alt="转账凭证" class="img-responsive" style="max-width: 100%; margin: 0 auto;">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal"><i class="fa fa-times"></i> 关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 修改文章ID和会议简称的模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #5cb85c; color: white;">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                        style="color: white; opacity: 0.8;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="editModalLabel"><i class="fa fa-edit"></i> 修改文章ID和会议简称</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info" style="font-size: 13px; border-left: 3px solid #5bc0de;">
                        <i class="fa fa-info-circle"></i> 修改这些信息将同时更新多个相关表的数据，包括注册信息、转账信息和发票信息。
                    </div>
                    <form id="editForm">
                        <div class="form-group">
                            <label for="paper_id"><i class="fa fa-file-text-o"></i> 文章ID</label>
                            <input type="text" class="form-control" id="paper_id" name="paper_id"
                                value="{$registerInfo.paperid}" placeholder="请输入新的文章ID">
                            <p class="help-block"><small>格式：字母、数字、连字符组成，多个ID用逗号分隔</small></p>
                        </div>
                        <div class="form-group">
                            <label for="event"><i class="fa fa-calendar"></i> 会议简称</label>
                            <input type="text" class="form-control" id="event" name="event"
                                value="{$registerInfo.event}" placeholder="请输入新的会议简称">
                            <p class="help-block"><small>格式：字母、数字、连字符加四位年份，多个简称用逗号分隔</small></p>
                        </div>
                        <input type="hidden" id="reg_id" name="reg_id" value="{$registerInfo.id}">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="saveChangesBtn"><i class="fa fa-save"></i>
                        保存修改</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示转账凭证模态框
        function showReceiptModal(imageUrl) {
            // 图片URL已经包含了ATTACHMENT_URL前缀
            $('#receiptImage').attr('src', imageUrl);
            $('#receiptModal').modal('show');
            
            // 添加图片加载错误处理
            $('#receiptImage').on('error', function() {
                $(this).attr('src', '/Public/images/receipt-error.png');
                Swal.fire({
                    icon: 'warning',
                    title: '图片加载失败',
                    text: '无法加载转账凭证图片，请检查图片路径是否正确',
                    confirmButtonText: '确定'
                });
            });
        }
        
        // 显示修改转账信息模态框
        function showEditTransferModal() {
            // 确保转账时间输入框为日期选择器
            $('#transfer_time').attr('type', 'date');
            
            // 获取原始时间戳值（数据库中存储的是时间戳）
            var timestampValue = parseInt('{$transferInfo.transfer_time}');
            console.log('原始时间戳值：' + timestampValue);
            
            if (!isNaN(timestampValue) && timestampValue > 0) {
                // 将时间戳转换为日期对象
                var date = new Date(timestampValue * 1000); // 转换为毫秒
                var year = date.getFullYear();
                var month = (date.getMonth() + 1).toString().padStart(2, '0');
                var day = date.getDate().toString().padStart(2, '0');
                var formattedDate = year + '-' + month + '-' + day;
                
                // 设置输入框的值
                $('#transfer_time').val(formattedDate);
                console.log('转换后的日期值：' + formattedDate);
            } else {
                // 如果时间戳无效，尝试使用PHP格式化的日期
                var formattedDate = '{$formattedTransferTime}';
                
                if (formattedDate && formattedDate.trim() !== '') {
                    $('#transfer_time').val(formattedDate);
                    console.log('使用PHP格式化的日期：' + formattedDate);
                } else {
                    // 获取当前输入框中的日期值
                    var currentValue = $('#transfer_time').val();
                    
                    if (currentValue && currentValue.trim() !== '') {
                        // 如果已经有值，检查格式是否正确
                        if (currentValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
                            // 已经是正确的格式，不需要处理
                            console.log('日期已经是正确格式：' + currentValue);
                        } else {
                            // 尝试将其他格式的日期转换为YYYY-MM-DD
                            try {
                                var date = new Date(currentValue);
                                if (!isNaN(date.getTime())) {
                                    var year = date.getFullYear();
                                    var month = (date.getMonth() + 1).toString().padStart(2, '0');
                                    var day = date.getDate().toString().padStart(2, '0');
                                    var formattedDate = year + '-' + month + '-' + day;
                                    $('#transfer_time').val(formattedDate);
                                    console.log('日期格式化成功：' + formattedDate);
                                }
                            } catch(e) {
                                console.log('日期格式化失败，保持原值');
                            }
                        }
                    } else {
                        // 如果日期为空，保持空白，不设置默认值
                        console.log('日期值为空，保持空白');
                    }
                }
            }
            
            // 显示模态框
            $('#editTransferModal').modal('show');
        }
        
        // 显示编辑模态框
        function showEditModal() {
            $('#paper_id').val('{$registerInfo.paperid}');
            $('#event').val('{$registerInfo.event}');
            $('#reg_id').val('{$registerInfo.id}');

            // 显示模态框
            $('#editModal').modal('show');
        }

        $(document).ready(function () {
            // 初始化所有popover提示
            $('[data-toggle="popover"]').popover();
            
            // 使用原生HTML5日期选择器，不需要初始化
            
            // 删除转账信息按钮点击事件
            $('#deleteTransferBtn').click(function() {
                var transferId = $(this).data('id');
                var regId = $(this).data('reg-id');
                
                // 显示确认对话框
                Swal.fire({
                    title: '确认删除',
                    text: '您确定要删除这条转账信息吗？删除后将无法恢复。',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: '确认删除',
                    cancelButtonText: '取消'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // 发送AJAX请求删除转账信息
                        $.ajax({
                            url: '{:U("OA/OfflineChain/deleteTransfer")}',
                            type: 'POST',
                            data: {
                                transfer_id: transferId,
                                reg_id: regId
                            },
                            dataType: 'json',
                            success: function(response) {
                                if (response.status == 1) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: '删除成功',
                                        text: response.message,
                                        confirmButtonText: '确定'
                                    }).then(() => {
                                        // 跳转到指定页面或刷新当前页面
                                        if (response.redirect_url) {
                                            window.location.href = response.redirect_url;
                                        } else {
                                            window.location.reload();
                                        }
                                    });
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: '删除失败',
                                        text: response.message || '删除转账信息失败',
                                        confirmButtonText: '确定'
                                    });
                                }
                            },
                            error: function() {
                                Swal.fire({
                                    icon: 'error',
                                    title: '系统错误',
                                    text: '网络错误或服务器异常，请稍后再试',
                                    confirmButtonText: '确定'
                                });
                            }
                        });
                    }
                });
            });
            
            // 保存修改转账信息按钮点击事件
            $('#saveTransferBtn').click(function() {
                // 获取表单数据
                var formData = {
                    transfer_id: $('#transfer_id').val(),
                    reg_id: $('#reg_id').val(),
                    transfer_name: $('#transfer_name').val(),
                    transfer_account: $('#transfer_account').val(),
                    transfer_time: $('#transfer_time').val(),
                    currency: $('#currency').val(),
                    total: $('#total').val(),
                    remark: $('#remark').val()
                };
                
                // 表单验证
                if (!formData.transfer_name) {
                    Swal.fire({
                        icon: 'error',
                        title: '验证错误',
                        text: '转账人不能为空'
                    });
                    return false;
                }
                
                if (!formData.total || isNaN(formData.total)) {
                    Swal.fire({
                        icon: 'error',
                        title: '验证错误',
                        text: '转账金额必须为有效数字'
                    });
                    return false;
                }
                
                // 确认对话框
                Swal.fire({
                    title: '确认修改',
                    text: '是否要修改作者的转账信息？',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: '确认修改',
                    cancelButtonText: '取消',
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // 发送AJAX请求
                        $.ajax({
                            url: '{:U("OA/OfflineChain/updateTransferInfo")}',
                            type: 'POST',
                            data: formData,
                            dataType: 'json',
                            success: function(response) {
                                if (response.status == 1) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: '操作成功',
                                        text: response.message,
                                        confirmButtonText: '确定'
                                    }).then((result) => {
                                        // 刷新页面
                                        window.location.reload();
                                    });
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: '操作失败',
                                        text: response.message || '修改转账信息失败',
                                        confirmButtonText: '确定'
                                    });
                                }
                            },
                            error: function() {
                                Swal.fire({
                                    icon: 'error',
                                    title: '系统错误',
                                    text: '网络错误或服务器异常，请稍后再试',
                                    confirmButtonText: '确定'
                                });
                            }
                        });
                    }
                });
            });
            
            // 驳回转账申请按钮点击事件
            $('#rejectTransferBtn').click(function () {
                var transferId = $(this).data('id');

                // 使用SweetAlert2显示输入框
                Swal.fire({
                    title: '驳回转账申请',
                    text: '请输入驳回理由',
                    input: 'textarea',
                    inputPlaceholder: '请详细说明驳回原因，便于用户修改...',
                    inputAttributes: {
                        'aria-label': '驳回理由',
                        'rows': 5
                    },
                    showCancelButton: true,
                    confirmButtonText: '提交',
                    cancelButtonText: '取消',
                    showLoaderOnConfirm: true,
                    preConfirm: (reason) => {
                        if (!reason || reason.trim() === '') {
                            Swal.showValidationMessage('请输入驳回理由')
                            return false;
                        }

                        // 发送驳回请求
                        var result = null;
                        $.ajax({
                            url: '{:U("OA/Audit/rejectTransfer")}',
                            type: 'POST',
                            data: {
                                transfer_id: transferId,
                                refusal: reason
                            },
                            dataType: 'json',
                            async: false, // 使用同步请求
                            success: function(response) {
                                if (response.status) {
                                    result = response;
                                } else {
                                    Swal.showValidationMessage('驳回失败: ' + (response.message || '未知错误'));
                                }
                            },
                            error: function(xhr, status, error) {
                                Swal.showValidationMessage('请求失败: ' + error);
                            }
                        });
                        return result;
                    },
                    allowOutsideClick: () => !Swal.isLoading()
                }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire({
                            title: '成功',
                            text: result.value.message || '驳回成功',
                            icon: 'success'
                        }).then(() => {
                            // 刷新页面
                            window.location.reload();
                        });
                    }
                });
            });

            // 保存修改按钮点击事件
            $('#saveChangesBtn').click(function () {
                // 获取表单数据
                var paperId = $('#paper_id').val();
                var event = $('#event').val();
                var regId = $('#reg_id').val();

                // 表单验证
                if (!paperId) {
                    Swal.fire({
                        icon: 'error',
                        title: '错误',
                        text: '文章ID不能为空'
                    });
                    return;
                }

                if (!event) {
                    Swal.fire({
                        icon: 'error',
                        title: '错误',
                        text: '会议简称不能为空'
                    });
                    return;
                }

                // 确认修改
                Swal.fire({
                    title: '确认修改',
                    text: '您确定要修改文章ID和会议简称吗？此操作将同时更新多个相关表的数据。',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: '确认修改',
                    cancelButtonText: '取消'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // 发送AJAX请求
                        $.ajax({
                            url: '{:U("OA/BatchUpdate/updateByRegId")}',
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                reg_id: regId,
                                paper_id: paperId,
                                event: event
                            },
                            success: function (response) {
                                if (response.status) {
                                    // 更新成功
                                    Swal.fire({
                                        icon: 'success',
                                        title: '更新成功',
                                        text: response.message,
                                        showConfirmButton: true
                                    }).then(() => {
                                        // 刷新页面
                                        window.location.reload();
                                    });
                                } else {
                                    // 更新失败
                                    Swal.fire({
                                        icon: 'error',
                                        title: '更新失败',
                                        text: response.message
                                    });
                                }
                            },
                            error: function () {
                                // 请求错误
                                Swal.fire({
                                    icon: 'error',
                                    title: '请求错误',
                                    text: '服务器连接失败，请稍后重试'
                                });
                            }
                        });
                    }
                });
            });
        });
    </script>
</block>