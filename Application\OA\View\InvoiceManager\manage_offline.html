<div class="info-section">
    <div class="info-section-header">
        <h4><i class="fa fa-cogs"></i> 线下发票管理</h4>
    </div>

    <php>
        // 确保状态是整数类型
        $status = intval($invoice['status']);
        $statusClass = isset($invoice['status_class']) ? $invoice['status_class'] : 'info';
        $statusText = isset($invoice['status_text']) ? $invoice['status_text'] : '未知状态';
        
        // 引入状态常量类
        $INVOICE_PENDING = \Common\Lib\InvoiceStatusConstants::INVOICE_PENDING;    // 0
        $INVOICE_REJECTED = \Common\Lib\InvoiceStatusConstants::INVOICE_REJECTED;  // 10
        $INVOICE_WAITING = \Common\Lib\InvoiceStatusConstants::INVOICE_WAITING;    // 20
        $INVOICE_SUCCESS = \Common\Lib\InvoiceStatusConstants::INVOICE_SUCCESS;    // 30
        $INVOICE_FAILED = \Common\Lib\InvoiceStatusConstants::INVOICE_FAILED;     // 40
        $INVOICE_VOIDED = \Common\Lib\InvoiceStatusConstants::INVOICE_VOIDED;     // 50
    </php>

    <div class="alert alert-{$statusClass}">
        <i class="fa fa-info-circle"></i> 当前发票状态：<span class="label label-{$statusClass}">{$statusText}</span>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">可用操作</h3>
                </div>
                <div class="panel-body">
                    <if condition="$status eq $INVOICE_PENDING">
                        <!-- 待审核状态 -->
                        <div class="btn-group">
                            <button type="button" id="submitToApiBtn" class="btn btn-primary" data-id="{$invoice.id}">
                                <i class="fa fa-cloud-upload"></i> 提交到API
                            </button>
                            <button type="button" id="rejectBtn" class="btn btn-danger" data-id="{$invoice.id}">
                                <i class="fa fa-times"></i> 驳回
                            </button>
                        </div>
                        <p class="help-block" style="margin-top: 10px;">
                            <i class="fa fa-info-circle"></i> 提交到API后，发票状态将变为"等待开票"，驳回后需要用户重新提交。
                        </p>
                    <elseif condition="$status eq $INVOICE_REJECTED" />
                        <!-- 初审未通过状态 -->
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 发票初审未通过，需要用户重新提交。
                        </div>
                    <elseif condition="$status eq $INVOICE_WAITING" />
                        <!-- 等待开票状态 -->
                        <div class="alert alert-primary">
                            <i class="fa fa-exclamation-triangle"></i> 发票已提交到API，正在等待开票，请耐心等待。
                        </div>
                    <elseif condition="$status eq $INVOICE_SUCCESS" />
                        <!-- 开票成功状态 -->
                        <div class="alert alert-success">
                            <i class="fa fa-check-circle"></i> 发票已开具成功，可以在"发票附件"选项卡中下载发票。
                        </div>
                    <elseif condition="$status eq $INVOICE_FAILED" />
                        <!-- 开票失败状态 -->
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-circle"></i> 发票开具失败，请联系管理员处理。
                        </div>
                    <elseif condition="$status eq $INVOICE_VOIDED" />
                        <!-- 发票已作废状态 -->
                        <div class="alert alert-secondary">
                            <i class="fa fa-ban"></i> 发票已作废，无法进行操作。
                        </div>
                    <else />
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 当前状态下没有可用操作。
                        </div>
                    </if>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">转账信息</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>会议简称</label>
                                <p class="form-control-static">{$invoice.event}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>文章ID</label>
                                <p class="form-control-static">{$invoice.paper_id}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>OA订单号</label>
                                <p class="form-control-static">{$invoice.oa_id}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>转账账户</label>
                                <p class="form-control-static">{$auditInfo.record.account|default="--"}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>转账时间</label>
                                <p class="form-control-static">{$auditInfo.record.remit_time|default="--"}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>转账人</label>
                                <p class="form-control-static">{$auditInfo.record.remitter_info|default="--"}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
