<?php

namespace OA\Service;

use Think\Controller;

/**
 * OA模块基础服务类
 * 所有OA模块的服务类都应继承此类
 */
class BaseService extends Controller
{
    public function _initialize() {}
    
    /**
     * 清理数据中的特殊字符，确保API交互正常
     * 主要清理换行符、回车符和制表符等不可见字符
     * 
     * @param string|array $data 需要清理的数据
     * @return string|array 清理后的数据
     */
    protected function sanitizeData($data) 
    {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if (is_string($value)) {
                    $data[$key] = trim(str_replace(array("\r", "\n", "\t"), '', $value));
                } else if (is_array($value)) {
                    $data[$key] = $this->sanitizeData($value);
                }
            }
            return $data;
        } else if (is_string($data)) {
            return trim(str_replace(array("\r", "\n", "\t"), '', $data));
        }
        return $data;
    }


    protected function initUserOaInfo()
    {
        try {
            // 默认初始化用户OA信息为空
            $this->userOaInfo = null;
            $this->isOaBound = false;

            // 获取当前登录用户ID
            $userId = session('userid');
            if (empty($userId)) {
                return; // 用户未登录，不进行后续处理
            }

            // 查询用户信息
            $adminModel = D('Admin');
            $user = $adminModel->where(array('id' => $userId))->find();

            // 检查用户是否已绑定OA信息
            if (!empty($user) && !empty($user['work_id']) && !empty($user['oa_info'])) {
                $this->isOaBound = true;

                // 解析OA信息JSON
                $oaInfo = json_decode($user['oa_info'], true);
                if ($oaInfo) {
                    // 标准化字段名称，确保后续处理使用统一的字段名
                    $this->userOaInfo = array(
                        'name' => isset($oaInfo['name']) ? $oaInfo['name'] : $user['fullname'],
                        'workId' => isset($oaInfo['workId']) ? $oaInfo['workId'] : $user['work_id'],
                        'deptName' => isset($oaInfo['deptName']) ? $oaInfo['deptName'] : '',
                        'deptId' => isset($oaInfo['deptId']) ? $oaInfo['deptId'] : '',
                        'userId' => isset($oaInfo['userId']) ? $oaInfo['userId'] : $userId
                    );

                    // 调试信息
                    \Think\Log::write("用户OA信息: " . json_encode($this->userOaInfo, JSON_UNESCAPED_UNICODE), 'DEBUG');
                }

                // 将OA信息传递给视图，方便在视图中使用
                $this->assign('userOaInfo', $this->userOaInfo);
                $this->assign('isOaBound', $this->isOaBound);
            }

            // 记录日志
            \Think\Log::write("用户ID:{$userId} OA绑定状态:{$this->isOaBound}", 'DEBUG');
        } catch (\Exception $e) {
            // 记录异常日志，但不中断程序执行
            \Think\Log::write("初始化用户OA信息异常: " . $e->getMessage(), 'ERROR');
        }
    }
    /**
     * 验证数据
     * @param array $data 待验证的数据
     * @param array $rules 验证规则
     * @return array 验证结果，包含status和msg
     */
    protected function validate($data, $rules)
    {
        if (empty($rules)) {
            return array('status' => true);
        }

        foreach ($rules as $field => $rule) {
            // 检查字段是否存在且不为空
            if (isset($rule['required']) && $rule['required'] && (!isset($data[$field]) || (isset($data[$field]) && trim($data[$field]) === ''))) {
                return array('status' => false, 'msg' => isset($rule['message']) ? $rule['message'] : "{$field}为必填项");
            }

            // 如果字段不存在且不是必填，则跳过
            if (!isset($data[$field])) {
                continue;
            }

            // 检查字段值
            $value = $data[$field];

            // 检查类型
            if (isset($rule['type'])) {
                switch ($rule['type']) {
                    case 'number':
                        if (!is_numeric($value)) {
                            return array('status' => false, 'msg' => isset($rule['message']) ? $rule['message'] : "{$field}必须是数字");
                        }
                        break;
                    case 'integer':
                        if (!is_numeric($value) || intval($value) != $value) {
                            return array('status' => false, 'msg' => isset($rule['message']) ? $rule['message'] : "{$field}必须是整数");
                        }
                        break;
                    case 'string':
                        if (!is_string($value)) {
                            return array('status' => false, 'msg' => isset($rule['message']) ? $rule['message'] : "{$field}必须是字符串");
                        }
                        break;
                    case 'array':
                        if (!is_array($value)) {
                            return array('status' => false, 'msg' => isset($rule['message']) ? $rule['message'] : "{$field}必须是数组");
                        }
                        break;
                }
            }

            // 检查长度
            if (isset($rule['min']) && strlen($value) < $rule['min']) {
                return array('status' => false, 'msg' => isset($rule['message']) ? $rule['message'] : "{$field}长度不能小于{$rule['min']}");
            }
            if (isset($rule['max']) && strlen($value) > $rule['max']) {
                return array('status' => false, 'msg' => isset($rule['message']) ? $rule['message'] : "{$field}长度不能大于{$rule['max']}");
            }

            // 检查正则表达式
            if (isset($rule['pattern']) && !preg_match($rule['pattern'], $value)) {
                return array('status' => false, 'msg' => isset($rule['message']) ? $rule['message'] : "{$field}格式不正确");
            }

            // 检查自定义验证器
            if (isset($rule['validator']) && is_callable($rule['validator'])) {
                if (!call_user_func($rule['validator'], $value)) {
                    return array(
                        'status' => false,
                        'msg' => isset($rule['validator_message']) ? $rule['validator_message'] : "{$field}验证失败"
                    );
                }
            }
        }

        return array('status' => true);
    }
}
