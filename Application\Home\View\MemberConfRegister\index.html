<extend name="public:member_base" />
<block name="main">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="page-title-box d-flex align-items-center justify-content-between">
          <h4 class="mb-0 font-size-18">My Conference Registrations</h4>
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="javascript: void(0);">Home</a></li>
              <li class="breadcrumb-item active">Conference Registrations</li>
            </ol>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <h4 class="card-title">Registration Records</h4>
            <p class="card-subtitle mb-4">View and manage your conference registration records.</p>

            <!-- 搜索表单 -->
            <form action="{:U('index')}" method="get" class="mb-3">
              <div class="row">
                <div class="col-md-5 mb-2">
                  <div class="input-group">
                    <input type="text" name="event" value="{$search_event}" class="form-control"
                      placeholder="Search by Conference">
                  </div>
                </div>
                <div class="col-md-5 mb-2">
                  <div class="input-group">
                    <input type="text" name="paper_id" value="{$search_paper_id}" class="form-control"
                      placeholder="Search by Paper ID">
                  </div>
                </div>
                <div class="col-md-2 mb-2">
                  <button class="btn btn-primary w-100" type="submit">
                    <i class="feather-search mr-1"></i> Search
                  </button>
                </div>
              </div>
            </form>

            <div class="table-responsive">
              <table class="table table-centered table-striped table-bordered mb-0">
                <thead>
                  <tr>
                    <th>Conference</th>
                    <th>Paper ID</th>
                    <th>Registration Fee</th>
                    <th>Registration Time</th>
                    <th>Payment Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <if condition="empty($registrations)">
                    <tr>
                      <td colspan="6" class="text-center">No registration records found</td>
                    </tr>
                    <else />
                    <volist name="registrations" id="registration">
                      <tr>
                        <td>{$registration.event}</td>
                        <td>{$registration.paperid}</td>
                        <td class="text-nowrap"><span class="amount-display">{$registration.total_formatted}</span></td>
                        <td>{$registration.create_time_format}</td>
                        <td>
                          <span class="badge badge-{$registration.pay_status_class}">{$registration.pay_status_text}</span>
                        </td>
                        <td>
                          <div class="btn-group">
                            <a href="{:U('detail', array('id' => $registration['id']))}" class="btn btn-sm btn-info" 
                               data-toggle="tooltip" title="View Details">
                              <i class="feather-eye mr-1"></i> Details
                            </a>
                            
                            <if condition="$registration.pay_status eq 0">
                              <a href="{:U('continuePay', array('id' => $registration['id']))}" class="btn btn-sm btn-primary ml-1" 
                                 data-toggle="tooltip" title="Continue Payment">
                                <i class="feather-credit-card mr-1"></i> Pay Now
                              </a>
                            </if>
                            
                            <if condition="$registration.pay_status eq 1">
                              <a href="{:U('Bill/PayList')}" 
                                 class="btn btn-sm btn-success ml-1" data-toggle="tooltip" title="Apply for Invoice">
                                <i class="feather-file-text mr-1"></i> Invoice
                              </a>
                            </if>
                          </div>
                        </td>
                      </tr>
                    </volist>
                  </if>
                </tbody>
              </table>
            </div>

            <!-- 分页 -->
            <div class="mt-3 iconf-pagination-wrapper">
              <div class="iconf-pagination">
                {$page}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</block>

<block name="script">
  <script>
    $(document).ready(function () {
      // 初始化工具提示
      $('[data-toggle="tooltip"]').tooltip();
    });
  </script>
</block>
