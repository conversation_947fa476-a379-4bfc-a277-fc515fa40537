<?php

namespace Mpanel\Controller;

use Think\Controller;
use Think\Page;
use PHPExcel;
use PHPExcel_IOFactory;

/**
 * 会议注册管理控制器
 * 负责会议注册相关的功能
 */
class AdminConfRegisterController extends CheckloginController
{
    /**
     * 会议注册服务
     * @var \Mpanel\Service\ConfRegisterService
     */
    protected $registerService;

    /**
     * 状态服务
     * @var \Mpanel\Service\StatusService
     */
    protected $statusService;

    /**
     * 初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        // 实例化服务类
        $this->registerService = D('Mpanel/ConfRegister', 'Service');
        $this->statusService = D('Mpanel/Status', 'Service');
    }

    /**
     * 会议注册列表页面
     */
    public function index()
    {
        $title = '注册信息列表';
        $db = D("SubConference");
        $f_grade = session('grade');
        $userid = session('userid');
        $k_event = I('k_event');
        if ($k_event) $map['event'] = array('like', '%' . $k_event . '%');

        //按级别读取会议列表
        if ($f_grade == 2) {
            $map['userid'] = session('userid');
        } else if ($f_grade == 3) {
            // 组员级别 - 通过关联表查询自己可以管理的会议
            // 先查询关联表中该用户作为管理员的所有会议ID
            $adminModel = M('sub_conference_admin');
            $adminConfs = $adminModel->where(array('admin_id' => session('userid')))->field('cid')->select();

            if (!empty($adminConfs)) {
                // 提取会议ID数组
                $confIds = array();
                foreach ($adminConfs as $conf) {
                    $confIds[] = $conf['cid'];
                }
                $map['id'] = array('in', $confIds);
            } else {
                // 如果没有管理的会议，则设置一个不可能的条件，确保查询结果为空
                $map['id'] = 0;
            }
        }

        $map['delete_time'] = array('exp', 'IS NULL'); // 排除软删除
        $count = $db->where($map)->count();
        $Page = new \Think\Page($count, 15);
        $Page->setConfig('header', '<span class="total">Total:<b>%TOTAL_ROW%</b>');
        $Page->setConfig('theme', '%FIRST% %UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% %HEADER%');
        $show = $Page->show(); // 分页显示输出
        $list = $db->where($map)->order('id desc')->limit($Page->firstRow . ',' . $Page->listRows)->select();

        $this->assign('list', $list);
        $this->assign('page', $show);
        $this->assign('title', $title);
        $this->assign('grade', $f_grade);
        $this->assign('action', ACTION_NAME);
        $this->display();
    }

    /**
     * 会议注册详情页面
     */
    public function regInfo()
    {
        $cid = I('cid');
        if (!$cid) {
            $this->error('参数错误');
        }

        try {
            // 检查权限
            check_conference_access($cid);

            $db = D("SubRegister");
            $paperid = I('paperid');
            $pay_status = I('pay_status');
            $email = I('email');
            $view = I('view');

            // 构建查询条件
            $map = array();
            if ($paperid) $map['paperid'] = array('like', '%' . $paperid . '%');
            if ($email) $map['email'] = array('like', '%' . $email . '%');
            if ($view !== null) $map['view'] = array('like', '%' . $view . '%');
            if ($pay_status !== null) $map['pay_status'] = array('like', '%' . $pay_status . '%');
            $map['cid'] = $cid;
            $map['delete_time'] = array('exp', 'IS NULL');

            // 查询满足要求的总记录数
            $count = $db->where($map)->count();

            // 实例化分页类 传入总记录数和每页显示的记录数(15)
            $Page = new Page($count, 15);
            $Page->setConfig('header', '<span class="total">Total:<b>%TOTAL_ROW%</b></span>');
            $Page->setConfig('theme', '%FIRST% %UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% %HEADER%');
            $show = $Page->show(); // 分页显示输出

            // 查询数据并获取关联数据
            $list = $db->relation(true)->where($map)->order('pay_status desc, id desc')->limit($Page->firstRow . ',' . $Page->listRows)->select();

            // 处理每条记录的状态信息
            foreach ($list as &$item) {
                // 获取支付状态信息
                $item['pay_status_info'] = $this->statusService->getPayStatusInfo($item['pay_status'], $item['pay_type']);

                // 获取发票状态信息
                $item['invoice_status_info'] = $this->statusService->getInvoiceStatusInfo($item['id']);

                // 获取查账状态信息
                $item['audit_status_info'] = $this->statusService->getAuditStatusInfo($item['id']);

                // 获取实付金额信息
                $item['actual_payment_info'] = $this->statusService->getActualPaymentInfo($item['id'], $item['pay_status'], $item['pay_type']);
            }

            // 输出查询结果
            $this->assign('list', $list);
            $this->assign('page', $show);
            $this->assign('cid', $cid);
            $this->assign('action', 'index');
            $this->assign('grade', session('grade'));
            $this->display();
        } catch (\Think\Exception $e) {
            $this->handleAccessException($e);
            return;
        }
    }

    /**
     * 查看单个注册记录详情
     */
    public function regShow()
    {
        $id = I('id');
        $db_paper = D('SubRegister');

        $where['delete_time'] = array('exp', 'is null');
        $where['id'] = $id;
        $info = $db_paper->relation(true)->where($where)->find();
        if (!$info) {
            $this->error('该信息不存在');
        }

        // 获取info cid
        $cid = $info['cid'];
        check_conference_access($cid);

        // 解析各种数据
        $lastpaper = string2array($info['lastpaper']);
        $type = string2array($info['type']);
        $extras = string2array($info['extras']);
        $other = string2array($info['other']);
        $more = string2array($info['more']);
        $total = string2array($info['total']);
        $proof = string2array($info['proof']);

        // 获取用户信息
        $userid = $info['userid'];
        $db_user = M('SubMember');
        $user = $db_user->find($userid);

        // 获取相关付款信息
        $db_pay = M('SubPay');
        $sql['regid'] = $id;
        $sql['status'] = 20;
        $pay_info = $db_pay->where($sql)->find();

        // 处理支付状态文本
        $payStatusText = $this->getPayStatusText($info['pay_status'], $info['pay_type']);

        // 处理支付方式文本
        $payTypeText = '';
        if ($pay_info) {
            $payTypeText = $this->getPayTypeText($pay_info['pay_type']);
        }

        // 处理付款金额显示
        $paytotal = '';
        if ($pay_info) {
            if ($pay_info['moneytype'] == 0) {
                $paytotal = $pay_info['total'] . ' CNY';
            } else if ($pay_info['moneytype'] == 1) {
                $paytotal = $pay_info['total'] . ' USD';
            }
        }

        // 更新已读状态
        $data['id'] = $id;
        $data['view'] = 1;
        $db_paper->save($data);

        // 处理发票信息
        $invoice_arr = array();
        if (!empty($info['invoice_info'])) {
            $invoice_arr = json_decode($info['invoice_info'], true);
        }

        // 获取新版本转账信息
        $transfer_info = null;
        $is_new_transfer = false;
        if ($info['pay_type'] == 2) { // 如果是线下转账
            $db_transfer = D('SubTransfer');
            $transfer_where = array('reg_id' => $id);
            $transfer_info = $db_transfer->where($transfer_where)->find();

            if ($transfer_info) {
                $is_new_transfer = true;

                // 格式化转账时间
                if (!empty($transfer_info['transfer_time'])) {
                    $transfer_info['transfer_time_text'] = date('Y-m-d H:i:s', $transfer_info['transfer_time']);
                }

                // 格式化创建时间
                if (!empty($transfer_info['create_time'])) {
                    $transfer_info['create_time_text'] = date('Y-m-d H:i:s', $transfer_info['create_time']);
                }

                // 格式化更新时间
                if (!empty($transfer_info['update_time'])) {
                    $transfer_info['update_time_text'] = date('Y-m-d H:i:s', $transfer_info['update_time']);
                }

                // 格式化币种
                $transfer_info['currency_text'] = $transfer_info['currency'] == 1 ? 'USD' : 'CNY';

                // 获取转账状态文本
                $transfer_info['status_text'] = $this->getTransferStatusText($transfer_info['status']);
            }
        }

        // 获取关联的发票信息
        $invoices = [];
        try {
            // 直接从数据库查询发票信息，避免通过服务类可能导致的类重复加载
            $invoiceModel = M('OaInvoice'); // 使用M()而非D()避免重复加载模型类
            if ($invoiceModel) {
                $invoiceRecords = $invoiceModel->where(array('reg_id' => $id))->select();
                $invoices = $invoiceRecords ?: [];
            }
            
            // 如果直接查询失败，再尝试使用服务类
            if (empty($invoices)) {
                // 优先使用Common模块的发票服务
                if (class_exists('\Common\Service\InvoiceService', false)) { // 添加false参数避免自动加载
                    $invoiceService = new \Common\Service\InvoiceService();
                    $invoices = $invoiceService->getInvoicesByRegId($id) ?: [];
                }
                // 备选方案：使用OA模块的发票服务
                else if (class_exists('\OA\Service\OnlineInvoiceService', false)) { // 添加false参数避免自动加载
                    $invoiceService = new \OA\Service\OnlineInvoiceService();
                    $result = $invoiceService->getInvoicesByRegId($id);
                    $invoices = ($result['status'] && !empty($result['data'])) ? $result['data'] : [];
                }
            }

            // 处理发票状态和类型
            if (!empty($invoices)) {
                $statusHelper = class_exists('\Common\Lib\InvoiceStatusConstants') ?
                    '\Common\Lib\InvoiceStatusConstants' : null;

                foreach ($invoices as &$invoice) {
                    // 处理发票状态
                    if ($statusHelper) {
                        $invoice['status_text'] = $statusHelper::getStatusText($invoice['status']);
                        $invoice['status_class'] = $statusHelper::getStatusClass($invoice['status']);
                    }

                    // 处理发票类型
                    if (isset($invoice['invoice_type'])) {
                        switch ($invoice['invoice_type']) {
                            case 'pc':
                                $invoice['invoice_type_text'] = '普通发票';
                                $invoice['invoice_type_class'] = 'info';
                                break;
                            case 'bs':
                                $invoice['invoice_type_text'] = '专用发票';
                                $invoice['invoice_type_class'] = 'primary';
                                break;
                            default:
                                $invoice['invoice_type_text'] = $invoice['invoice_type'];
                                $invoice['invoice_type_class'] = 'default';
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // 记录异常
            \Think\Log::write('获取发票列表异常: ' . $e->getMessage(), 'ERROR');
        }

        // 分配变量到视图
        $this->assign('info', $info);
        $this->assign('invoice_info', $invoice_arr);
        $this->assign('user', $user);
        $this->assign('type', $type);
        $this->assign('pay_info', $pay_info);
        $this->assign('paytotal', $paytotal);
        $this->assign('extras', $extras);
        $this->assign('other', $other);
        $this->assign('proof', $proof);
        $this->assign('total', $total);
        $this->assign('more', $more);
        $this->assign('lastpaper', $lastpaper);
        $this->assign('payStatusText', $payStatusText);
        $this->assign('payTypeText', $payTypeText);
        $this->assign('transfer_info', $transfer_info);
        $this->assign('is_new_transfer', $is_new_transfer);
        $this->assign('invoices', $invoices); // 新增：分配发票列表到视图

        $this->display();
    }

    /**
     * 获取支付状态文本
     * @param int $payStatus 支付状态码
     * @param int $payType 支付类型码
     * @return string 支付状态文本
     */
    private function getPayStatusText($payStatus, $payType)
    {
        switch ($payStatus) {
            case 0:
                return '尚未付款';
            case 1:
                switch ($payType) {
                    case 1:
                        return '在线支付成功';
                    case 2:
                        return '转账汇款成功';
                    default:
                        return '支付成功';
                }
            case 2:
                return '等待确认';
            case 3:
                return '支付失败';
            default:
                return '未知状态';
        }
    }

    /**
     * 获取支付方式文本
     * @param int $payType 支付方式码
     * @return string 支付方式文本
     */
    private function getPayTypeText($payType)
    {
        switch ($payType) {
            case 1:
                return '支付宝';
            case 2:
                return '微信';
            case 3:
                return '银联';
            case 4:
                return 'Paypal';
            case 5:
                return '转账汇款';
            default:
                return '其他方式';
        }
    }

    /**
     * 获取转账状态文本
     * @param int $status 转账状态码
     * @return string 转账状态文本
     */
    private function getTransferStatusText($status)
    {
        // 使用 Common\Lib\TransferStatusConstants 中的常量
        switch ($status) {
            case 0: // TRANSFER_PENDING
                return '待审核';
            case 10: // TRANSFER_SUCCESS
                return '转账审核成功';
            case 20: // TRANSFER_FAILED
                return '转账失败';
            case 30: // TRANSFER_REJECTED
                return '转账被驳回';
            default:
                return '未知状态';
        }
    }

    /**
     * 删除注册记录（软删除）
     * @return void
     */
    public function regDel()
    {
        // 权限检查
        stop_ction(2);

        // 获取并验证参数
        $id = I('id', 0, 'intval');
        if (empty($id)) {
            $this->error('参数错误：缺少有效的ID');
            return;
        }

        $db_paper = M('SubRegister');

        // 检查记录是否存在
        $exists = $db_paper->where(array('id' => $id))->find();
        //获取cid 判断权限
        $cid = $exists['cid'];
        check_conference_access($cid);
        if (!$exists) {
            $this->error('记录不存在或已被删除');
            return;
        }

        // 检查是否已缴费
        if ($exists['pay_status'] == 1) {
            $this->error('该记录已完成缴费，不允许删除');
            return;
        }

        // 开启事务
        $db_paper->startTrans();

        try {
            // 软删除数据
            $data = array(
                'delete_time' => time(),
            );

            $info = $db_paper->where(array('id' => $id))->save($data);

            if (!$info) {
                $db_paper->rollback();
                $this->error('删除失败，请稍后重试');
                return;
            }

            // 提交事务
            $db_paper->commit();

            // 返回成功信息
            $this->success('删除成功');
        } catch (\Exception $e) {
            // 回滚事务
            $db_paper->rollback();

            // 记录错误日志
            \Think\Log::write('删除注册记录异常：' . $e->getMessage(), 'ERROR');

            // 返回错误信息
            $this->error('操作异常：' . $e->getMessage());
        }
    }

    /**
     * 导出注册表的Excel表格
     */
    public function regExcel()
    {
        import("Org.Excel.PHPExcel");
        $db = D("SubRegister");
        $cid = I('cid');
        $s = I('s');
        if ($s == 1) $map['pay_status'] = 1;
        if ($s == 2) $map['pay_status'] = 0;
        $map['cid'] = $cid;
        $list = $db->where($map)->select();
        //获取会议简称
        $events = $list[1]['event'];
        $row = array();

        $row[0] = array('Paper ID', '题目', 'Presentation Type', '注册作者', '职称', '全部作者', '参会作者', '参会作者职称', '邮箱', '电话', '单位和国家', '注册金额', '实际金额', '支付状态',  'bill',  'remarks', '餐饮要求', '注册时间', '订单号', '自定义注册项目');
        $i = 1;
        foreach ($list as $v) {
            $row[$i]['id'] = $v['paperid'];
            $more = string2array($v['more']);
            //获取更多支付信息
            $db_pay = M('SubPay');
            $sql['regid'] = $v['id'];
            $sql['status'] = 20;
            $pay_info = $db_pay->where($sql)->find();

            //转换实际支付状态
            $ps = $v['pay_status'];
            switch ($ps) {
                case 0:
                    $status = '未付款';
                    break;
                case 1:
                    $status = '已支付';
                    break;
                case 2:
                    $status = '等待确认';
                    break;
                case 3:
                    $status = '支付失败';
                    break;
            }

            $row[$i]['title'] = $v['paper_title'];
            $row[$i]['presentation'] = $v['presentation'];
            $name = $v['firstname'] . ' ' . $v['middlename'] . ' ' . $v['lastname'];
            $row[$i]['author'] = preg_replace("/\s(?=\s)/", "\\1", $name);
            $row[$i]['position'] = $v['position'];
            $row[$i]['all_authors_names'] = $v['all_authors_names'];
            $row[$i]['attendeename'] = $v['attendeename'];
            $row[$i]['attendee_position'] = $v['attendee_position'];
            $row[$i]['email'] = $v['email'];
            $row[$i]['telenumber'] = $v['telenumber'];
            $row[$i]['country'] = $v['affiliation'] . '' . ',' . '' . $v['country'];
            //转换实际付款信息
            $reg_total = string2array($v['total']);
            $row[$i]['total'] = $reg_total['usd'] . 'USD' . '/' . $reg_total['cny'] . 'CNY';
            $row[$i]['totals'] = $pay_info['total'];
            $row[$i]['status'] = $status;
            $row[$i]['bill'] = $v['bill'];
            $row[$i]['remarks'] = $v['remarks'];
            $row[$i]['diet'] = $v['diet'];
            $row[$i]['comments'] = $v['comments'];
            $row[$i]['datetime'] = date("Y-m-d", $v['datetime']);
            $row[$i]['orderid'] = $pay_info['orderid'];
            //判断$more的内容是否为空，如果不为空，就循环读取，将其转换为文本格式的，一行一个
            if (!empty($more)) {
                foreach ($more as $key => $value) {
                    // 检查$value是否非空
                    if ($value !== "" && $value !== NULL && $value !== 0 && !empty($value)) {
                        $row[$i]['more'] .= $key . ': ' . $value . "\n" . '┇┇';
                    }
                }
            }
            $i++;
        }

        $xls = new PHPExcel();
        $xls->setActiveSheetIndex(0);
        $sheet = $xls->getActiveSheet();
        $sheet->fromArray($row, null, 'A1');

        $objWriter = PHPExcel_IOFactory::createWriter($xls, 'Excel2007');
        $filename = "$events" . '-Register-' . date("Y-m-d") . '.xlsx'; // 设置导出文件的文件名
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=$filename");
        header('Cache-Control: max-age=0');
        $objWriter->save('php://output');
        exit;
    }

    /**
     * 确认线下付款
     */
    public function confirmPay()
    {
        //确认线下付款
        $db = M('SubRegister');
        $data['id'] = I('id');
        $data['pay_status'] = 1;
        $info = $db->save($data);
        if ($info) $this->success('设置成功');
    }

    /**
     * 下载注册相关文件
     * @param int $id 注册记录ID
     * @param string $type 文件类型
     */
    public function downflieReg()
    {
        $id = I('id');
        $type = I('type');
        $db = D('SubRegister');
        $info = $db->find($id);

        if (!$info) {
            $this->error('文件不存在');
        }

        // 检查权限
        check_conference_access($info['cid']);

        $pid = $info['paperid'];
        
        // 下载头像
        if ($type == 'jpg') {
            $file = $info['photo'];
            $file = "Uploads/" . $file;
            $file_name = $pid . "-" . basename($file);
            header("Content-type: octet/stream");
            header("Content-disposition:attachment;filename=" . $file_name . ";");
            header("Content-Length:" . filesize($file));
            readfile($file);
            exit;
        }
        
        // 下载稿件功能
        if ($type == 'pdf') {
            $file = $info['lastpaper'];
            // 设置下载压缩包的名字
            $download_file_name = "{$info['event']}-(Paper_id_{$info['paperid']}).zip";
            
            // 判断file字段是数组还是普通字符串
            $filedata = json_decode($file, true); // 解析为关联数组
            
            // 如果不是JSON格式，尝试使用string2array函数
            if ($filedata === null) {
                $filedata = string2array($file);
            }
            
            // 创建临时文件
            $zip = new \ZipArchive(); // 使用完全限定名称
            $tmp_file = tempnam('.', '');
            if ($zip->open($tmp_file, \ZipArchive::CREATE) !== TRUE) {
                exit("无法创建ZIP文件");
            }
            
            $upload_dir = './Uploads/'; // 上传目录前缀
            
            if (is_array($filedata)) {
                // 数据是数组格式，表示有多个文件存在
                foreach ($filedata as $key => $value) {
                    $file_path = $upload_dir . $value; // 加上上传目录前缀
                    if (file_exists($file_path)) {
                        $zip->addFile($file_path, basename($file_path));
                    }
                }
            } else {
                // 数据不是数组格式，只有一个文件
                $file_path = $upload_dir . $file;
                if (file_exists($file_path)) {
                    $zip->addFile($file_path, basename($file_path));
                }
            }
            
            // 关闭 ZIP 文件
            $zip->close();
            
            // 检查 ZIP 文件是否有内容
            if (filesize($tmp_file) === 0) {
                $this->error('文件不存在或已被删除');
                unlink($tmp_file);
                exit;
            }
            
            // 发送 ZIP 文件到浏览器下载
            header('Content-Type: application/zip');
            header('Content-Disposition: attachment; filename=' . $download_file_name);
            header('Content-Length: ' . filesize($tmp_file));
            readfile($tmp_file);
            
            // 删除临时文件
            unlink($tmp_file);
            exit;
        }
    }

    /**
     * 处理访问异常
     * @param \Think\Exception $e 异常对象
     */
    private function handleAccessException($e)
    {
        if (strpos($e->getMessage(), '权限不足') !== false) {
            $this->error('您没有权限访问该会议');
        } else {
            $this->error('系统错误：' . $e->getMessage());
        }
    }
}
