<?php
// 构造提交到OA的发票数据
namespace OA\Service;

use Common\Lib\InvoiceStatusConstants;

class BuildInvoiceDataService extends BaseService
{

    public function build($invoices)
    {

        // 返回处理后的发票数据
        return $this->parseInvoiceData($invoices);
    }
    /**
     * 获取带有发票编号参数的回调地址
     *
     * @param string $invoiceNo 发票编号
     * @return string 完整的回调地址
     */
    protected function getCallbackUrl($invoiceNo)
    {
        // 从配置文件中获取基础回调地址
        $baseUrl = C('OA_CALLBACK_URLS.' . C('OA_ENVIRONMENT'));

        // 如果发票编号为空，直接返回基础URL
        if (empty($invoiceNo)) {
            return $baseUrl;
        }

        // 判断URL是否已经包含参数
        $separator = (strpos($baseUrl, '?') !== false) ? '&' : '?';

        // 返回带有发票编号参数的完整URL
        return $baseUrl . $separator . 'no=' . urlencode($invoiceNo);
    }

    // 获取提交人信息
    protected function getSubmitterInfo()
    {
        $userOaInfo = $this->userOaInfo;
        $submiter['submitName'] = $userOaInfo['workId'] . '#' . $userOaInfo['name'];
        $submiter['deptId'] = $userOaInfo['deptId'];
        $submiter['submitDept'] = $userOaInfo['deptName'];
        $submiter['userId'] = $userOaInfo['userId'];
        return $submiter;
    }
    //解析发票数据字段
    protected function parseInvoiceData($invoices)
    {
        if (empty($invoices)) {
            return array();
        }

        // 获取提交人的数组信息
        $submiter_info = $this->getSubmitterInfo();

        $result = array();
        foreach ($invoices as $invoice) {
            $formattedInvoice = array(
                'orderNum' => $invoice['oa_id'], // oa 流水号
                'invoiceTitle' => $invoice['invoice_title'], // 发票抬头
                'buyerTaxNum' => $invoice['buyer_tax_num'], // 税号
                'amount' => $invoice['amount'], // 开票金额
                'buyerPhone' => $invoice['buyer_phone'], // 买方电话
                'buyerEmail' => $invoice['buyer_email'], // 买方邮箱
                'buyerAddress' => $invoice['buyer_address'], // 买方地址
                'buyerAccount' => $invoice['buyer_account'], // 买方账户
                'buyerAccountName' => $invoice['buyer_account_name'], // 买方账户名
                'salerCompany' => $invoice['saler_company'], // 销方公司
                'goodsInfo' => $invoice['goods_info'], // 商品信息
                'invoiceType' => $invoice['invoice_type'], // 发票类型 普票 专票
                'userId' => $submiter_info['userId'], // 提交用户id
                'deptId' => $submiter_info['deptId'], // 用户部门
                'submitName' => $submiter_info['submitName'], // 提交人姓名
                'submitDept' => $submiter_info['submitDept'], // 提交人部门
                //remark
                'remark' => $invoice['remark'],
                // 优先使用已保存的回调URL，如果没有则生成新的
                "submitReturnApi" => !empty($invoice['call_back_url']) ? $invoice['call_back_url'] : $this->getCallbackUrl($invoice['no']),
                'invoiceRemark' => isset($invoice['invoice_remark']) ? $invoice['invoice_remark'] : null, // 新增：发票备注信息
                'buyerTel' => isset($invoice['buyer_tel']) ? $invoice['buyer_tel'] : null // 新增：买方电话
            );
            $result[] = $formattedInvoice;
        }

        return $result;

        dump($result);
    }

    /**
     * 根据查账记录查找关联的发票记录
     * 通过查账记录的reg_id匹配发票表中的reg_id
     *
     * @param array $auditRecord 查账记录
     * @return array 关联的发票记录列表
     */
    public function findRelatedInvoices($auditRecord)
    {
        if (empty($auditRecord) || empty($auditRecord['reg_id'])) {
            return array();
        }

        // 实例化发票模型
        $invoiceModel = D('OaInvoice', 'OA');

        // 通过reg_id查询关联的发票记录
        $invoices = $invoiceModel->where(array('reg_id' => $auditRecord['reg_id']))->select();

        // 如果找到了发票记录，但发票记录中没有oa_id，则更新发票记录，建立关联
        if (!empty($invoices)) {
            foreach ($invoices as &$invoice) {
                // 如果发票记录没有oa_id，但有查账记录的oa_id，则更新发票记录
                if (empty($invoice['oa_id']) && !empty($auditRecord['oa_id'])) {
                    $updateData = array(
                        'oa_id' => $auditRecord['oa_id'],
                        'update_time' => time()
                    );

                    // 更新发票记录
                    $invoiceModel->where(array('id' => $invoice['id']))->save($updateData);

                    // 更新当前记录的oa_id
                    $invoice['oa_id'] = $auditRecord['oa_id'];
                }
            }

            return $invoices;
        }
    }
}
