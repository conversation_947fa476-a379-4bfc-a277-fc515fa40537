<extend name="Mpanel@Base/admin_base" />

<block name="breadcrumb">
    <ol class="breadcrumb">
        <li><a href="{:U('Mpanel/Index/index')}"><i class="fa fa-home"></i> 首页</a></li>
        <li><a href="{:U('OA/Audit/onlineAudit', array('reg_id' => $auditRecord['reg_id']))}"><i
                    class="fa fa-search"></i> 查账详情</a></li>
        <li class="active"><i class="fa fa-file-text-o"></i> 发票管理</li>
    </ol>

</block>

<block name="style">
    <style>
        /* 发票管理页面样式 */
        .invoice-list {
            background-color: #fff;
            border-radius: 3px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .invoice-list-header {
            padding: 12px 15px;
            background-color: #337ab7;
            color: #fff;
            border-top-left-radius: 3px;
            border-top-right-radius: 3px;
        }

        .invoice-list-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .invoice-list-body {
            padding: 15px;
        }

        .invoice-card {
            margin-bottom: 10px;
            border-radius: 3px;
            border: 1px solid #eee;
            background-color: #fff;
            transition: all 0.2s ease;
        }

        .invoice-card:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .invoice-card-header {
            padding: 12px 15px;
            border-bottom: 1px solid #f5f5f5;
        }

        .invoice-card-body {
            padding: 15px 15px 5px 15px;
        }

        .invoice-card-footer {
            padding: 10px 15px;
            background-color: #f9f9f9;
            border-top: 1px solid #f0f0f0;
            text-align: right;
        }

        .info-row {
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: baseline;
        }

        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .info-label {
            color: #777;
            margin-right: 5px;
            display: inline-block;
            min-width: 80px;
            flex-shrink: 0;
        }

        .info-value {
            color: #333;
            flex-grow: 1;
            word-break: break-all;
        }

        .invoice-card-body .col-md-6 {
            margin-bottom: 5px;
        }

        .status-note {
            margin-top: 12px;
            padding: 10px;
            color: #31708f;
            background-color: #f8f9fa;
            border-radius: 3px;
            border-left: 3px solid #5bc0de;
            font-size: 13px;
            line-height: 1.5;
        }

        .filter-toolbar {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 15px;
            border: 1px solid #f0f0f0;
        }

        .action-toolbar {
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 3px;
            margin-bottom: 15px;
            border: 1px solid #f0f0f0;
        }
    </style>
</block>

<block name="content">
    <div class="row">
        <div class="col-md-12">
            <!-- 发票记录面板 -->
            <div class="invoice-list">
                <div class="invoice-list-header">
                    <h3><i class="fa fa-list"></i> 发票记录</h3>
                </div>
                <div class="invoice-list-body">
                    <if condition="!empty($invoices)">
                        <!-- 快速筛选工具栏 -->
                        <div class="filter-toolbar">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                        <input type="text" id="invoiceSearchInput" class="form-control"
                                            placeholder="搜索发票抬头、申请人邮箱或电话...">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <select id="statusFilter" class="form-control">
                                        <option value="">全部状态</option>
                                        <option value="待审核">待审核</option>
                                        <option value="已驳回">已驳回</option>
                                        <option value="等待开票">等待开票</option>
                                        <option value="开票成功">开票成功</option>
                                        <option value="开票失败">开票失败</option>
                                        <option value="已作废">已作废</option>
                                    </select>
                                </div>
                                <div class="col-md-2 text-right">
                                    <button id="resetFilters" class="btn btn-default">
                                        <i class="fa fa-refresh"></i> 重置筛选
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 全选和批量操作工具栏 -->
                        <div class="action-toolbar">
                            <div class="row">
                                <div class="col-md-6">
                                    <div>
                                        <label>
                                            <input type="checkbox" id="selectAll"> 全选/取消全选
                                        </label>
                                        <span id="selectedCountInfo" class="text-muted"
                                            style="margin-left: 15px;"></span>
                                    </div>
                                </div>
                                <div class="col-md-6 text-right">
                                    <div class="btn-group">
                                        <button type="button" id="batchEditBtn" class="btn btn-warning" disabled>
                                            <i class="fa fa-edit"></i> 批量编辑选中
                                        </button>
                                        <button type="button" id="editAllBtn" class="btn btn-primary">
                                            <i class="fa fa-edit"></i> 编辑全部发票
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 面板式发票列表 -->
                        <div id="invoiceCards">
                            <volist name="invoices" id="invoice">
                                <div class="invoice-card"
                                    style="border-left: 4px solid #{$invoice.status_color|default='ddd'};"
                                    data-status="{$invoice.status_text}">
                                    <div class="invoice-card-header">
                                        <div class="row">
                                            <div class="col-xs-8">
                                                <if condition="$invoice.status eq 0">
                                                    <input type="checkbox" class="invoice-checkbox"
                                                        value="{$invoice.id}" style="margin-right: 8px;">
                                                    <else />
                                                    <input type="checkbox" disabled
                                                        title="状态为「{$invoice.status_text}」的发票不可选择"
                                                        style="margin-right: 8px;">
                                                </if>
                                                <span
                                                    style="font-size: 16px; font-weight: bold;">{$invoice.invoice_title}</span>
                                            </div>
                                            <div class="col-xs-4 text-right">
                                                <span class="label label-{$invoice.status_class}">
                                                    <i class="fa {$invoice.status_icon}"></i> {$invoice.status_text}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="invoice-card-body">
                                        <!-- 主要信息 -->
                                        <div class="row">
                                            <!-- 左侧信息 -->
                                            <div class="col-md-6">
                                                <div class="info-row">
                                                    <span class="info-label"><i class="fa fa-barcode"></i> OA订单号：</span>
                                                    <span class="info-value"><code>{$invoice.oa_id}</code></span>
                                                </div>
                                                <div class="info-row">
                                                    <span class="info-label"><i class="fa fa-tag"></i> 会议简称：</span>
                                                    <span class="info-value">{$invoice.event_display}</span>
                                                </div>
                                                <div class="info-row">
                                                    <span class="info-label"><i class="fa fa-file-text"></i>
                                                        文章ID：</span>
                                                    <span class="info-value">{$invoice.paper_id_display}</span>
                                                </div>
                                                <div class="info-row">
                                                    <span class="info-label"><i class="fa fa-money"></i> 金额：</span>
                                                    <span
                                                        class="info-value text-danger"><strong>{$invoice.formatted_amount}</strong></span>
                                                </div>
                                            </div>

                                            <!-- 右侧信息 -->
                                            <div class="col-md-6">
                                                <div class="info-row">
                                                    <span class="info-label"><i class="fa fa-user"></i> 申请人：</span>
                                                    <span class="info-value">{$invoice.buyer_name}</span>
                                                </div>
                                                <div class="info-row">
                                                    <span class="info-label"><i class="fa fa-envelope"></i> 邮箱：</span>
                                                    <span class="info-value">{$invoice.buyer_email}</span>
                                                </div>
                                                <div class="info-row">
                                                    <span class="info-label"><i class="fa fa-phone"></i> 电话：</span>
                                                    <span class="info-value">{$invoice.buyer_phone}</span>
                                                </div>
                                                <div class="info-row">
                                                    <span class="info-label"><i class="fa fa-calendar"></i> 申请时间：</span>
                                                    <span class="info-value">{$invoice.create_time_text}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 状态说明 -->
                                        <notempty name="invoice.status_note">
                                            <div class="status-note">
                                                <i class="fa fa-info-circle"></i> {$invoice.status_note}
                                            </div>
                                        </notempty>
                                    </div>

                                    <div class="invoice-card-footer">
                                        <if condition="$invoice.is_editable">
                                            <button type="button" class="btn btn-sm btn-warning edit-btn"
                                                data-id="{$invoice.id}" data-invoice-title="{$invoice.invoice_title}"
                                                data-buyer-tax-num="{$invoice.buyer_tax_num}"
                                                data-buyer-email="{$invoice.buyer_email}"
                                                data-buyer-phone="{$invoice.buyer_phone}">
                                                <i class="fa fa-edit"></i> 编辑
                                            </button>
                                        </if>
                                        <a href="{:U('OA/Invoice/detail', array('id' => $invoice['id']))}"
                                            class="btn btn-sm btn-info">
                                            <i class="fa fa-eye"></i> 查看详情
                                        </a>
                                        <if condition="$invoice.status eq 0">
                                            <button type="button" class="btn btn-sm btn-danger reject-btn"
                                                data-id="{$invoice.id}">
                                                <i class="fa fa-times"></i> 驳回
                                            </button>
                                        </if>
                                    </div>
                                </div>
                            </volist>
                        </div>

                        <!-- 操作说明 -->
                        <div class="help-block text-center" style="margin-top: 20px; color: #666;">
                            <i class="fa fa-info-circle"></i>
                            <span>选择状态为"待审核"的发票，点击"提交到API"按钮将发票提交到OA系统</span>
                        </div>
                        <else />
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle"></i>
                            <span>未找到关联的发票记录</span>
                            <p>您可以点击上方的"申请发票"按钮创建新的发票申请。</p>
                        </div>
                    </if>
                </div>
            </div>

            <!-- 查账详细信息（可折叠） -->
            <div class="panel panel-info">
                <div class="panel-heading" data-toggle="collapse" data-target="#auditDetailPanel"
                    style="cursor: pointer;">
                    <h3 class="panel-title">
                        <i class="fa fa-list-alt"></i> 查账详细信息
                        <span class="pull-right"><i class="fa fa-chevron-down"></i></span>
                    </h3>
                </div>
                <div id="auditDetailPanel" class="panel-body collapse">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered table-striped">
                                <tbody>
                                    <tr>
                                        <th style="width:40%;"><i class="fa fa-barcode"></i> OA流水号</th>
                                        <td><code>{$auditRecord.oa_id}</code></td>
                                    </tr>
                                    <tr>
                                        <th><i class="fa fa-tag"></i> 会议简称</th>
                                        <td>{$auditRecord.event}</td>
                                    </tr>
                                    <tr>
                                        <th><i class="fa fa-file-text"></i> 论文ID</th>
                                        <td>{$auditRecord.paper_id}</td>
                                    </tr>
                                    <tr>
                                        <th><i class="fa fa-check-circle"></i> 查账类型</th>
                                        <td>
                                            <php>
                                                $auditType = isset($auditRecord['audit_type']) ?
                                                $auditRecord['audit_type'] :
                                                \Common\Lib\OaAuditStatusConstants::AUDIT_TYPE_ONLINE;
                                                echo \Common\Lib\OaAuditStatusConstants::getTypeText($auditType);
                                            </php>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered table-striped">
                                <tbody>
                                    <tr>
                                        <th style="width:40%;"><i class="fa fa-money"></i> 金额</th>
                                        <td>
                                            <strong class="text-danger">{$auditRecord.total}</strong>
                                            <span class="label label-default">{$auditRecord.currency}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th><i class="fa fa-calendar"></i> 汇款时间</th>
                                        <td>{$auditRecord.remit_time}</td>
                                    </tr>
                                    <tr>
                                        <th><i class="fa fa-user"></i> 汇款人</th>
                                        <td>{$auditRecord.remitter_info}</td>
                                    </tr>
                                    <tr>
                                        <th><i class="fa fa-clock-o"></i> 提交时间</th>
                                        <td>{$auditRecord.create_time_text}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <notempty name="auditRecord.remark">
                        <div class="row" style="margin-top: 10px;">
                            <div class="col-md-12">
                                <div class="well well-sm">
                                    <strong><i class="fa fa-comment"></i> 备注信息：</strong>
                                    <p>{$auditRecord.remark}</p>
                                </div>
                            </div>
                        </div>
                    </notempty>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center" style="margin: 30px 0;">
        <button type="button" id="submitToApiBtn" class="btn btn-success btn-lg" style="padding: 15px 40px; font-size: 18px;" {$submit_button_attrs}>
            <i class="fa fa-cloud-upload"></i> 提交到OA开票
        </button>
    </div>

    <!-- 编辑弹窗 -->
    <div class="modal fade" id="editModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
                    <h4 class="modal-title">编辑发票信息</h4>
                </div>
                <div class="modal-body">
                    <form id="editForm" class="form-horizontal">
                        <input type="hidden" id="editInvoiceId" name="invoice_id">

                        <div class="form-group">
                            <label class="col-sm-3 control-label">发票抬头：<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="editInvoiceTitle" name="invoice_title"
                                    required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">税号：<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="editBuyerTaxNum" name="buyer_tax_num"
                                    required>
                                <p class="help-block small">统一社会信用代码或纳税人识别号</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">买方邮箱：<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="email" class="form-control" id="editBuyerEmail" name="buyer_email"
                                    required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">买方电话：</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="editBuyerPhone" name="buyer_phone">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveEditBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量编辑弹窗 -->
    <div class="modal fade" id="batchEditModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
                    <h4 class="modal-title">批量编辑发票信息</h4>
                </div>
                <div class="modal-body">
                    <form id="batchEditForm" class="form-horizontal">
                        <input type="hidden" id="batchEditIds" name="invoice_ids">

                        <div class="form-group">
                            <label class="col-sm-3 control-label">发票抬头：<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" name="invoice_title" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">税号：<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" name="buyer_tax_num" required>
                                <p class="help-block small">统一社会信用代码或纳税人识别号</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">买方邮箱：<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="email" class="form-control" name="buyer_email" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">买方电话：</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" name="buyer_phone">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveBatchEditBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <block name="script">
        <script>
            $(document).ready(function () {
                // 折叠面板图标切换
                $('.panel-heading[data-toggle="collapse"]').click(function () {
                    $(this).find('.fa-chevron-down, .fa-chevron-up').toggleClass('fa-chevron-down fa-chevron-up');
                });

                // 全选/取消全选
                $('#selectAll').change(function () {
                    $('.invoice-checkbox:not(:disabled)').prop('checked', $(this).prop('checked'));
                    updateSubmitButton();
                    updateSelectedCount();
                });

                // 单个复选框变化时更新提交按钮状态
                $(document).on('change', '.invoice-checkbox', function () {
                    updateSubmitButton();
                    updateSelectedCount();
                });

                // 更新提交按钮状态
                function updateSubmitButton() {
                    var checkedCount = $('.invoice-checkbox:checked').length;
                    $('#submitToApiBtn').prop('disabled', checkedCount === 0);
                    $('#batchEditBtn').prop('disabled', checkedCount === 0);
                }

                // 更新选中数量显示
                function updateSelectedCount() {
                    var checkedCount = $('.invoice-checkbox:checked').length;
                    var totalCount = $('.invoice-checkbox:not(:disabled)').length;

                    if (totalCount > 0) {
                        $('#selectedCountInfo').html('<i class="fa fa-check-square-o"></i> 已选择 ' +
                            checkedCount + ' / ' + totalCount + ' 条记录');

                        if (checkedCount > 0) {
                            $('#selectedCountInfo').removeClass('text-muted').addClass('text-primary');
                        } else {
                            $('#selectedCountInfo').removeClass('text-primary').addClass('text-muted');
                        }

                        $('#selectedCountInfo').show();
                    } else {
                        $('#selectedCountInfo').hide();
                    }
                }

                // 初始化
                updateSubmitButton();
                updateSelectedCount();

                // 表格搜索和筛选功能
                $("#invoiceSearchInput").on("keyup", function () {
                    filterCards();
                });

                $("#statusFilter").on("change", function () {
                    filterCards();
                });

                $("#resetFilters").click(function () {
                    $("#invoiceSearchInput").val("");
                    $("#statusFilter").val("");
                    filterCards();
                });

                function filterCards() {
                    var searchValue = $("#invoiceSearchInput").val().toLowerCase();
                    var statusValue = $("#statusFilter").val();

                    $(".invoice-card").each(function () {
                        var card = $(this);
                        var titleText = card.find(".invoice-card-header span").text().toLowerCase();
                        var emailText = card.find(".info-label:contains('邮箱')").next().text().toLowerCase();
                        var phoneText = card.find(".info-label:contains('电话')").next().text().toLowerCase();
                        var oaIdText = card.find(".info-label:contains('OA流水号')").next().text().toLowerCase();
                        var eventText = card.find(".info-label:contains('会议简称')").next().text().toLowerCase();
                        var paperIdText = card.find(".info-label:contains('文章ID')").next().text().toLowerCase();
                        var statusText = card.data("status");

                        var matchesSearch = titleText.indexOf(searchValue) > -1 ||
                            emailText.indexOf(searchValue) > -1 ||
                            phoneText.indexOf(searchValue) > -1 ||
                            oaIdText.indexOf(searchValue) > -1 ||
                            eventText.indexOf(searchValue) > -1 ||
                            paperIdText.indexOf(searchValue) > -1;

                        var matchesStatus = statusValue === "" || statusText === statusValue;

                        if (matchesSearch && matchesStatus) {
                            card.show();
                        } else {
                            card.hide();
                        }
                    });

                    // 更新选中计数
                    updateSelectedCount();
                }

                // 单个编辑按钮点击事件
                $('.edit-btn').click(function () {
                    var id = $(this).data('id');
                    var invoiceTitle = $(this).data('invoice-title');
                    var buyerTaxNum = $(this).data('buyer-tax-num');
                    var buyerEmail = $(this).data('buyer-email');
                    var buyerPhone = $(this).data('buyer-phone');

                    $('#editInvoiceId').val(id);
                    $('#editInvoiceTitle').val(invoiceTitle);
                    $('#editBuyerTaxNum').val(buyerTaxNum);
                    $('#editBuyerEmail').val(buyerEmail);
                    $('#editBuyerPhone').val(buyerPhone);

                    $('#editModal').modal('show');
                });

                // 保存编辑
                $('#saveEditBtn').click(function () {
                    var id = $('#editInvoiceId').val();
                    var invoiceTitle = $('#editInvoiceTitle').val();
                    var buyerTaxNum = $('#editBuyerTaxNum').val();
                    var buyerEmail = $('#editBuyerEmail').val();
                    var buyerPhone = $('#editBuyerPhone').val();

                    // 验证输入
                    if (!invoiceTitle || !buyerTaxNum || !buyerEmail) {
                        Swal.fire({
                            title: '格式错误',
                            text: '请填写必填项',
                            icon: 'error',
                            confirmButtonText: '确定'
                        });
                        return;
                    }

                    // 验证邮箱格式
                    if (buyerEmail && !validateEmail(buyerEmail)) {
                        Swal.fire({
                            title: '格式错误',
                            text: '邮箱格式不正确',
                            icon: 'error',
                            confirmButtonText: '确定'
                        });
                        return;
                    }

                    // 发送AJAX请求
                    handleAjaxRequest("{:U('OA/Invoice/editInvoiceInfo', '', true, true)}", {
                        invoice_id: id,
                        invoice_title: invoiceTitle,
                        buyer_tax_num: buyerTaxNum,
                        buyer_email: buyerEmail,
                        buyer_phone: buyerPhone
                    }, '更新成功', '更新失败', '#editModal');
                });

                // 批量编辑按钮点击事件
                $('#batchEditBtn').click(function () {
                    var checkedBoxes = $('.invoice-checkbox:checked');
                    if (checkedBoxes.length === 0) {
                        Swal.fire({
                            title: '提示',
                            text: '请先选择要编辑的发票',
                            icon: 'warning',
                            confirmButtonText: '确定'
                        });
                        return;
                    }

                    var ids = [];
                    checkedBoxes.each(function () {
                        ids.push($(this).val());
                    });

                    $('#batchEditIds').val(ids.join(','));
                    $('#batchEditModal').modal('show');
                });

                // 保存批量编辑
                $('#saveBatchEditBtn').click(function () {
                    try {
                        // 获取表单数据
                        const invoiceIds = $('#batchEditIds').val();
                        const invoiceTitle = $('#batchEditForm [name="invoice_title"]').val();
                        const buyerTaxNum = $('#batchEditForm [name="buyer_tax_num"]').val();
                        const buyerEmail = $('#batchEditForm [name="buyer_email"]').val();
                        const buyerPhone = $('#batchEditForm [name="buyer_phone"]').val();

                        // 验证输入
                        if (!invoiceTitle || !buyerTaxNum || !buyerEmail) {
                            Swal.fire({
                                title: '格式错误',
                                text: '请填写必填项',
                                icon: 'error',
                                confirmButtonText: '确定'
                            });
                            return;
                        }

                        // 验证邮箱格式
                        if (buyerEmail && !validateEmail(buyerEmail)) {
                            Swal.fire({
                                title: '格式错误',
                                text: '邮箱格式不正确',
                                icon: 'error',
                                confirmButtonText: '确定'
                            });
                            return;
                        }

                        // 发送AJAX请求
                        handleAjaxRequest("{:U('OA/Invoice/batchEditInvoiceInfo', '', true, true)}", {
                            invoice_ids: invoiceIds,
                            invoice_title: invoiceTitle,
                            buyer_tax_num: buyerTaxNum,
                            buyer_email: buyerEmail,
                            buyer_phone: buyerPhone
                        }, '批量更新成功', '批量更新失败', '#batchEditModal');
                    } catch (error) {
                        console.error('批量编辑错误:', error);
                        Swal.fire({
                            title: '错误',
                            text: '操作过程中发生错误',
                            icon: 'error',
                            confirmButtonText: '确定'
                        });
                    }
                });

                // 编辑全部发票按钮点击事件
                $('#editAllBtn').click(function () {
                    // 检查是否有可编辑的发票
                    var hasEditableInvoices = $('.invoice-checkbox:not(:disabled)').length > 0;

                    if (!hasEditableInvoices) {
                        Swal.fire({
                            title: '提示',
                            text: '没有可编辑的发票',
                            icon: 'warning',
                            confirmButtonText: '确定'
                        });
                        return;
                    }

                    // 创建一个新的模态框，用于编辑所有发票
                    Swal.fire({
                        title: '编辑全部发票',
                        html: `
                            <form id="editAllForm" class="form-horizontal">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">发票抬头：<span class="text-danger">*</span></label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="editAllInvoiceTitle" name="invoice_title" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">税号：<span class="text-danger">*</span></label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="editAllBuyerTaxNum" name="buyer_tax_num" required>
                                        <p class="help-block small">统一社会信用代码或纳税人识别号</p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">买方邮箱：<span class="text-danger">*</span></label>
                                    <div class="col-sm-9">
                                        <input type="email" class="form-control" id="editAllBuyerEmail" name="buyer_email" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">买方电话：</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="editAllBuyerPhone" name="buyer_phone">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-offset-3 col-sm-9">
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" id="editAllSyncCheckbox" checked> 同步更新所有关联发票
                                            </label>
                                            <p class="help-block small">选中后，将更新当前OA流水号下的所有发票</p>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        `,
                        showCancelButton: true,
                        confirmButtonText: '保存',
                        cancelButtonText: '取消',
                        focusConfirm: false,
                        didOpen: () => {
                            // 尝试从第一个发票获取默认值
                            const firstInvoice = $('.invoice-card:visible').first();
                            if (firstInvoice.length > 0) {
                                const editBtn = firstInvoice.find('.edit-btn');
                                if (editBtn.length > 0) {
                                    $('#editAllInvoiceTitle').val(editBtn.data('invoice-title') || '');
                                    $('#editAllBuyerTaxNum').val(editBtn.data('buyer-tax-num') || '');
                                    $('#editAllBuyerEmail').val(editBtn.data('buyer-email') || '');
                                    $('#editAllBuyerPhone').val(editBtn.data('buyer-phone') || '');
                                }
                            }
                        },
                        preConfirm: () => {
                            const invoiceTitle = document.getElementById('editAllInvoiceTitle').value;
                            const buyerTaxNum = document.getElementById('editAllBuyerTaxNum').value;
                            const buyerEmail = document.getElementById('editAllBuyerEmail').value;
                            const buyerPhone = document.getElementById('editAllBuyerPhone').value;
                            const syncAll = document.getElementById('editAllSyncCheckbox').checked ? 1 : 0;

                            if (!invoiceTitle || !buyerTaxNum || !buyerEmail) {
                                Swal.showValidationMessage('请填写必填项');
                                return false;
                            }

                            if (buyerEmail && !validateEmail(buyerEmail)) {
                                Swal.showValidationMessage('邮箱格式不正确');
                                return false;
                            }

                            return {
                                invoiceTitle,
                                buyerTaxNum,
                                buyerEmail,
                                buyerPhone,
                                syncAll
                            };
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            const { invoiceTitle, buyerTaxNum, buyerEmail, buyerPhone, syncAll } = result.value;

                            // 发送AJAX请求
                            handleAjaxRequest("{:U('OA/Invoice/editAllInvoices', '', true, true)}", {
                                oa_id: '{$auditRecord.oa_id}',
                                invoice_title: invoiceTitle,
                                buyer_tax_num: buyerTaxNum,
                                buyer_email: buyerEmail,
                                buyer_phone: buyerPhone,
                                sync_all: syncAll
                            }, '所有发票更新成功', '更新失败');
                        }
                    });
                });

                // 提交到API按钮点击事件
                $('#submitToApiBtn').click(function () {
                    var checkedBoxes = $('.invoice-checkbox:checked');
                    if (checkedBoxes.length === 0) {
                        Swal.fire({
                            title: '提示',
                            text: '请先选择要提交的发票',
                            icon: 'warning',
                            confirmButtonText: '确定'
                        });
                        return;
                    }

                    var ids = [];
                    checkedBoxes.each(function () {
                        ids.push($(this).val());
                    });

                    Swal.fire({
                        title: '确认提交',
                        text: `确定要将选中的 ${ids.length} 张发票提交到API吗？提交后状态将变为"等待开票"。`,
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonText: '确定提交',
                        cancelButtonText: '取消'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // 发送AJAX请求
                            handleAjaxRequest("{:U('OA/Invoice/submitToApi', '', true, true)}", {
                                invoice_ids: ids.join(',')
                            }, '提交成功', '提交失败');
                        }
                    });
                });

                // 驳回按钮点击事件
                $('.reject-btn').click(function () {
                    var id = $(this).data('id');

                    Swal.fire({
                        title: '驳回发票',
                        html: '<div class="text-left">' +
                            '<p><i class="fa fa-info-circle text-info"></i> 驳回后，系统将通知申请人修改发票信息</p>' +
                            '<div class="form-group">' +
                            '<label for="swal-rejection-reason">请输入驳回原因：</label>' +
                            '<textarea id="swal-rejection-reason" class="form-control" placeholder="请详细说明驳回原因，以便申请人了解如何修改..." rows="4"></textarea>' +
                            '</div>' +
                            '</div>',
                        showCancelButton: true,
                        confirmButtonText: '确认驳回',
                        cancelButtonText: '取消',
                        focusConfirm: false,
                        preConfirm: () => {
                            const refusal = document.getElementById('swal-rejection-reason').value;
                            if (!refusal) {
                                Swal.showValidationMessage('请输入驳回原因');
                                return false;
                            }
                            return refusal;
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            const refusal = result.value;

                            // 发送AJAX请求
                            handleAjaxRequest("{:U('OA/Invoice/rejectInvoice', '', true, true)}", {
                                invoice_id: id,
                                refusal: refusal
                            }, '驳回成功', '驳回失败');
                        }
                    });
                });

                // 验证邮箱格式
                function validateEmail(email) {
                    var emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    return emailRegex.test(email);
                }

                // 处理AJAX请求的通用函数
                function handleAjaxRequest(url, data, successMsg, errorMsg, modalId = null) {
                    // 显示加载中提示
                    Swal.fire({
                        title: '处理中',
                        text: '请稍候...',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        allowEnterKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // 发送AJAX请求
                    $.ajax({
                        url: url,
                        type: 'POST',
                        dataType: 'json',
                        data: data,
                        success: function (response) {
                            if (response.status) {
                                // 如果提供了模态框ID，则关闭模态框
                                if (modalId) {
                                    $(modalId).modal('hide');
                                }

                                // 显示成功提示
                                Swal.fire({
                                    title: '成功',
                                    text: response.message || successMsg,
                                    icon: 'success',
                                    confirmButtonText: '确定',
                                    timer: 2000,
                                    timerProgressBar: true
                                }).then(function () {
                                    location.reload();
                                });
                            } else {
                                // 显示错误提示
                                Swal.fire({
                                    title: '错误',
                                    text: response.message || errorMsg,
                                    icon: 'error',
                                    confirmButtonText: '确定'
                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('AJAX请求错误:', status, error);

                            // 显示错误提示
                            Swal.fire({
                                title: '错误',
                                text: '网络错误，请稍后重试 (' + status + ')',
                                icon: 'error',
                                confirmButtonText: '确定'
                            });
                        }
                    });
                }
            });
        </script>
    </block>