<extend name="Mpanel@Base/admin_base" />
<block name="title">
    <title>（线下转账）确认查账与发票信息</title>
</block>
<block name="breadcrumb">
    <ol class="breadcrumb">
        <li><a href="{:U('Mpanel/Index/index')}"><i class="fa fa-home"></i> 首页</a></li>
        <li class="active"><i class="fa fa-link"></i> 线下一键查账和开票</li>
    </ol>
</block>

<block name="content">
    <link rel="stylesheet" href="/Public/statics/css/steps_admin_enhanced.css">
    <div class="container-fluid">
        <!-- 步骤指示器 -->
        <div class="row">
            <div class="col-md-12">
                <ul class="admin-stepped-progress">
                    <li class="admin-complete"><span>确认注册与转账信息</span></li>
                    <li class="admin-current"><span>确认查账和发票信息</span></li>
                    <li><span>提交到OA</span></li>
                </ul>
            </div>
        </div>

        <form id="chainForm" class="form-horizontal">
            <div class="row">
                <div class="col-md-12">
                    <!-- 查账信息区域 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title"><i class="fa fa-search"></i> 查账信息</h4>
                        </div>
                        <div class="panel-body">
                            <!-- 会议与文章信息 -->
                            <h5 class="section-title"><i class="fa fa-calendar-check-o"></i> 会议与文章信息</h5>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">会议名称</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" name="confName"
                                        value="{$registerInfo['event']}" required readonly>
                                    <p class="help-block">
                                        <small><i class="fa fa-info-circle"></i> 如需修改会议名称或文章ID，请<a href="{:U('OA/OfflineChain/confirmInfo', array('reg_id' => $registerInfo['id']))}" class="text-primary">返回上一步</a></small>
                                    </p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">文章ID</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" name="paperCode"
                                        value="{$registerInfo['paperid']}" required readonly>
                                </div>
                            </div>

                            <!-- 转账信息 -->
                            <h5 class="section-title"><i class="fa fa-money"></i> 转账信息</h5>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">转账人</label>
                                <div class="col-sm-9">
                                    <p class="form-control-static">{$transferInfo['transfer_name']}</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">转账账户</label>
                                <div class="col-sm-9">
                                    <p class="form-control-static">{$transferInfo['transfer_account']}</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">转账金额</label>
                                <div class="col-sm-9">
                                    <p class="form-control-static">{$transferInfo['total']} {$transferInfo['currency'] == 0 ? 'CNY' : 'USD'}</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">转账时间</label>
                                <div class="col-sm-9">
                                    <p class="form-control-static">{:date('Y-m-d', $transferInfo['transfer_time'])}</p>
                                </div>
                            </div>

                            <!-- 收款信息部分 -->
                            <h5 class="section-title"><i class="fa fa-credit-card"></i> 收款信息</h5>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">收款银行账户 <span class="text-danger">*</span></label>
                                <div class="col-sm-9">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="selectedAccountDisplay" readonly placeholder="请选择收款账户" required>
                                        <input type="hidden" name="account" id="selectedAccountValue" required>
                                        <div class="input-group-btn">
                                            <button type="button" class="btn btn-primary" id="selectAccountBtn">
                                                <i class="fa fa-search"></i> 选择账户
                                            </button>
                                        </div>
                                    </div>
                                    <div id="accountDetailPreview" class="well well-sm mt-2" style="margin-top: 10px; display: none;">
                                        <p class="mb-0"><strong>账户详情：</strong></p>
                                        <div id="accountDetailContent"></div>
                                    </div>
                                    <span class="help-block">请选择与转账货币类型匹配的收款账户</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">备注信息</label>
                                <div class="col-sm-9">
                                    <textarea class="form-control" name="remark" rows="3" placeholder="可选：添加备注信息">{$transferInfo.remark}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 发票信息区域 -->
                    <notempty name="isUSD">
                        <div class="panel panel-warning">
                            <div class="panel-heading">
                                <h4 class="panel-title"><i class="fa fa-exclamation-triangle"></i> 美元订单提示</h4>
                            </div>
                            <div class="panel-body">
                                <div class="alert alert-warning">
                                    <i class="fa fa-info-circle"></i> 美元订单不支持开具发票，将只执行查账操作。
                                </div>
                            </div>
                        </div>
                    <else />
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title"><i class="fa fa-file-text-o"></i> 发票信息</h4>
                            </div>
                            <div class="panel-body">
                                <!-- 操作按钮 -->
                                <div class="row" style="margin-bottom: 15px;">
                                    <div class="col-md-12 text-right">
                                        <button type="button" class="btn btn-success" id="addInvoiceBtn">
                                            <i class="fa fa-plus"></i> 添加发票
                                        </button>
                                    </div>
                                </div>

                                <!-- 已有发票列表 -->
                                <notempty name="invoices">
                                    <h5 class="section-title"><i class="fa fa-list"></i> 已有发票</h5>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover" id="invoiceTable">
                                            <thead>
                                                <tr>
                                                    <th width="5%"><input type="checkbox" id="selectAllInvoices" checked></th>
                                                    <th width="15%">发票抬头</th>
                                                    <th width="15%">纳税人识别号</th>
                                                    <th width="10%">发票类型</th>
                                                    <th width="10%">金额</th>
                                                    <th width="15%">发票项目</th>
                                                    <th width="10%">状态</th>
                                                    <th width="20%">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <volist name="invoices" id="invoice">
                                                    <tr>
                                                        <td>
                                                            <if condition="$invoice['status'] eq 0">
                                                                <input type="checkbox" class="invoice-checkbox" name="selected_invoices[]" value="{$invoice.id}" checked>
                                                            <else />
                                                                <span class="text-muted"><i class="fa fa-lock"></i></span>
                                                            </if>
                                                        </td>
                                                        <td>{$invoice.invoice_title}</td>
                                                        <td>{$invoice.buyer_tax_num}</td>
                                                        <td>
                                                            <switch name="invoice.invoice_type">
                                                                <case value="pc"><span class="label label-info">普通发票</span></case>
                                                                <case value="bs"><span class="label label-primary">专用发票</span></case>
                                                                <default><span class="label label-default">{$invoice.invoice_type}</span></default>
                                                            </switch>
                                                        </td>
                                                        <td><span class="text-danger">{$invoice.amount}</span></td>
                                                        <td>
                                                            <php>
                                                                $goodsInfoText = $invoice['goods_info'];
                                                                // 检查是否是键值形式
                                                                if (isset($invoiceItemOptions[$invoice['goods_info']])) {
                                                                    $goodsInfoText = $invoiceItemOptions[$invoice['goods_info']];
                                                                }
                                                                // 检查是否包含会议简称前缀
                                                                if (strpos($goodsInfoText, '会议注册费，会议简称：') === 0) {
                                                                    echo $goodsInfoText;
                                                                } else {
                                                                    echo $goodsInfoText;
                                                                }
                                                            </php>
                                                        </td>
                                                        <td>
                                                            <switch name="invoice.status">
                                                                <case value="0"><span class="label label-default">待审核</span></case>
                                                                <case value="10"><span class="label label-info">初审未通过</span></case>
                                                                <case value="20"><span class="label label-primary">等待开票</span></case>
                                                                <case value="30"><span class="label label-success">开票成功</span></case>
                                                                <case value="40"><span class="label label-danger">开票失败</span></case>
                                                                <case value="50"><span class="label label-secondary">发票已冲红</span></case>
                                                                <default><span class="label label-default">未知状态({$invoice.status})</span></default>
                                                            </switch>
                                                        </td>
                                                        <td>
                                                            <if condition="$invoice['status'] eq 0 || $invoice['status'] eq 10">
                                                                <button type="button" class="btn btn-xs btn-primary edit-invoice-btn" data-id="{$invoice.id}">
                                                                    <i class="fa fa-edit"></i> 编辑
                                                                </button>
                                                                <button type="button" class="btn btn-xs btn-danger delete-invoice-btn" data-id="{$invoice.id}">
                                                                    <i class="fa fa-trash"></i> 删除
                                                                </button>
                                                            <else />
                                                                <button type="button" class="btn btn-xs btn-default" disabled>
                                                                    <i class="fa fa-lock"></i> 已锁定
                                                                </button>
                                                                <if condition="$invoice['status'] eq 30">
                                                                    <a href="{:U('OA/Invoice/download', array('id' => $invoice['id']))}" class="btn btn-xs btn-success" target="_blank">
                                                                        <i class="fa fa-download"></i> 下载
                                                                    </a>
                                                                </if>
                                                            </if>
                                                        </td>
                                                    </tr>
                                                </volist>
                                            </tbody>
                                        </table>
                                    </div>
                                <else />
<div class="alert alert-info">
    <i class="fa fa-info-circle"></i> <strong>暂无发票信息</strong>
    <p class="mt-2 mb-0">您可以：</p>
    <ul class="mt-1 mb-1">
        <li>点击上方<strong>"添加发票"</strong>按钮，为作者新增发票信息</li>
        <li>也可以先不提交发票，等查账成功后再次提交发票申请</li>
    </ul>
    <p class="mb-0"><small>注意：查账与发票申请可以分开处理，优先确保查账成功</small></p>
</div>
                                </notempty>
                            </div>
                        </div>
                    </notempty>

                    <!-- 隐藏字段 -->
                    <input type="hidden" name="reg_id" value="{$registerInfo['id']}">
                    <input type="hidden" name="transfer_id" value="{$transferInfo['id']}">
                    <input type="hidden" name="paper_id" value="{$registerInfo['paperid']}">
                    <input type="hidden" name="goodsInfo" value="会议注册费，会议简称：{$registerInfo['event']}">
                    <!-- 添加表单令牌，防止重复提交 -->
                    <input type="hidden" name="form_token" value="{$form_token|default=''}">

                    <!-- 提交按钮 -->
                    <div class="form-group">
                        <div class="col-sm-12 text-center">
                            <if condition="$isAuditSubmitted">
                                <div class="alert alert-warning" style="margin-bottom: 15px;">
                                    <i class="fa fa-exclamation-triangle"></i>
                                    <strong>注意：</strong> 该查账信息已经提交到OA系统，状态为：
                                    <switch name="auditRecord.status">
                                        <case value="0"><span class="label label-warning">待提交查账</span></case>
                                        <case value="5"><span class="label label-default">草稿状态</span></case>
                                        <case value="10"><span class="label label-info">OA审核中</span></case>
                                        <case value="20"><span class="label label-success">查账成功</span></case>
                                        <case value="30"><span class="label label-danger">查账失败</span></case>
                                        <case value="40"><span class="label label-warning">查账驳回</span></case>
                                        <default><span class="label label-default">未知状态({$auditRecord.status})</span></default>
                                    </switch>
                                    <if condition="$auditRecord.status eq 30">
                                        <p class="text-danger" style="margin-top: 10px;">失败原因：{$auditRecord.fail_reason}</p>
                                    </if>
                                </div>
                                <button type="button" class="btn btn-default btn-lg" disabled>
                                    <i class="fa fa-lock"></i>
                                    <span>
                                        <if condition="$isUSD">
                                            提交查账
                                        <else />
                                            提交查账并开票
                                        </if>
                                    </span>
                                    <small>(已提交)</small>
                                </button>
                            <else />
                                <button type="button" id="submitChainBtn" class="btn btn-primary btn-lg">
                                    <i class="fa fa-check"></i> <span id="submitBtnText">
                                        <if condition="$isUSD">
                                            提交查账
                                        <else />
                                            提交查账并开票
                                        </if>
                                    </span>
                                </button>
                            </if>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

<!-- 引入通用发票表单模板 -->
<assign name="scene" value="offline" />
<assign name="form_id" value="invoiceForm" />
<assign name="modal_id" value="invoiceModal" />
<assign name="invoice_item_options" value="$invoiceItemOptions" />
<include file="Public:_invoice_form" />
</block>




<block name="script">
    <script>
        // 定义全局变量，用于判断是否已提交
        var isAuditSubmitted = <if condition="$isAuditSubmitted">true<else />false</if>;
        
        // 银行账户相关样式
        $('<style>\n\
            .account-detail-cell { max-height: 100px; overflow-y: auto; }\n\
            .disabled-account-row { background-color: #f9f9f9; color: #999; }\n\
            .account-row { transition: all 0.2s ease; }\n\
            .account-row:hover:not(.disabled-account-row) { background-color: #f8f8f8; cursor: pointer; }\n\
            .account-row.selected { background-color: #e8f5e9 !important; }\n\
            #accountsTable th { background-color: #f8f8f8; }\n\
            .modal-dialog { width: 800px; }\n\
            .table-responsive { max-height: 400px; overflow-y: auto; }\n\
            .account-detail-cell { line-height: 1.5; }\n\
            /* 单选框样式优化 */\n\
            .radio { margin: 0; }\n\
            .account-radio-container { display: inline-block; position: relative; padding-left: 25px; cursor: pointer; }\n\
            .account-radio-container input { position: absolute; opacity: 0; cursor: pointer; }\n\
            .account-radio-checkmark { position: absolute; top: 0; left: 0; height: 20px; width: 20px; background-color: #fff; border: 2px solid #ddd; border-radius: 50%; }\n\
            .account-radio-container:hover input ~ .account-radio-checkmark { border-color: #5cb85c; }\n\
            .account-radio-container input:checked ~ .account-radio-checkmark { background-color: #fff; border-color: #5cb85c; }\n\
            .account-radio-checkmark:after { content: ""; position: absolute; display: none; }\n\
            .account-radio-container input:checked ~ .account-radio-checkmark:after { display: block; }\n\
            .account-radio-container .account-radio-checkmark:after { top: 3px; left: 3px; width: 10px; height: 10px; border-radius: 50%; background: #5cb85c; }\n\
            .select-column { text-align: center; vertical-align: middle; }\n\
        </style>').appendTo('head');

        $(document).ready(function () {
            // 如果已提交，禁用所有表单元素
            if (isAuditSubmitted) {
                $('#chainForm input, #chainForm select, #chainForm textarea').prop('disabled', true);
                $('.edit-invoice-btn, .delete-invoice-btn, #addInvoiceBtn, #selectAccountBtn').prop('disabled', true).addClass('disabled');

                // 添加提示信息
                if ($('#addInvoiceBtn').length > 0) {
                    $('#addInvoiceBtn').after('<span class="text-muted" style="margin-left: 10px;"><i class="fa fa-info-circle"></i> 查账信息已提交，无法修改</span>');
                }

                // 如果已提交，在会话存储中标记为已提交
                var regId = $('input[name="reg_id"]').val();
                sessionStorage.setItem('audit_submitted_' + regId, 'true');
            }

            // 检查是否已经提交过（从会话存储中获取）
            checkPreviousSubmission();

            // 全选/取消全选
            $('#selectAllInvoices').change(function () {
                $('.invoice-checkbox').prop('checked', $(this).prop('checked'));
            });

            // 添加发票按钮点击事件
            $('#addInvoiceBtn').click(function() {
                // 确保InvoiceManager已初始化
                if (typeof InvoiceManager !== 'undefined' && window.invoiceManager) {
                    window.invoiceManager.openAddModal();
                } else {
                    console.error('InvoiceManager未初始化，无法添加发票');
                    // 尝试初始化
                    if (typeof InvoiceManager !== 'undefined') {
                        initInvoiceManager();
                        setTimeout(function () {
                            window.invoiceManager.openAddModal();
                        }, 500);
                    } else {
                        alert('发票管理模块未初始化，请刷新页面重试');
                    }
                }
            });

            // 编辑发票按钮点击事件
            $(document).on('click', '.edit-invoice-btn', function() {
                var invoiceId = $(this).data('id');

                // 确保InvoiceManager已初始化
                if (typeof InvoiceManager !== 'undefined' && window.invoiceManager) {
                    window.invoiceManager.openEditModal(invoiceId);
                } else {
                    console.error('InvoiceManager未初始化，无法编辑发票');
                    // 尝试初始化
                    if (typeof InvoiceManager !== 'undefined') {
                        initInvoiceManager();
                        setTimeout(function () {
                            window.invoiceManager.openEditModal(invoiceId);
                        }, 500);
                    } else {
                        alert('发票管理模块未初始化，请刷新页面重试');
                    }
                }
            });

            // 删除发票按钮点击事件
            $(document).on('click', '.delete-invoice-btn', function() {
                var invoiceId = $(this).data('id');

                // 确保InvoiceManager已初始化
                if (typeof InvoiceManager !== 'undefined' && window.invoiceManager) {
                    window.invoiceManager.openDeleteModal(invoiceId);
                } else {
                    console.error('InvoiceManager未初始化，无法删除发票');
                    // 尝试初始化
                    if (typeof InvoiceManager !== 'undefined') {
                        initInvoiceManager();
                        setTimeout(function () {
                            window.invoiceManager.openDeleteModal(invoiceId);
                        }, 500);
                    } else {
                        alert('发票管理模块未初始化，请刷新页面重试');
                    }
                }
            });

            // InvoiceManager 会处理保存发票按钮点击事件和表单验证

            // 提交按钮点击事件
            // 银行账户选择按钮点击事件
            $('#selectAccountBtn').click(function() {
                $('#accountSelectModal').modal('show');
            });
            
            // 点击已选账户的详情预览也可以打开模态框
            $('#accountDetailPreview').click(function() {
                if (!isAuditSubmitted) { // 如果表单未提交，才允许点击打开
                    $('#accountSelectModal').modal('show');
                }
            }).css('cursor', 'pointer');
            
            // 处理账户行点击事件
            $(document).on('click', '.account-row:not(.disabled-account-row)', function(e) {
                // 如果点击的是单选按钮本身，不需要额外处理，因为单选按钮有自己的点击事件
                if (!$(e.target).is('input[type="radio"]') && $(e.target).parents('.radio').length === 0) {
                    // 点击行时自动选中单选按钮
                    $(this).find('input[type="radio"]').prop('checked', true).trigger('change');
                }
            });
            
            // 单选按钮变化事件
            $(document).on('change', '.account-radio', function() {
                // 移除所有行的选中状态
                $('.account-row').removeClass('selected');
                
                // 给当前选中的行添加选中状态
                $(this).closest('.account-row').addClass('selected');
                
                // 启用确认按钮
                $('#confirmAccountSelection').prop('disabled', false);
            });
            
            // 处理未配置账户标识的账户
            $(document).on('click', '.disabled-account-row', function(e) {
                // 点击不可用的账户行时显示提示
                Swal.fire({
                    title: "账户不可用",
                    text: "该账户未配置账户标识，无法选择。请联系管理员进行配置。",
                    icon: "warning",
                    confirmButtonText: "我知道了"
                });
            });
            
            // 确认选择按钮点击事件
            $('#confirmAccountSelection').click(function() {
                var selectedRadio = $('.account-radio:checked');
                if (selectedRadio.length > 0) {
                    var row = selectedRadio.closest('.account-row');
                    var nickname = row.data('account-nickname');
                    var oaDict = row.data('account-oa-dict');
                    var detail = row.data('account-detail');
                    
                    // 清理OA字典值，去除所有空格、换行和回车符
                    var cleanedOaDict = oaDict.replace(/[\s\n\r]/g, '');
                    console.log('原始账户标识:', oaDict, '清理后:', cleanedOaDict);
                    
                    // 设置选中的账户
                    $('#selectedAccountDisplay').val(nickname + ' (' + cleanedOaDict + ')');
                    $('#selectedAccountValue').val(cleanedOaDict);
                    
                    // 显示账户详情预览
                    $('#accountDetailContent').html(detail.replace(/{br}/g, '<br>'));
                    $('#accountDetailPreview').show();
                    
                    // 关闭模态框
                    $('#accountSelectModal').modal('hide');
                }
            });
            
            // 处理未配置账户标识的账户
            $(document).on('click', '.disabled-account-row', function(e) {
                // 点击不可用的账户行时显示提示
                Swal.fire({
                    title: "账户不可用",
                    text: "该账户未配置账户标识，无法选择。请联系管理员进行配置。",
                    icon: "warning",
                    confirmButtonText: "我知道了"
                });
            });
            
            // 模态框打开时重置选中状态
            $('#accountSelectModal').on('show.bs.modal', function() {
                // 移除所有行的选中状态
                $('.account-row').removeClass('selected');
                
                // 取消所有单选按钮的选中状态
                $('.account-radio').prop('checked', false);
                
                // 禁用确认按钮
                $('#confirmAccountSelection').prop('disabled', true);
                
                // 如果已经选择了账户，预选中对应的单选按钮
                var currentValue = $('#selectedAccountValue').val();
                if (currentValue) {
                    var radio = $('.account-radio[value="' + currentValue + '"]');
                    if (radio.length > 0) {
                        radio.prop('checked', true).trigger('change');
                    }
                }
            });
            
            $('#submitChainBtn').click(function () {
                // 简单表单验证
                var form = $('#chainForm');
                var isValid = true;
                
                // 检查是否选择了银行账户
                if (!$('#selectedAccountValue').val()) {
                    Swal.fire({
                        title: "请选择收款银行账户",
                        text: "请点击'选择账户'按钮，选择一个有效的收款银行账户。",
                        icon: "warning",
                        confirmButtonText: "确定"
                    });
                    return false;
                }

                // 检查是否有选中的发票
                var hasSelectedInvoices = $('.invoice-checkbox:checked').length > 0;
                var isUSD = <if condition="$isUSD">true<else />false</if>;
                var noInvoiceSelected = !hasSelectedInvoices && !isUSD;

                // 检查必填字段
                form.find('[required]:visible').each(function () {
                    if (!$(this).val()) {
                        var fieldLabel = $(this).closest('.form-group').find('label').text().trim();
                        // 使用SweetAlert2替代原生alert
                        Swal.fire({
                            title: 'iconfsys.cc 显示',
                            text: fieldLabel + '不能为空',
                            icon: 'warning',
                            confirmButtonText: '确定'
                        });
                        isValid = false;
                        return false;
                    }
                });

                if (!isValid) {
                    return false;
                }

                // 如果是美元订单，直接提交查账
                if (isUSD) {
                    // 添加一个标记，表示只执行查账
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'audit_only',
                        value: '1'
                    }).appendTo('#chainForm');

                    // 显示加载提示
                    $('#submitChainBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 处理中...');

                    // 直接执行提交
                    submitChainForm();
                    return false;
                }

                // 如果有发票列表但没有选中任何发票，询问用户是否只提交查账
                if (noInvoiceSelected) {
                    Swal.fire({
                        title: '未选择发票',
                        text: '您没有选择任何发票，是否只提交查账信息？',
                        icon: 'question',
                        showCancelButton: true,
                        showDenyButton: true,
                        confirmButtonText: '是，只提交查账',
                        denyButtonText: '选择发票',
                        cancelButtonText: '取消'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // 用户确认只提交查账
                            // 添加一个标记，表示只执行查账
                            $('<input>').attr({
                                type: 'hidden',
                                name: 'audit_only',
                                value: '1'
                            }).appendTo('#chainForm');

                            // 显示加载提示
                            $('#submitChainBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 处理中...');

                            // 直接执行提交
                            submitChainForm();
                        } else if (result.isDenied) {
                            // 用户选择添加发票，不执行任何操作
                            return false;
                        }
                    });
                    return false;
                }

                // 确定提交文本
                var confirmText = hasSelectedInvoices ? '是否确认执行一键查账和开票操作？' : '是否确认只执行查账操作？';
                var buttonText = hasSelectedInvoices ? '确认查账和开票' : '确认只查账';

                // 使用 SweetAlert2 确认提交
                Swal.fire({
                    title: '确认操作',
                    text: confirmText,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: buttonText,
                    cancelButtonText: '取消'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // 用户确认提交
                        // 显示加载提示
                        $('#submitChainBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 处理中...');

                        // 直接执行提交
                        submitChainForm();
                    }
                });
            });

            // 预览API数据函数已移除

            /**
             * 检查是否已经提交过（从会话存储中获取）
             */
            function checkPreviousSubmission() {
                var regId = $('input[name="reg_id"]').val();
                var submittedKey = 'audit_submitted_' + regId;

                // 检查会话存储中是否标记为已提交
                if (sessionStorage.getItem(submittedKey) === 'true') {
                    // 已经提交过，禁用提交按钮
                    $('#submitChainBtn').html('<i class="fa fa-lock"></i> 查账信息已提交').prop('disabled', true);

                    // 禁用所有表单元素
                    $('#chainForm input, #chainForm select, #chainForm textarea').prop('disabled', true);
                    $('.edit-invoice-btn, .delete-invoice-btn, #addInvoiceBtn').prop('disabled', true).addClass('disabled');

                    // 添加提示信息
                    if ($('#addInvoiceBtn').length > 0 && $('#addInvoiceBtn').next('.text-muted').length === 0) {
                        $('#addInvoiceBtn').after('<span class="text-muted" style="margin-left: 10px;"><i class="fa fa-info-circle"></i> 查账信息已提交，无法修改</span>');
                    }
                }
            }

            // 提交表单函数
            function submitChainForm() {
                // 在提交前再次清理OA字典值
                var oaDictValue = $('#selectedAccountValue').val();
                if (oaDictValue) {
                    var cleanedOaDict = oaDictValue.replace(/[\s\n\r]/g, '');
                    $('#selectedAccountValue').val(cleanedOaDict);
                    console.log('表单提交前清理账户标识:', cleanedOaDict);
                }
                
                // 检查是否已经提交过
                var regId = $('input[name="reg_id"]').val();
                var submittedKey = 'audit_submitted_' + regId;

                if (sessionStorage.getItem(submittedKey) === 'true') {
                    // 已经提交过，显示提示并阻止重复提交
                    Swal.fire({
                        title: '请勿重复提交',
                        text: '您的查账信息已经提交，请勿重复操作',
                        icon: 'warning',
                        confirmButtonText: '确定'
                    });

                    // 恢复按钮状态，但保持禁用
                    $('#submitChainBtn').html('<i class="fa fa-lock"></i> 查账信息已提交').prop('disabled', true);
                    return false;
                }

                // 获取表单数据
                var formData = $('#chainForm').serialize();

                // 添加时间戳，防止缓存
                formData += '&_t=' + new Date().getTime();

                // 发送AJAX请求
                $.ajax({
                    url: '{:U("OA/OfflineChain/submit")}',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    beforeSend: function() {
                        console.log('提交的账户标识值:', $('#selectedAccountValue').val());
                    },
                    success: function (response) {
                        if (response.status) {
                            // 操作成功，标记为已提交
                            sessionStorage.setItem(submittedKey, 'true');

                            // 操作成功
                            Swal.fire({
                                title: '操作成功',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: '查看结果'
                            }).then((result) => {
                                // 跳转到结果页面
                                if (response.redirect && response.url) {
                                    window.location.href = response.url;
                                }
                            });
                        } else {
                            // 操作失败，恢复按钮状态
                            $('#submitChainBtn').prop('disabled', false).html('<i class="fa fa-check"></i> ' + $('#submitBtnText').text());

                            // 显示错误信息
                            var errorTitle = '操作失败';
                            if (response.error_type) {
                                errorTitle = response.error_type;
                            }

                            Swal.fire({
                                title: errorTitle,
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: '确定'
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        // 恢复按钮状态
                        $('#submitChainBtn').prop('disabled', false).html('<i class="fa fa-check"></i> ' + $('#submitBtnText').text());

                        // 显示错误信息
                        Swal.fire({
                            title: '系统错误',
                            text: '请求处理失败，请稍后重试',
                            icon: 'error',
                            confirmButtonText: '确定'
                        });
                    }
                });
            }
        });
    </script>

    <!-- 引入发票管理JavaScript库 -->
    <script src="/Public/static/js/invoice.js?v=1.1.0"></script>
    
    <!-- 银行账户选择模态框 -->
    <div class="modal fade" id="accountSelectModal" tabindex="-1" role="dialog" aria-labelledby="accountSelectModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="accountSelectModalLabel"><i class="fa fa-bank"></i> 选择收款银行账户</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 请选择一个收款账户。点击表格行或单选按钮均可选中账户。
                    </div>
                    
                    <!-- 银行账户表格式列表 -->
                    <div class="table-responsive">
                        <notempty name="bankAccounts">
                            <table class="table table-hover table-striped" id="accountsTable">
                                <thead>
                                    <tr>
                                        <th width="10%">账户类型</th>
                                        <th width="20%">账户简称</th>
                                        <th width="50%">账户详情</th>
                                        <th width="10%">账户标识</th>
                                        <th width="10%">选择</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <foreach name="bankAccounts" item="bank">
                                        <tr class="account-row <empty name="bank.oa_dict">disabled-account-row</empty>" 
                                            data-account-id="{$bank.id}" 
                                            data-account-nickname="{$bank.nickname}" 
                                            data-account-oa-dict="{$bank.oa_dict}" 
                                            data-account-detail="{$bank.bank}"
                                            data-account-currency="{$bank.type eq 1 ? 'USD' : 'CNY'}">
                                            <td>
                                                <if condition="$bank['type'] eq 1">
                                                    <span class="label label-info">USD</span>
                                                <else />
                                                    <span class="label label-success">CNY</span>
                                                </if>
                                            </td>
                                            <td><strong>{$bank.nickname}</strong></td>
                                            <td class="account-detail-cell">
                                                <php>
                                                    echo str_replace(array('{br}'), array('<br>'), $bank['bank']);
                                                </php>
                                            </td>
                                            <td>
                                                <notempty name="bank.oa_dict">
                                                    <span class="label label-primary">{$bank.oa_dict}</span>
                                                <else />
                                                    <span class="label label-danger">未配置</span>
                                                </notempty>
                                            </td>
                                            <td class="select-column">
                                                <notempty name="bank.oa_dict">
                                                    <label class="account-radio-container">
                                                        <input type="radio" name="accountRadio" class="account-radio" value="{$bank.oa_dict}">
                                                        <span class="account-radio-checkmark"></span>
                                                    </label>
                                                <else />
                                                    <i class="fa fa-ban text-danger" title="未配置账户标识"></i>
                                                </notempty>
                                            </td>
                                        </tr>
                                    </foreach>
                                </tbody>
                            </table>
                        <else />
                            <div class="alert alert-warning">
                                <i class="fa fa-exclamation-triangle"></i> 未找到可用的银行账户
                            </div>
                        </notempty>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmAccountSelection" disabled>确认选择</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 初始化发票管理器 -->
    <script>
        $(document).ready(function() {
            // 检查InvoiceManager是否已定义
            if (typeof InvoiceManager === 'undefined') {
                console.error('InvoiceManager未定义，可能是invoice.js未正确加载');
                // 尝试动态加载invoice.js
                $.getScript('/Public/static/js/invoice.js', function() {
                    console.log('动态加载invoice.js成功');
                    initInvoiceManager();
                }).fail(function() {
                    console.error('动态加载invoice.js失败');
                    alert('加载发票管理模块失败，请刷新页面重试');
                });
            } else {
                initInvoiceManager();
            }
        });

        // 初始化发票管理器的函数
        function initInvoiceManager() {
            if (typeof InvoiceManager !== 'undefined') {
                // 获取转账金额并确保是数字类型
                var transferTotal = parseFloat('{$transferInfo.total}');
                console.log('转账金额:', transferTotal, '类型:', typeof transferTotal);
                console.log('转账ID:', '{$transferInfo.id}', '注册ID:', '{$registerInfo.id}');
                console.log('转账原始数据:', '{$transferInfo|json_encode}');

                // 确保金额是有效的数字
                if (isNaN(transferTotal) || transferTotal <= 0) {
                    console.error('转账金额无效:', transferTotal);
                    transferTotal =0; // 使用默认值
                    console.log('使用默认金额:', transferTotal);
                }

                window.invoiceManager = InvoiceManager.init({
                    scene: 'offline',
                    formId: 'invoiceForm',
                    modalId: 'invoiceModal',
                    deleteModalId: 'deleteConfirmModal',
                    listContainerId: 'invoiceTable',
                    urls: {
                        create: '{:U("OA/OfflineChain/createInvoice")}',
                        edit: '{:U("OA/OfflineChain/editInvoice")}',
                        delete: '{:U("OA/OfflineChain/deleteInvoice")}',
                        getDetail: '{:U("OA/OfflineChain/getInvoiceDetail")}'
                    },
                    orderTotal: transferTotal,
                    regId: '{$registerInfo.id}',
                    cid: '{$registerInfo.cid}',
                    payId: '{$transferInfo.id}'
                });
            }
        }
    </script>
</block>
