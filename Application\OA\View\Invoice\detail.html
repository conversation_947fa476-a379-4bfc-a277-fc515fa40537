<extend name="<PERSON><PERSON><PERSON>@Base/admin_base" />

<block name="style">
    <style>
        /* 美化按钮样式 */
        .btn {
            margin-bottom: 5px;
            border-radius: 4px;
            padding: 6px 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        /* 美化标签样式 */
        .label {
            display: inline-block;
            padding: 5px 10px;
            font-size: 13px;
            border-radius: 3px;
            margin-right: 5px;
        }

        /* 美化表格样式 */
        .table-bordered {
            border-radius: 4px;
            overflow: hidden;
        }

        .table-bordered th {
            background-color: #f5f5f5;
        }

        /* 美化面板样式 */
        .panel {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .panel:hover {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .panel-heading {
            padding: 12px 15px;
        }

        .panel-title {
            font-size: 16px;
            font-weight: 600;
        }

        /* 重要信息高亮 */
        .text-danger {
            color: #d9534f;
            font-weight: bold;
        }

        /* 提示框样式 */
        .well {
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-top: 20px;
        }

        /* 增强按钮可见度 */
        .btn-lg {
            font-size: 16px;
            border-radius: 6px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .btn-lg:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-3px);
        }



        /* 打印样式 */
        @media print {

            .btn,
            .navbar,
            footer,
            .alert,
            .well {
                display: none !important;
            }

            .panel {
                border: 1px solid #ddd !important;
                box-shadow: none !important;
            }

            .panel-heading {
                background-color: #f5f5f5 !important;
                color: #333 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            body {
                padding: 0;
                margin: 0;
            }
        }
    </style>
</block>

<block name="script">
    <script>
        // 发票ID和API URL
        var invoiceId = { $id };
        var apiUrl = "{$apiUrl}";
        var invoiceItemOptions = {: json_encode($invoiceItemOptions) };

        // 页面加载完成后执行
        $(document).ready(function () {
            // 加载发票数据
            loadInvoiceData();
        });

        // 加载发票数据
        function loadInvoiceData() {
            // 显示加载动画
            $("#content-container").html('<div class="loading"><i class="fa fa-spinner"></i><p>正在加载数据...</p></div>');

            // 调用API获取数据
            $.ajax({
                url: apiUrl,
                data: { id: invoiceId, json: 1, relations: 'audit,register,pay' },
                dataType: 'json',
                success: function (response) {
                    // 渲染页面
                    renderInvoiceDetail(response);
                },
                error: function (xhr, status, error) {
                    // 显示错误信息
                    $("#content-container").html('<div class="api-error"><h4><i class="fa fa-exclamation-triangle"></i> 加载失败</h4><p>无法加载发票数据: ' + error + '</p></div>');
                }
            });
        }

        // 渲染发票详情
        function renderInvoiceDetail(data) {
            if (!data || !data.id) {
                $("#content-container").html('<div class="api-error"><h4><i class="fa fa-exclamation-triangle"></i> 数据错误</h4><p>无法获取发票数据</p></div>');
                return;
            }

            var invoice = data;
            var html = '';

            // 构建页面HTML
            html += '<div class="panel panel-default">';
            html += '    <div class="panel-heading">';
            html += '        <h3 class="panel-title"><i class="fa fa-file-text-o"></i> 发票详情</h3>';
            html += '    </div>';
            html += '    <div class="panel-body">';

            // 操作按钮
            html += '        <div class="mb-3">';
            html += '            <a href="' + "{:U('OA/Invoice/index')}" + '" class="btn btn-default">';
            html += '                <i class="fa fa-arrow-left"></i> 返回列表';
            html += '            </a>';
            html += '            <a href="javascript:window.print();" class="btn btn-info pull-right">';
            html += '                <i class="fa fa-print"></i> 打印详情';
            html += '            </a>';
            html += '        </div>';

            // 渲染发票基本信息面板
            html += renderBasicInfoPanel(invoice);

            // 渲染收款公司信息面板（如果有）
            if (invoice.merchant_name) {
                html += renderMerchantInfoPanel(invoice);
            }

            // 渲染查账记录信息面板
            html += renderAuditInfoPanel(invoice);

            // 渲染注册信息面板（如果有）
            if (invoice.register) {
                html += renderRegisterInfoPanel(invoice);
            }

            html += '    </div>';
            html += '</div>';

            // 替换内容
            $("#content-container").html(html);
        }

        // 渲染发票基本信息面板
        function renderBasicInfoPanel(invoice) {
            var html = '';

            html += '<div class="panel panel-primary" id="invoice-basic-info">';
            html += '    <div class="panel-heading">';
            html += '        <h4 class="panel-title"><i class="fa fa-info-circle"></i> 发票基本信息</h4>';
            html += '    </div>';
            html += '    <div class="panel-body">';
            html += '        <div class="row">';

            // 左侧信息
            html += '            <div class="col-md-6">';
            html += '                <table class="table table-bordered table-striped">';
            html += '                    <tr>';
            html += '                        <th width="30%"><i class="fa fa-barcode"></i> 发票ID</th>';
            html += '                        <td>' + invoice.id + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-hashtag"></i> 发票系统编号</th>';
            html += '                        <td>' + (invoice.no || '--') + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-building"></i> 发票抬头</th>';
            html += '                        <td>' + invoice.invoice_title + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-money"></i> 发票金额</th>';
            html += '                        <td><span class="text-danger" style="font-weight: bold;">' + invoice.amount + '</span></td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-list-alt"></i> 发票类型</th>';
            html += '                        <td><span class="label label-' + invoice.invoice_type_class + '">' + invoice.invoice_type_text + '</span></td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-exchange"></i> 支付途径</th>';
            html += '                        <td><span class="label label-' + invoice.pay_type_class + '" style="font-size: 14px; padding: 6px 12px; display: inline-block; min-width: 100px; text-align: center;">' + invoice.pay_type_text + '</span></td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-check-circle"></i> 发票状态</th>';
            html += '                        <td><span class="label label-' + invoice.status_class + '">' + invoice.status_text + '</span></td>';
            html += '                    </tr>';
            html += '                </table>';
            html += '            </div>';

            // 右侧信息
            html += '            <div class="col-md-6">';
            html += '                <table class="table table-bordered table-striped">';
            html += '                    <tr>';
            html += '                        <th width="30%"><i class="fa fa-id-card"></i> 纳税人识别号</th>';
            html += '                        <td>' + (invoice.buyer_tax_num || '--') + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-phone"></i> 联系电话</th>';
            html += '                        <td>' + (invoice.buyer_phone || '--') + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-envelope"></i> 电子邮箱</th>';
            html += '                        <td>' + (invoice.buyer_email || '--') + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-shopping-cart"></i> 商品信息</th>';
            html += '                        <td>' + (invoice.goods_info_text || invoice.goods_info || '--') + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-clock-o"></i> 申请时间</th>';
            html += '                        <td>' + invoice.create_time_text + '</td>';
            html += '                    </tr>';
            html += '                </table>';
            html += '            </div>';
            html += '        </div>';

            // 专票特有信息
            if (invoice.invoice_type === 'bs') {
                html += '        <div class="row">';
                html += '            <div class="col-md-12">';
                html += '                <div class="panel panel-info">';
                html += '                    <div class="panel-heading">';
                html += '                        <h4 class="panel-title"><i class="fa fa-file-text"></i> 专票信息</h4>';
                html += '                    </div>';
                html += '                    <div class="panel-body">';
                html += '                        <div class="row">';
                html += '                            <div class="col-md-6">';
                html += '                                <table class="table table-bordered table-striped">';
                html += '                                    <tr>';
                html += '                                        <th width="30%"><i class="fa fa-map-marker"></i> 单位地址</th>';
                html += '                                        <td>' + (invoice.buyer_address || '--') + '</td>';
                html += '                                    </tr>';
                html += '                                    <tr>';
                html += '                                        <th><i class="fa fa-bank"></i> 开户行</th>';
                html += '                                        <td>' + (invoice.buyer_account_name || '--') + '</td>';
                html += '                                    </tr>';
                html += '                                    <tr>';
                html += '                                        <th><i class="fa fa-credit-card"></i> 账号</th>';
                html += '                                        <td>' + (invoice.buyer_account || '--') + '</td>';
                html += '                                    </tr>';
                html += '                                </table>';
                html += '                            </div>';
                html += '                        </div>';
                html += '                    </div>';
                html += '                </div>';
                html += '            </div>';
                html += '        </div>';
            }

            // 备注信息
            html += '        <div class="row">';
            html += '            <div class="col-md-12">';
            html += '                <div class="panel panel-default">';
            html += '                    <div class="panel-heading">';
            html += '                        <h4 class="panel-title"><i class="fa fa-comment"></i> 备注信息</h4>';
            html += '                    </div>';
            html += '                    <div class="panel-body">';
            html += '                        <p>' + (invoice.remark || '无') + '</p>';
            html += '                    </div>';
            html += '                </div>';
            html += '            </div>';
            html += '        </div>';

            html += '    </div>';
            html += '</div>';

            return html;
        }

        // 渲染收款公司信息面板
        function renderMerchantInfoPanel(invoice) {
            var html = '';

            html += '<div class="panel panel-success" id="merchant-info">';
            html += '    <div class="panel-heading">';
            html += '        <h4 class="panel-title"><i class="fa fa-building"></i> 收款公司信息</h4>';
            html += '    </div>';
            html += '    <div class="panel-body">';
            html += '        <div class="row">';
            html += '            <div class="col-md-6">';
            html += '                <table class="table table-bordered table-striped">';
            html += '                    <tr>';
            html += '                        <th width="30%"><i class="fa fa-building-o"></i> 公司名称</th>';
            html += '                        <td>' + (invoice.merchant_company || '--') + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-tag"></i> 公司别称</th>';
            html += '                        <td>' + (invoice.merchant_name || '--') + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-id-card-o"></i> 纳税人识别号</th>';
            html += '                        <td>' + (invoice.merchant_tax_num || '--') + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-key"></i> OA字典值</th>';
            html += '                        <td>' + (invoice.merchant_oa_dict || '--') + '</td>';
            html += '                    </tr>';
            html += '                </table>';
            html += '            </div>';
            html += '        </div>';
            html += '    </div>';
            html += '</div>';

            return html;
        }

        // 渲染查账记录信息面板
        function renderAuditInfoPanel(invoice) {
            var html = '';
            var auditRecord = invoice.audit;

            html += '<div class="panel panel-info" id="audit-record">';
            html += '    <div class="panel-heading">';
            html += '        <h4 class="panel-title"><i class="fa fa-check-square-o"></i> 查账信息</h4>';
            html += '    </div>';
            html += '    <div class="panel-body">';

            // 如果有查账记录
            if (auditRecord && auditRecord.id) {
                // 显示查账信息的详细内容
                html += '        <div class="row">';
                html += '            <div class="col-md-6">';
                html += '                <table class="table table-bordered table-striped">';
                html += '                    <tr>';
                html += '                        <th width="30%"><i class="fa fa-barcode"></i> OA订单号</th>';
                html += '                        <td>' + (auditRecord.oa_id || '--') + '</td>';
                html += '                    </tr>';
                html += '                    <tr>';
                html += '                        <th><i class="fa fa-check-circle"></i> 查账状态</th>';
                html += '                        <td><span class="label label-' + auditRecord.status_class + '">' + auditRecord.status_text + '</span></td>';
                html += '                    </tr>';
                html += '                    <tr>';
                html += '                        <th><i class="fa fa-clock-o"></i> 提交时间</th>';
                html += '                        <td>' + auditRecord.create_time_text + '</td>';
                html += '                    </tr>';
                html += '                </table>';
                html += '            </div>';
                html += '            <div class="col-md-6">';
                html += '                <table class="table table-bordered table-striped">';
                html += '                    <tr>';
                html += '                        <th width="30%"><i class="fa fa-money"></i> 金额</th>';
                html += '                        <td><span class="text-danger" style="font-weight: bold;">' + (auditRecord.total || '--') + ' ' + (auditRecord.currency || '') + '</span></td>';
                html += '                    </tr>';
                html += '                    <tr>';
                html += '                        <th><i class="fa fa-user"></i> 提交人</th>';
                html += '                        <td>' + (auditRecord.submit_name || '--') + '</td>';
                html += '                    </tr>';
                html += '                    <tr>';
                html += '                        <th><i class="fa fa-comment"></i> 备注</th>';
                html += '                        <td>' + (auditRecord.remark || '无') + '</td>';
                html += '                    </tr>';
                html += '                </table>';
                html += '            </div>';
                html += '        </div>';

                // 查账成功时显示查账详情和开票按钮
                if (auditRecord.status == 20) { // AUDIT_SUCCESS = 20
                    var registerInfo = invoice.register;
                    var auditDetailUrl = '';

                    // 根据支付类型设置不同的查账详情链接
                    switch (invoice.pay_type) {
                        case 0: // 线上支付
                            auditDetailUrl = "{:U('OA/Audit/onlineAudit')}" + '?reg_id=' + registerInfo.id;
                            break;
                        case 1: // 线下转账
                            auditDetailUrl = "{:U('OA/Audit/offlineAudit')}" + '?reg_id=' + registerInfo.id;
                            break;
                        case 2: // 游客支付
                            auditDetailUrl = "{:U('OA/Audit/guestAudit')}" + '?reg_id=' + registerInfo.id;
                            break;
                    }

                    html += '        <div class="well" style="background-color: #f5f9fc; border-left: 4px solid #5bc0de; margin-top: 15px;">';
                    html += '            <div class="row">';
                    html += '                <div class="col-md-7">';
                    html += '                    <h4 style="color: #31708f; margin-top: 5px;"><i class="fa fa-check-circle"></i> <strong>查账已成功，可以继续开票</strong></h4>';
                    html += '                    <p style="color: #555;">您可以查看查账详情或直接进行开票操作</p>';
                    html += '                </div>';
                    html += '                <div class="col-md-5 text-right">';
                    html += '                    <a href="' + auditDetailUrl + '" class="btn btn-info" style="margin-right: 10px;">';
                    html += '                        <i class="fa fa-search"></i> 查账详情';
                    html += '                    </a>';
                    html += '                    <a href="{:U(\'OA/Invoice/onlineInvoice\')}' + '?oa_id=' + auditRecord.oa_id + '" class="btn btn-success">';
                    html += '                        <i class="fa fa-file-text-o"></i> 点击开票';
                    html += '                    </a>';
                    html += '                </div>';
                    html += '            </div>';
                    html += '        </div>';
                }
            } else {
                // 没有查账记录，显示提交查账的按钮
                html += '        <div class="alert alert-warning">';
                html += '            <i class="fa fa-exclamation-triangle"></i> <strong>当前发票没有关联的查账记录</strong> - 开票前需要先完成查账流程';
                html += '        </div>';

                // 如果有注册信息，显示查账链接
                if (invoice.register && invoice.register.id) {
                    var registerInfo = invoice.register;
                    var auditLinkUrl = '';
                    var auditLinkText = '';
                    var auditLinkClass = '';
                    var auditLinkIcon = 'fa-check-circle';

                    // 根据支付类型设置不同的查账链接
                    switch (invoice.pay_type) {
                        case 0: // 线上支付
                            auditLinkUrl = "{:U('OA/Audit/onlineAudit')}" + '?reg_id=' + registerInfo.id;
                            auditLinkText = '点击提交线上查账信息';
                            auditLinkClass = 'btn-primary';
                            break;
                        case 1: // 线下转账
                            auditLinkUrl = "{:U('OA/Audit/offlineAudit')}" + '?reg_id=' + registerInfo.id;
                            auditLinkText = '点击提交线下查账信息';
                            auditLinkClass = 'btn-warning';
                            break;
                        case 2: // 游客支付
                            auditLinkUrl = "{:U('OA/Audit/guestAudit')}" + '?reg_id=' + registerInfo.id;
                            auditLinkText = '点击提交游客查账信息';
                            auditLinkClass = 'btn-info';
                            break;
                        default:
                            // 未知支付类型，不显示链接
                            break;
                    }

                    if (auditLinkUrl) {
                        // 使用更突出的设计显示查账按钮
                        html += '        <div class="well" style="background-color: #f5f9fc; border-left: 4px solid #5bc0de; margin-bottom: 20px;">';
                        html += '            <div class="row">';
                        html += '                <div class="col-md-8">';
                        html += '                    <h4 style="color: #31708f; margin-top: 5px;"><i class="fa fa-info-circle"></i> <strong>需要提交查账信息</strong></h4>';
                        html += '                    <p style="color: #555;">该发票尚未提交查账信息，请点击右侧按钮进行提交。查账成功后才能进行开票操作。</p>';
                        html += '                </div>';
                        html += '                <div class="col-md-4 text-center" style="padding-top: 15px;">';
                        html += '                    <a href="' + auditLinkUrl + '" class="btn ' + auditLinkClass + ' btn-lg" style="padding: 10px 20px;">';
                        html += '                        <i class="fa ' + auditLinkIcon + '"></i> ' + auditLinkText;
                        html += '                    </a>';
                        html += '                </div>';
                        html += '            </div>';
                        html += '        </div>';
                    }
                } else {
                    // 没有注册信息，显示错误信息
                    html += '        <div class="alert alert-danger">';
                    html += '            <i class="fa fa-times-circle"></i> <strong>无法提交查账：</strong> 缺少注册信息，请先完成注册流程。';
                    html += '        </div>';
                }
            }

            html += '    </div>';
            html += '</div>';

            return html;
        }

        // 渲染注册信息面板
        function renderRegisterInfoPanel(invoice) {
            var html = '';
            var registerInfo = invoice.register;

            if (!registerInfo) {
                return '';
            }

            // 注意：查账按钮已移至查账信息面板中，这里不再显示

            // 注册信息面板
            html += '<div class="panel panel-warning" id="register-info">';
            html += '    <div class="panel-heading">';
            html += '        <h4 class="panel-title"><i class="fa fa-user-circle"></i> 关联注册信息</h4>';
            html += '    </div>';
            html += '    <div class="panel-body">';
            html += '        <div class="row">';
            html += '            <div class="col-md-6">';
            html += '                <table class="table table-bordered table-striped">';
            html += '                    <tr>';
            html += '                        <th width="30%"><i class="fa fa-user"></i> 注册人</th>';
            html += '                        <td>';

            // 构建全名
            var fullname = registerInfo.firstname || '';
            if (registerInfo.middlename) {
                fullname += ' ' + registerInfo.middlename;
            }
            if (registerInfo.lastname) {
                fullname += ' ' + registerInfo.lastname;
            }

            html += fullname || '--';
            html += '                        </td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-envelope"></i> 邮箱</th>';
            html += '                        <td>' + (registerInfo.email || '--') + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-institution"></i> 单位</th>';
            html += '                        <td>';

            // 单位信息
            if (registerInfo.affiliation) {
                html += registerInfo.affiliation;
            } else if (registerInfo.position) {
                html += registerInfo.position;
            } else {
                html += '--';
            }

            html += '                        </td>';
            html += '                    </tr>';
            html += '                </table>';
            html += '            </div>';
            html += '            <div class="col-md-6">';
            html += '                <table class="table table-bordered table-striped">';
            html += '                    <tr>';
            html += '                        <th width="30%"><i class="fa fa-calendar"></i> 会议名称</th>';
            html += '                        <td>' + (registerInfo.event || '--') + '</td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-file-text"></i> 文章ID</th>';
            html += '                        <td>';

            // 文章ID
            if (registerInfo.paperid) {
                html += registerInfo.paperid;
            } else if (registerInfo.paper_id) {
                html += registerInfo.paper_id;
            } else {
                html += '--';
            }

            html += '                        </td>';
            html += '                    </tr>';
            html += '                    <tr>';
            html += '                        <th><i class="fa fa-money"></i> 注册费</th>';
            html += '                        <td>';

            // 注册费
            var fee = '--';
            var currency = '';

            if (registerInfo.total) {
                fee = registerInfo.total;
                if (registerInfo.currency) {
                    currency = registerInfo.currency;
                }
            } else if (registerInfo.fee) {
                fee = registerInfo.fee;
                if (registerInfo.currency) {
                    currency = registerInfo.currency;
                }
            }

            html += '<span class="text-danger" style="font-weight: bold;">' + fee + ' ' + currency + '</span>';
            html += '                        </td>';
            html += '                    </tr>';
            html += '                </table>';
            html += '            </div>';
            html += '        </div>';
            html += '    </div>';
            html += '</div>';

            return html;
        }
    </script>
</block>

<block name="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-file-text-o"></i> 发票详情</h3>
                    </div>
                    <div class="panel-body">
                        <!-- 操作按钮 -->
                        <div class="mb-3">
                            <a href="{:U('OA/Invoice/index')}" class="btn btn-default">
                                <i class="fa fa-arrow-left"></i> 返回列表
                            </a>
                            <if condition="$invoice.pay_type eq \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_GUEST">
                                <a href="{:U('OA/GuestChain/index', array('pay_id' => $invoice['pay_id']))}" class="btn btn-success">
                                    <i class="fa fa-link"></i> 点击提交游客查账信息
                                </a>
                            </if>
                            <a href="javascript:window.print();" class="btn btn-info pull-right">
                                <i class="fa fa-print"></i> 打印详情
                            </a>
                        </div>

                        <!-- 查账记录信息（移动到顶部） -->
                        <div class="panel panel-info" id="audit-record">
                            <div class="panel-heading">
                                <h4 class="panel-title"><i class="fa fa-check-square-o"></i> 查账信息</h4>
                            </div>

                            <div class="panel-body">
                                <!-- 已有查账记录 -->
                                <notempty name="auditInfo.has_record">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-bordered table-striped">
                                                <tr>
                                                    <th width="30%"><i class="fa fa-barcode"></i> OA订单号</th>
                                                    <td>{$auditInfo.record.oa_id}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-check-circle"></i> 查账状态</th>
                                                    <td><span
                                                            class="label label-{$auditInfo.record.status_class}">{$auditInfo.record.status_text}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-clock-o"></i> 提交时间</th>
                                                    <td>{$auditInfo.record.create_time_text}</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-bordered table-striped">
                                                <tr>
                                                    <th width="30%"><i class="fa fa-money"></i> 金额</th>
                                                    <td><span class="text-danger"
                                                            style="font-weight: bold;">{$auditInfo.record.amount}
                                                            {$auditInfo.record.unit}</span></td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-user"></i> 提交人</th>
                                                    <td>{$auditInfo.record.submit_name}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-comment"></i> 备注</th>
                                                    <td>{$auditInfo.record.remark|default="无"}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
{}
                                    <!-- 查账成功时显示查账详情和开票按钮 -->
                                    <if condition="$auditInfo.record.status eq \Common\Lib\OaAuditStatusConstants::AUDIT_SUCCESS">
                                        <div class="well" style="background-color: #f5f9fc; border-left: 4px solid #5bc0de; margin-top: 15px;">
                                            <div class="row">
                                                <div class="col-md-7">
                                                    <h4 style="color: #31708f; margin-top: 5px;"><i class="fa fa-check-circle"></i> <strong>查账已成功，可以继续开票</strong></h4>
                                                    <p style="color: #555;">您可以查看查账详情或直接进行开票操作</p>
                                                </div>
                                                <div class="col-md-5 text-right">
                                                    <if condition="$invoice.pay_type eq \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_ONLINE">
                                                        <a href="{:U('OA/Audit/onlineAudit', array('reg_id' => $registerInfo['id']))}" class="btn btn-info" style="margin-right: 10px;">
                                                            <i class="fa fa-search"></i> 查账详情
                                                        </a>
                                                    <elseif condition="$invoice.pay_type eq \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_OFFLINE" />
                                                        <a href="{:U('OA/Audit/offlineAudit', array('reg_id' => $registerInfo['id']))}" class="btn btn-info" style="margin-right: 10px;">
                                                            <i class="fa fa-search"></i> 查账详情
                                                        </a>
                                                    <elseif condition="$invoice.pay_type eq \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_GUEST" />
                                                        <a href="{:U('OA/Audit/guestAudit', array('pay_id' => $invoice['pay_id']))}" class="btn btn-info" style="margin-right: 10px;">
                                                            <i class="fa fa-search"></i> 查账详情
                                                        </a>
                                                        <a href="{:U('OA/GuestChain/index', array('pay_id' => $invoice['pay_id']))}" class="btn btn-success" style="margin-right: 10px;">
                                                            <i class="fa fa-link"></i> 一键查账和开票
                                                        </a>
                                                    </if>
                                                    <a href="{:U('OA/Invoice/onlineInvoice', array('oa_id' => $auditInfo.record.oa_id))}" class="btn btn-success">
                                                        <i class="fa fa-file-text-o"></i> 点击开票
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <else />
                                        <!-- 查账失败或处理中时显示一键查账和开票按钮 -->
                                        <div class="well" style="background-color: #fff8f8; border-left: 4px solid #f0ad4e; margin-top: 15px;">
                                            <div class="row">
                                                <div class="col-md-7">
                                                    <h4 style="color: #8a6d3b; margin-top: 5px;"><i class="fa fa-exclamation-circle"></i> <strong>查账未成功，可以尝试一键操作</strong></h4>
                                                    <p style="color: #555;">您可以使用一键查账和开票功能重新提交</p>
                                                </div>
                                                <div class="col-md-5 text-right">
                                                    <if condition="$invoice.pay_type eq \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_ONLINE">
                                                        <a href="{:U('OA/OnlineChain/confirmInfo', array('reg_id' => $registerInfo['id']))}" class="btn btn-warning" style="margin-right: 10px;">
                                                            <i class="fa fa-link"></i> 一键查账和开票
                                                        </a>
                                                    <elseif condition="$invoice.pay_type eq \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_OFFLINE" />
                                                        <a href="{:U('OA/OfflineChain/confirmInfo', array('reg_id' => $registerInfo['id']))}" class="btn btn-warning" style="margin-right: 10px;">
                                                            <i class="fa fa-link"></i> 一键查账和开票
                                                        </a>
                                                    <elseif condition="$invoice.pay_type eq \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_GUEST" />
                                                        <a href="{:U('OA/GuestChain/index', array('pay_id' => $invoice['pay_id']))}" class="btn btn-warning" style="margin-right: 10px;">
                                                            <i class="fa fa-link"></i> 一键查账和开票
                                                        </a>
                                                    </if>
                                                </div>
                                            </div>
                                        </div>
                                    </if>
                                </notempty>

                                <!-- 没有查账记录 -->
                                <empty name="auditInfo.has_record">
                                    <div class="alert alert-warning">
                                        <i class="fa fa-exclamation-triangle"></i> <strong>当前发票没有关联的查账记录</strong> - 开票前需要先完成查账流程
                                    </div>

                                    <!-- 显示查账链接 -->
                                    <notempty name="showAuditLink">
                                        <div class="well" style="background-color: #f5f9fc; border-left: 4px solid #5bc0de; margin-bottom: 20px;">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <h4 style="color: #31708f; margin-top: 5px;"><i class="fa fa-info-circle"></i> <strong>需要提交查账信息</strong></h4>
                                                    <p style="color: #555;">该发票尚未提交查账信息，请点击右侧按钮进行提交。查账成功后才能进行开票操作。</p>
                                                </div>
                                                <div class="col-md-4 text-center" style="padding-top: 15px;">
                                                    <a href="{$auditLinkUrl}" class="btn {$auditLinkClass} btn-lg" style="padding: 10px 20px; margin-bottom: 10px;">
                                                        <i class="fa {$auditLinkIcon}"></i> {$auditLinkText}
                                                    </a>

                                                    <if condition="$invoice.pay_type eq \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_ONLINE">
                                                        <a href="{:U('OA/OnlineChain/confirmInfo', array('reg_id' => $registerInfo['id']))}" class="btn btn-success btn-lg" style="padding: 10px 20px; width: 100%;">
                                                            <i class="fa fa-link"></i> 一键查账和开票
                                                        </a>
                                                    <elseif condition="$invoice.pay_type eq \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_OFFLINE" />
                                                        <a href="{:U('OA/OfflineChain/confirmInfo', array('reg_id' => $registerInfo['id']))}" class="btn btn-success btn-lg" style="padding: 10px 20px; width: 100%;">
                                                            <i class="fa fa-link"></i> 一键查账和开票
                                                        </a>
                                                    <elseif condition="$invoice.pay_type eq \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_GUEST" />
                                                        <a href="{:U('OA/GuestChain/index', array('pay_id' => $invoice['pay_id']))}" class="btn btn-success btn-lg" style="padding: 10px 20px; width: 100%;">
                                                            <i class="fa fa-link"></i> 一键查账和开票
                                                        </a>
                                                    </if>
                                                </div>
                                            </div>
                                        </div>
                                    </notempty>

                                    <!-- 显示错误信息 -->
                                    <notempty name="auditInfo.message">
                                        <div class="alert alert-danger">
                                            <i class="fa fa-times-circle"></i> <strong>无法提交查账：</strong> {$auditInfo.message}
                                        </div>
                                    </notempty>
                                </empty>
                            </div>
                        </div>

                        <!-- 发票基本信息 -->
                        <div class="panel panel-primary" id="invoice-basic-info">
                            <div class="panel-heading">
                                <h4 class="panel-title"><i class="fa fa-info-circle"></i> 发票基本信息</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-bordered table-striped">
                                            <tr>
                                                <th width="30%"><i class="fa fa-barcode"></i> 发票ID</th>
                                                <td>{$invoice.id}</td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-hashtag"></i> 发票系统编号</th>
                                                <td>{$invoice.no|default="--"}</td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-building"></i> 发票抬头</th>
                                                <td>{$invoice.invoice_title}</td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-money"></i> 发票金额</th>
                                                <td><span class="text-danger"
                                                        style="font-weight: bold;">{$invoice.amount}</span></td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-list-alt"></i> 发票类型</th>
                                                <td>
                                                    <span
                                                        class="label label-{$invoice.invoice_type_class}">{$invoice.invoice_type_text}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-exchange"></i> 支付途径</th>
                                                <td>
                                                    <span class="label label-{$invoice.pay_type_class}"
                                                        style="font-size: 14px; padding: 6px 12px; display: inline-block; min-width: 100px; text-align: center;">{$invoice.pay_type_text}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-check-circle"></i> 发票状态</th>
                                                <td><span
                                                        class="label label-{$invoice.status_class}">{$invoice.status_text}</span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-bordered table-striped">
                                            <tr>
                                                <th width="30%"><i class="fa fa-id-card"></i> 纳税人识别号</th>
                                                <td>{$invoice.buyer_tax_num}</td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-phone"></i> 联系电话</th>
                                                <td>{$invoice.buyer_phone}</td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-envelope"></i> 电子邮箱</th>
                                                <td>{$invoice.buyer_email}</td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-shopping-cart"></i> 商品信息</th>
                                                <td>{$invoice.goods_info_text}</td>
                                            </tr>
                                            <tr>
                                                <th><i class="fa fa-clock-o"></i> 申请时间</th>
                                                <td>{$invoice.create_time_text}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <!-- 专票特有信息 -->
                                <if condition="$invoice.invoice_type eq 'bs'">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="panel panel-info">
                                                <div class="panel-heading">
                                                    <h4 class="panel-title"><i class="fa fa-file-text"></i> 专票信息</h4>
                                                </div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <table class="table table-bordered table-striped">
                                                                <tr>
                                                                    <th width="30%"><i class="fa fa-map-marker"></i>
                                                                        单位地址</th>
                                                                    <td>{$invoice.buyer_address}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th><i class="fa fa-bank"></i> 开户行</th>
                                                                    <td>{$invoice.buyer_account_name}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th><i class="fa fa-credit-card"></i> 账号</th>
                                                                    <td>{$invoice.buyer_account}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </if>

                                <!-- 备注信息 -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="panel panel-default">
                                            <div class="panel-heading">
                                                <h4 class="panel-title"><i class="fa fa-comment"></i> 备注信息</h4>
                                            </div>
                                            <div class="panel-body">
                                                <p>{$invoice.remark|default="无"}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 收款公司信息 -->
                        <notempty name="invoice.merchant_name">
                            <div class="panel panel-success" id="merchant-info">
                                <div class="panel-heading">
                                    <h4 class="panel-title"><i class="fa fa-building"></i> 收款公司信息</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-bordered table-striped">
                                                <tr>
                                                    <th width="30%"><i class="fa fa-building-o"></i> 公司名称</th>
                                                    <td>{$invoice.merchant_company}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-tag"></i> 公司别称</th>
                                                    <td>{$invoice.merchant_name}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-id-card-o"></i> 纳税人识别号</th>
                                                    <td>{$invoice.merchant_tax_num}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-key"></i> OA字典值</th>
                                                    <td>{$invoice.merchant_oa_dict}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </notempty>

                        <!-- 查账记录信息已移动到顶部 -->

                        <!-- 注册信息 -->
                        <notempty name="registerInfo">
                            <div class="panel panel-warning" id="register-info">
                                <div class="panel-heading">
                                    <h4 class="panel-title"><i class="fa fa-user-circle"></i> 关联注册信息</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-bordered table-striped">
                                                <tr>
                                                    <th width="30%"><i class="fa fa-user"></i> 注册人</th>
                                                    <td>
                                                        <php>
                                                            $fullname = $registerInfo['firstname'];
                                                            if(!empty($registerInfo['middlename'])) {
                                                            $fullname .= ' ' . $registerInfo['middlename'];
                                                            }
                                                            if(!empty($registerInfo['lastname'])) {
                                                            $fullname .= ' ' . $registerInfo['lastname'];
                                                            }
                                                            echo $fullname;
                                                        </php>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-envelope"></i> 邮箱</th>
                                                    <td>{$registerInfo.email}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-institution"></i> 单位</th>
                                                    <td>
                                                        <php>
                                                            if(!empty($registerInfo['affiliation'])) {
                                                            echo $registerInfo['affiliation'];
                                                            } else if(!empty($registerInfo['position'])) {
                                                            echo $registerInfo['position'];
                                                            } else {
                                                            echo "--";
                                                            }
                                                        </php>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-bordered table-striped">
                                                <tr>
                                                    <th width="30%"><i class="fa fa-calendar"></i> 会议名称</th>
                                                    <td>{$registerInfo.event}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-file-text"></i> 文章ID</th>
                                                    <td>
                                                        <php>
                                                            if(!empty($registerInfo['paperid'])) {
                                                            echo $registerInfo['paperid'];
                                                            } else if(!empty($registerInfo['paper_id'])) {
                                                            echo $registerInfo['paper_id'];
                                                            } else {
                                                            echo "--";
                                                            }
                                                        </php>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-money"></i> 注册费</th>
                                                    <td>
                                                        <php>
                                                            $fee = "--";
                                                            $currency = "";

                                                            if(!empty($registerInfo['total'])) {
                                                            $fee = $registerInfo['total'];
                                                            if(!empty($registerInfo['currency'])) {
                                                            $currency = $registerInfo['currency'];
                                                            }
                                                            } else if(!empty($registerInfo['fee'])) {
                                                            $fee = $registerInfo['fee'];
                                                            if(!empty($registerInfo['currency'])) {
                                                            $currency = $registerInfo['currency'];
                                                            }
                                                            }

                                                            echo '<span class="text-danger"
                                                                style="font-weight: bold;">'.$fee.'
                                                                '.$currency.'</span>';
                                                        </php>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </notempty>
                    </div>
                </div>
            </div>
        </div>
    </div>

</block>