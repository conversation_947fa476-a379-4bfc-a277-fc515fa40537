<extend name="Base/admin_base" />

<block name="style">
<style>
    .records-table {
        border-radius: 5px;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .records-table thead th {
        background-color: #3498db;
        color: white;
        font-weight: 600;
        border-bottom: 2px solid #2980b9;
        text-align: center;
        vertical-align: middle !important;
    }
    .btn-info {
        background-color: #17a2b8;
        border-color: #17a2b8;
        transition: all 0.3s;
    }
    .btn-info:hover {
        background-color: #138496;
        border-color: #117a8b;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .records-table tbody tr:hover {
        background-color: #f5f7fa;
    }
    .records-table tbody td {
        vertical-align: middle !important;
    }
    .amount {
        font-weight: bold;
        color: #e74c3c;
    }
    .email-text {
        color: #7f8c8d;
        font-size: 12px;
    }
    .conference-badge {
        display: inline-block;
        padding: 4px 8px;
        background-color: #f1f5f9;
        border: 1px solid #dde6ed;
        border-radius: 4px;
        color: #34495e;
        font-weight: 600;
    }
    .admin-name {
        font-weight: 600;
    }
    .date-text {
        color: #7f8c8d;
        font-size: 13px;
    }
    .remark-text {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .remark-text:hover {
        white-space: normal;
        overflow: visible;
    }
    .btn-claim-new {
        background-color: #3498db;
        border-color: #2980b9;
        transition: all 0.3s;
    }
    .btn-claim-new:hover {
        background-color: #2980b9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .empty-records {
        padding: 30px;
        text-align: center;
        color: #7f8c8d;
    }
    .empty-records i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #bdc3c7;
    }
    .pagination-container {
        margin-top: 20px;
        text-align: center;
    }
</style>
</block>
<block name="breadcrumb">
    <ol class="breadcrumb">
      <li><a href="{:U('Index/index')}">首页</a></li>
      <li><a href="">我认领的订单</a></li>
    </ol>
  </block>
<block name="content">
<div class="row">
    <div class="col-md-12">
        <div class="box">
            <div class="box-header with-border" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <div>
                    <h3 class="box-title" style="font-size: 22px; margin: 0;">
                        <i class="fa fa-history"></i> 订单认领记录
                    </h3>
                </div>
                <div>
                    <a href="{:U('index')}" class="btn btn-primary btn-claim-new" style="margin-left: 15px;">
                        <i class="fa fa-plus"></i> 认领新订单
                    </a>
                </div>
            </div>
            <div class="box-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover records-table">
                        <thead>
                            <tr>
                                <th width="5%">ID</th>
                                <th width="15%">订单号/发票申请链接</th>
                                <th width="15%">付款人</th>
                                <th width="10%">金额</th>
                                <th width="15%">会议简称</th>
                                <th width="10%">认领人</th>
                                <th width="15%">认领时间</th>
                                <th width="10%">备注</th>
                                <th width="10%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <empty name="list">
                                <tr>
                                    <td colspan="9" class="empty-records">
                                        <i class="fa fa-folder-open-o"></i>
                                        <p>暂无认领记录</p>
                                        <a href="{:U('index')}" class="btn btn-primary btn-sm">
                                            <i class="fa fa-plus"></i> 去认领订单
                                        </a>
                                    </td>
                                </tr>
                            <else />
                                <volist name="list" id="item">
                                    <tr>
                                        <td class="text-center">{$item.id}</td>
                                        <td>
                                            <a href="{:U('Home/GuestInvoice/index', array('order_id'=>$item['orderid']))}" title="发票申请" style="display: inline-block;">
                                                <strong>{$item.orderid}</strong>
                                                <i class="fa fa-chain text-primary" style="margin-left: 5px;"></i>
                                            </a>
                                        </td>
                                        <td>
                                            {$item.fullname}<br>
                                            <span class="email-text"><i class="fa fa-envelope-o"></i> {$item.email}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="amount">
                                                {$item.total}
                                                <if condition="$item.moneytype eq 0">
                                                    CNY
                                                <else />
                                                    USD
                                                </if>
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="conference-badge">
                                                <i class="fa fa-calendar"></i> {$item.event}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="admin-name">
                                                <i class="fa fa-user"></i> {$item.admin_name}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="date-text">
                                                <i class="fa fa-clock-o"></i>
                                                <php>
                                                    if (!empty($item['claim_time']) && $item['claim_time'] > 0) {
                                                        echo date('Y-m-d H:i:s', $item['claim_time']);
                                                    } else {
                                                        echo '未知时间';
                                                    }
                                                </php>
                                            </span>
                                        </td>
                                        <td>
                                            <if condition="empty($item.remark)">
                                                <span class="text-muted">无备注</span>
                                            <else />
                                                <span class="remark-text" title="{$item.remark}">
                                                    {$item.remark}
                                                </span>
                                            </if>
                                        </td>
                                        <td class="text-center">
                                            <a href="{:U('OA/GuestChain/confirmInfo', array('pay_id'=>$item['pay_id']))}" class="btn btn-sm btn-info">
                                                <i class="fa fa-search-plus"></i> 查账与开票
                                            </a>
                                        </td>
                                    </tr>
                                </volist>
                            </empty>
                        </tbody>
                    </table>
                </div>

                <div class="row">
                    <div class="col-sm-12 pagination-container">
                        <div class="dataTables_paginate paging_simple_numbers">
                            {$page}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</block>
