<?php

namespace OA\Controller;

use OA\Controller\BaseController;

/**
 * 线下一键查账和开票控制器
 * 用于处理线下转账的一键查账和开票操作
 */
class OfflineChainController extends BaseController
{

    protected $auditService;
    protected $chainService;
    protected $permissionService;
    protected $invoiceService;
    protected $queryService; // 新增查询服务

    /**
     * 初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        // 初始化服务
        $this->auditService = new \OA\Service\AuditService();
        $this->chainService = new \OA\Service\ChainOperationService();
        $this->permissionService = new \Common\Service\PermissionService();
        // 使用 OfflineInvoiceService 专门处理线下发票
        $this->invoiceService = new \OA\Service\OfflineInvoiceService();
        // 初始化查询服务
        $this->queryService = new \OA\Service\AuditInvoiceQueryService();
    }

    /**
     * 获取发票项目选项
     * @return array 发票项目选项
     */
    protected function getInvoiceItemOptions()
    {
        // 使用 InvoiceItemService 获取标准格式的发票项目选项
        $invoiceItemService = new \Common\Service\InvoiceItemService();
        return $invoiceItemService->getInvoiceItemOptions();
    }

    /**
     * 显示确认注册和转账信息页面（步骤1）
     */
    public function confirmInfo()
    {
        try {
            $regId = I('get.reg_id', 0, 'intval');

            // 参数校验
            if (empty($regId)) {
                throw new \Exception('参数错误：注册ID不能为空');
            }

            // 获取注册信息
            $auditInfo = $this->auditService->getOfflineAuditInfo($regId);

            // 检查用户是否有权限访问该会议
            if (!empty($auditInfo['registerInfo']['cid']) && !$this->canAccessConference($auditInfo['registerInfo']['cid'])) {
                throw new \Exception('您没有权限访问该会议的查账信息');
            }

            // 获取转账信息
            $transferModel = D('SubTransfer');
            $transferInfo = $transferModel->where(['reg_id' => $regId])->find();

            if (empty($transferInfo)) {
                throw new \Exception('未找到相关的转账记录');
            }
            
            // 获取收款账户信息
            $receiverInfo = null;
            if (!empty($transferInfo['receiver_id'])) {
                $bankModel = D('SubBank');
                $receiverInfo = $bankModel->find($transferInfo['receiver_id']);
                
                // 处理特殊字符
                if (!empty($receiverInfo['bank'])) {
                    $receiverInfo['bank_info'] = str_replace(array('{br}'), array('<br>'), $receiverInfo['bank']);
                }
            }

            // 计算转账金额与注册费用是否一致
            $amountComparisonData = $this->compareTransferAndRegistrationAmount($transferInfo, $auditInfo['registerInfo']);
            
            // 准备转账时间显示，只显示年月日
            $displayTransferTime = '-';
            $formattedTransferTime = '';
            if (!empty($transferInfo['transfer_time']) && $transferInfo['transfer_time'] > 0) {
                $displayTransferTime = date('Y-m-d', $transferInfo['transfer_time']);
                $formattedTransferTime = $displayTransferTime;
            }
            
            // 准备货币类型显示
            $currencyLabel = '<span class="label label-default">其他</span>';
            if ($transferInfo['currency'] == 0) {
                $currencyLabel = '<span class="label label-success">人民币 (CNY)</span>';
            } elseif ($transferInfo['currency'] == 1) {
                $currencyLabel = '<span class="label label-info">美元 (USD)</span>';
            }
            
            // 准备转账金额显示
            $formattedAmount = $transferInfo['total'];
            if ($transferInfo['currency'] == 0) {
                $formattedAmount = '￥' . $transferInfo['total'] . ' 元';
            } elseif ($transferInfo['currency'] == 1) {
                $formattedAmount = '$' . $transferInfo['total'] . ' 美元';
            }
            
            // 准备转账凭证HTML
            $receiptHtml = '<p class="form-control-static"><span class="text-muted">未提供转账凭证</span></p>';
            if (!empty($transferInfo['transfer_pic'])) {
                $receiptHtml = '<div class="receipt-thumbnail-container">';
                $receiptHtml .= '<a href="javascript:void(0);" class="receipt-thumbnail" onclick="showReceiptModal(\''.ATTACHMENT_URL.$transferInfo['transfer_pic'].'\')">';
                $receiptHtml .= '<img src="'.ATTACHMENT_URL.$transferInfo['transfer_pic'].'" alt="转账凭证" class="img-thumbnail receipt-img" style="max-height: 120px;">';
                $receiptHtml .= '<div class="receipt-overlay"><i class="fa fa-search-plus"></i> 点击查看大图</div>';
                $receiptHtml .= '</a></div>';
            }
            
            // 准备备注信息HTML
            $remarkHtml = '';
            if (!empty($transferInfo['remark'])) {
                $remarkHtml = '<div class="row" style="margin-top: 10px;">';
                $remarkHtml .= '<div class="col-md-12"><div class="form-group">';
                $remarkHtml .= '<label class="control-label key-field"><i class="fa fa-comment"></i> 备注信息：</label>';
                $remarkHtml .= '<div class="well well-sm" style="margin-bottom: 0;">'.$transferInfo['remark'].'</div>';
                $remarkHtml .= '</div></div></div>';
            }
            
            // 准备货币类型提示
            $currencyWarning = '';
            if ($transferInfo['currency'] == 1) {
                $currencyWarning = '<div class="alert alert-warning" id="currency-warning" ';
                $currencyWarning .= 'style="border-left: 4px solid #f0ad4e; border-radius: 0; margin-top: 15px;">';
                $currencyWarning .= '<p><i class="fa fa-exclamation-triangle fa-lg"></i> <strong>注意：</strong> ';
                $currencyWarning .= '当前订单为美元(USD)支付，不支持开具发票。您只能执行查账操作。</p></div>';
            }
            
            // 赋值到模板
            $this->assign('registerInfo', $auditInfo['registerInfo']);
            $this->assign('transferInfo', $transferInfo);
            $this->assign('receiverInfo', $receiverInfo);
            $this->assign('amountComparisonData', $amountComparisonData);
            
            // 赋值处理好的显示变量
            $this->assign('displayTransferTime', $displayTransferTime);
            $this->assign('formattedTransferTime', $formattedTransferTime);
            $this->assign('currencyLabel', $currencyLabel);
            $this->assign('formattedAmount', $formattedAmount);
            $this->assign('receiptHtml', $receiptHtml);
            $this->assign('remarkHtml', $remarkHtml);
            $this->assign('currencyWarning', $currencyWarning);
            
            $this->display();
        } catch (\Exception $e) {
            // 捕获异常
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 更新转账信息
     * 管理员修改转账信息
     */
    public function updateTransferInfo()
    {
        if (!IS_POST) {
            $this->error('非法请求');
        }

        $transferId = I('post.transfer_id', 0, 'intval');
        $regId = I('post.reg_id', 0, 'intval');
        $transferName = I('post.transfer_name', '', 'trim');
        $transferAccount = I('post.transfer_account', '', 'trim');
        $transferTime = I('post.transfer_time', '', 'trim');
        $currency = I('post.currency', 0, 'intval');
        $total = I('post.total', 0, 'floatval');
        $remark = I('post.remark', '', 'trim');

        // 参数验证
        if (empty($transferId) || empty($regId)) {
            $this->ajaxReturn(['status' => 0, 'message' => '参数错误：转账ID或注册ID不能为空']);
        }

        if (empty($transferName)) {
            $this->ajaxReturn(['status' => 0, 'message' => '转账人不能为空']);
        }

        if ($total <= 0) {
            $this->ajaxReturn(['status' => 0, 'message' => '转账金额必须大于0']);
        }

        try {
            // 检查转账记录是否存在
            $transferModel = D('SubTransfer');
            $transferInfo = $transferModel->where(['id' => $transferId, 'reg_id' => $regId])->find();
            
            if (empty($transferInfo)) {
                $this->ajaxReturn(['status' => 0, 'message' => '未找到相关的转账记录']);
            }
            
            // 处理转账时间，只处理年月日
            if (empty($transferTime)) {
                $transferTimeStamp = time();
            } else {
                // 处理HTML5日期选择器提交的YYYY-MM-DD格式
                // 将时间设置为当天的00:00:00
                $transferTimeStamp = strtotime($transferTime . ' 00:00:00');
                if ($transferTimeStamp === false) {
                    $transferTimeStamp = time();
                }
            }
            
            // 准备更新数据
            $updateData = [
                'transfer_name' => $transferName,
                'transfer_account' => $transferAccount,
                'transfer_time' => $transferTimeStamp,
                'currency' => $currency,
                'total' => $total,
                'remark' => $remark,
                'update_time' => time()
            ];
            
            // 更新转账信息
            $result = $transferModel->where(['id' => $transferId])->save($updateData);

            if ($result === false) {
                $this->ajaxReturn(['status' => 0, 'message' => '更新转账信息失败']);
            }

            // 记录操作日志
            \Think\Log::write('管理员修改了转账信息，转账ID: ' . $transferId . ', 注册ID: ' . $regId, 'INFO');

            // 返回成功信息
            $this->ajaxReturn(['status' => 1, 'message' => '转账信息更新成功']);
        } catch (\Exception $e) {
            $this->ajaxReturn(['status' => 0, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * 删除转账信息
     * 仅允许删除已驳回的转账信息
     */
    public function deleteTransfer()
    {
        if (!IS_POST) {
            $this->error('非法请求');
        }
        
        $transferId = I('post.transfer_id', 0, 'intval');
        $regId = I('post.reg_id', 0, 'intval');
        
        // 参数验证
        if (empty($transferId) || empty($regId)) {
            $this->ajaxReturn(['status' => 0, 'message' => '参数错误：转账ID或注册ID不能为空']);
        }
        
        try {
            // 获取转账信息
            $transferModel = D('SubTransfer');
            $transferInfo = $transferModel->where(['id' => $transferId, 'reg_id' => $regId])->find();
            
            if (empty($transferInfo)) {
                $this->ajaxReturn(['status' => 0, 'message' => '未找到相关的转账记录']);
            }
            
            // 检查转账状态，只允许删除已驳回的转账信息
            if ($transferInfo['status'] != 40) { // 40为驳回状态
                $this->ajaxReturn(['status' => 0, 'message' => '只能删除已驳回的转账信息']);
            }
            
            // 获取注册信息，检查权限
            $registerModel = D('SubRegister');
            $registerInfo = $registerModel->where(['id' => $regId])->find();
            
            // 检查用户是否有权限访问该会议
            if (!empty($registerInfo['cid']) && !$this->canAccessConference($registerInfo['cid'])) {
                $this->ajaxReturn(['status' => 0, 'message' => '您没有权限删除该转账信息']);
            }
            
            // 开始事务
            $transferModel->startTrans();
            
            try {
                // 删除转账信息
                $deleteResult = $transferModel->where(['id' => $transferId])->delete();
                
                if ($deleteResult === false) {
                    throw new \Exception('删除转账信息失败');
                }
                
                // 提交事务
                $transferModel->commit();
                
                // 记录日志
                \Think\Log::write('管理员删除了转账信息，转账ID: ' . $transferId . ', 注册ID: ' . $regId, 'INFO');
                
                // 返回成功信息
                $this->ajaxReturn([
                    'status' => 1, 
                    'message' => '转账信息删除成功',
                    'redirect_url' => U('Mpanel/Index/index') // 删除成功后跳转到首页
                ]);
                
            } catch (\Exception $e) {
                // 回滚事务
                $transferModel->rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            $this->ajaxReturn(['status' => 0, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 比较转账金额与注册费用
     * 检查转账金额与注册费用是否一致，并返回比对结果
     * 
     * @param array $transferInfo 转账信息
     * @param array $registerInfo 注册信息
     * @return array 比对结果数据
     */
    protected function compareTransferAndRegistrationAmount($transferInfo, $registerInfo)
    {
        // 初始化返回数据
        $result = array(
            'amountMismatch' => false,
            'transferAmount' => 0,
            'registrationAmount' => 0,
            'difference' => 0,
            'currencySymbol' => '',
            'currencyName' => '',
            'hasFee' => false,
            'feeAmount' => 0
        );
        
        // 获取转账金额
        $transferAmount = floatval($transferInfo['total']);
        $result['transferAmount'] = $transferAmount;
        
        // 根据货币类型获取相应的注册费用金额
        if($transferInfo['currency'] == 0 && isset($registerInfo['total']['cny'])) {
            // 人民币
            $registrationAmount = floatval($registerInfo['total']['cny']);
            $result['currencySymbol'] = '¥';
            $result['currencyName'] = '人民币';
        } elseif($transferInfo['currency'] == 1 && isset($registerInfo['total']['usd'])) {
            // 美元
            $registrationAmount = floatval($registerInfo['total']['usd']);
            $result['currencySymbol'] = '$';
            $result['currencyName'] = '美元';
            
            // 美元汇款需要考虑30元手续费
            $result['hasFee'] = true;
            $result['feeAmount'] = 30;
        }
        
        $result['registrationAmount'] = $registrationAmount;
        
        // 计算差额，考虑手续费
        if ($transferInfo['currency'] == 1 && $result['hasFee']) {
            // 美元汇款：作者填写的金额 = 系统金额 + 30美元手续费
            // 因此比较时，应该是 |转账金额 - (注册金额 + 手续费)|
            $expectedAmount = $registrationAmount + $result['feeAmount'];
            $difference = abs($transferAmount - $expectedAmount);
            
            // 更新结果中的差额
            $result['difference'] = $difference;
            $result['expectedAmount'] = $expectedAmount; // 添加预期金额（含手续费）
        } else {
            // 人民币汇款：直接比较
            $difference = abs($transferAmount - $registrationAmount);
            $result['difference'] = $difference;
        }
        
        // 允许小额差异(比如0.01差异)
        $tolerance = 0.01;
        $result['amountMismatch'] = $difference > $tolerance;
        
        return $result;
    }
    
    /**
     * 显示一键查账和开票表单（步骤2）
     */
    public function index()
    {
        try {
            $regId = I('get.reg_id', 0, 'intval');

            // 参数校验
            if (empty($regId)) {
                throw new \Exception('参数错误：注册ID不能为空');
            }

            // 获取注册信息
            $auditInfo = $this->auditService->getOfflineAuditInfo($regId);

            // 检查用户是否有权限访问该会议
            if (!empty($auditInfo['registerInfo']['cid']) && !$this->canAccessConference($auditInfo['registerInfo']['cid'])) {
                throw new \Exception('您没有权限访问该会议的查账信息');
            }

            // 获取转账信息
            $transferModel = D('SubTransfer');
            $transferInfo = $transferModel->where(['reg_id' => $regId])->find();

            if (empty($transferInfo)) {
                throw new \Exception('未找到相关的转账记录');
            }

            // 检查币种，如果是美元则不显示发票选项
            $isUSD = ($transferInfo['currency'] == \Common\Lib\CurrencyTypeConstants::CURRENCY_USD);

            // 获取该注册ID关联的发票信息
            $invoiceModel = D('OaInvoice', 'OA');
            $invoices = $invoiceModel->where(['reg_id' => $regId])->select();

            // 获取发票项目选项
            $invoiceItemOptions = $this->getInvoiceItemOptions();

            // 检查是否已存在查账记录
            $oaAuditAccountService = D('OaAuditAccount', 'OA');
            $existingAudit = $oaAuditAccountService->where(['reg_id' => $regId])->find();
            $isAuditSubmitted = !empty($existingAudit) && $existingAudit['status'] != \Common\Lib\OaAuditStatusConstants::AUDIT_DRAFT;

            // 获取银行账户信息
            $bankAccounts = [];
            if (!empty($auditInfo['registerInfo']['cid'])) {
                // 使用全局函数获取银行账户信息，传入null作为货币类型，返回所有可用账户
                $bankAccounts = getBankInfoByCidAndCurrency($auditInfo['registerInfo']['cid'], null);
                
                // 添加货币类型标记，方便前端显示
                if (!empty($bankAccounts)) {
                    foreach ($bankAccounts as &$bank) {
                        // 根据银行账户类型添加货币标记
                        if ($bank['type'] == 1) {
                            $bank['currency_label'] = '<span class="label label-info">USD</span>';
                            $bank['currency_type'] = 'USD';
                        } else {
                            $bank['currency_label'] = '<span class="label label-success">CNY</span>';
                            $bank['currency_type'] = 'CNY';
                        }
                    }
                    unset($bank); // 释放引用
                }
            }

            // 生成表单令牌，用于防止重复提交
            $formToken = md5($regId . '_' . time() . '_' . mt_rand(1000, 9999));
            session('offline_chain_form_token_' . $regId, $formToken);

            // 赋值到模板
            $this->assign('registerInfo', $auditInfo['registerInfo']);
            $this->assign('transferInfo', $transferInfo);
            $this->assign('invoices', $invoices);
            $this->assign('isUSD', $isUSD);
            $this->assign('invoiceItemOptions', $invoiceItemOptions);
            $this->assign('isAuditSubmitted', $isAuditSubmitted);
            $this->assign('auditRecord', $existingAudit);
            $this->assign('bankAccounts', $bankAccounts);
            $this->assign('form_token', $formToken);
            $this->display();
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 处理一键查账和开票提交
     */
    public function submit()
    {
        try {
            // 获取注册ID
            $regId = I('post.reg_id', 0, 'intval');

            // 参数校验
            if (empty($regId)) {
                throw new \Common\Exception\LocalOperationException('参数错误：注册ID不能为空');
            }

            // 验证表单令牌，防止重复提交
            $formToken = I('post.form_token', '', 'trim');
            $sessionToken = session('offline_chain_form_token_' . $regId);

            if (empty($formToken) || $formToken !== $sessionToken) {
                \Think\Log::write('表单令牌验证失败，可能是重复提交 - 注册ID: ' . $regId . ', 表单令牌: ' . $formToken . ', 会话令牌: ' . $sessionToken, 'WARN');
                throw new \Common\Exception\LocalOperationException('表单已过期或已提交，请刷新页面重试');
            }

            // 检查是否已存在查账记录
            $oaAuditAccountService = new \OA\Service\OaAuditAccountService();
            $existingAudit = $oaAuditAccountService->checkExistingRecord($regId, 0);

            if (!empty($existingAudit) && $existingAudit['status'] != \Common\Lib\OaAuditStatusConstants::AUDIT_DRAFT) {
                \Think\Log::write('尝试重复提交查账 - 注册ID: ' . $regId . ', 已存在记录ID: ' . $existingAudit['id'] . ', 状态: ' . $existingAudit['status'], 'WARN');
                throw new \Common\Exception\LocalOperationException('该查账信息已经提交到OA，请勿重复提交');
            }

            // 使用文件锁防止并发提交
            $lockFile = RUNTIME_PATH . 'offline_chain_lock_' . $regId . '.lock';
            $fp = fopen($lockFile, 'w+');

            if (!flock($fp, LOCK_EX | LOCK_NB)) {
                \Think\Log::write('获取锁失败，可能有其他请求正在处理 - 注册ID: ' . $regId, 'WARN');
                throw new \Common\Exception\LocalOperationException('系统正在处理您的请求，请勿重复提交');
            }

            try {
                // 再次检查是否已存在查账记录（双重检查，防止锁获取前已有其他请求提交成功）
                $existingAudit = $oaAuditAccountService->checkExistingRecord($regId, 0);

                if (!empty($existingAudit) && $existingAudit['status'] != \Common\Lib\OaAuditStatusConstants::AUDIT_DRAFT) {
                    \Think\Log::write('锁内二次检查发现重复提交 - 注册ID: ' . $regId . ', 已存在记录ID: ' . $existingAudit['id'] . ', 状态: ' . $existingAudit['status'], 'WARN');
                    throw new \Common\Exception\LocalOperationException('该查账信息已经提交到OA，请勿重复提交');
                }

                // 处理链式操作
                $result = $this->chainService->processOfflineChainOperation(I('post.'));

                // 保存结果到会话，用于结果页面显示
                session('chain_operation_result', $result);

                // 清除表单令牌，防止重复提交
                session('offline_chain_form_token_' . $regId, null);

                if ($result['status']) {
                    // 操作成功，重定向到结果页面
                    $this->ajaxReturn([
                        'status' => true,
                        'message' => $result['message'],
                        'redirect' => true,
                        'url' => U('OA/OfflineChain/result', ['reg_id' => $regId])
                    ]);
                } else {
                    // 操作失败，返回错误信息
                    $this->ajaxReturn($result);
                }
            } finally {
                // 释放锁
                flock($fp, LOCK_UN);
                fclose($fp);
                @unlink($lockFile);
            }
        } catch (\Common\Exception\RemoteApiException $e) {
            // 远程API异常
            \Think\Log::write('一键查账和开票远程API异常: ' . $e->getMessage(), 'ERROR');

            // 返回远程错误信息
            $this->ajaxReturn([
                'status' => false,
                'message' => $e->getMessage(),
                'error_type' => '远程错误',
                'error_source' => 'remote'
            ]);
        } catch (\Common\Exception\LocalOperationException $e) {
            // 本地操作异常
            \Think\Log::write('一键查账和开票本地异常: ' . $e->getMessage(), 'ERROR');

            // 返回本地错误信息
            $this->ajaxReturn([
                'status' => false,
                'message' => $e->getMessage(),
                'error_type' => '本地错误',
                'error_source' => 'local'
            ]);
        } catch (\Exception $e) {
            // 其他未预期的异常
            \Think\Log::write('一键查账和开票未预期异常: ' . $e->getMessage(), 'ERROR');

            // 返回系统错误信息
            $this->ajaxReturn([
                'status' => false,
                'message' => $e->getMessage(),
                'error_type' => '系统错误',
                'error_source' => 'system'
            ]);
        }
    }

    /**
     * 显示操作结果
     *
     * @param int $reg_id 注册ID
     */
    public function result($reg_id = null)
    {
        // 检查参数
        if (empty($reg_id)) {
            $this->error('注册ID不能为空');
        }

        // 使用查询服务获取查账和发票信息
        $queryResult = $this->queryService->getAuditAndInvoiceByRegId($reg_id);

        if (!$queryResult['success']) {
            $this->error($queryResult['message']);
        }

        // 获取会议ID并检查权限
        $conferenceId = $queryResult['data']['register_info']['cid'];
        if (!empty($conferenceId) && !$this->canAccessConference($conferenceId)) {
            $this->error('您没有权限访问该会议');
        }

        // 设置模板变量
        $this->assign('auditRecord', $queryResult['data']['audit_record']);
        $this->assign('invoiceRecord', $queryResult['data']['invoice_record']);
        $this->assign('invoices', $queryResult['data']['invoices']);
        $this->assign('registerInfo', $queryResult['data']['register_info']);
        $this->assign('hasAudit', $queryResult['data']['has_audit']);
        $this->assign('hasInvoice', $queryResult['data']['has_invoice']);
        $this->assign('regId', $reg_id);

        // 显示模板
        $this->display();
    }

    /**
     * 通过AJAX创建新发票
     */
    public function createInvoice()
    {
        try {
            // 获取POST数据
            $postData = I('post.');

            // 特别处理金额字段，确保它是标准格式
            if (isset($postData['amount'])) {
                $postData['amount'] = number_format((float)$postData['amount'], 2, '.', '');
            }

            // 添加必要的字段
            if (empty($postData['event'])) {
                // 获取会议简称
                $conferenceModel = M('Conference');
                $conference = $conferenceModel->where(['id' => $postData['cid']])->find();
                $postData['event'] = $conference ? $conference['abbr'] : '';
            }

            // 添加回调URL
            $postData['call_back_url'] = $this->getCallbackUrl();

            // 使用发票服务创建发票
            $result = $this->invoiceService->createInvoice($postData);

            // 如果创建成功，更新发票记录，添加带有发票编号的回调URL
            if ($result['status'] && !empty($result['data']['id']) && !empty($result['data']['no'])) {
                $invoiceId = $result['data']['id'];
                $invoiceNo = $result['data']['no'];

                // 生成带有发票编号的回调URL
                $callbackUrl = $this->getCallbackUrl($invoiceNo);

                // 更新发票记录
                $invoiceModel = D('OaInvoice', 'OA');
                $updateResult = $invoiceModel->where(['id' => $invoiceId])->save([
                    'call_back_url' => $callbackUrl,
                    'update_time' => time()
                ]);

                // 记录更新结果
                if ($updateResult) {
                    \Think\Log::write('更新发票回调URL成功: ' . $callbackUrl, 'INFO');
                    // 更新返回数据中的回调URL
                    $result['data']['call_back_url'] = $callbackUrl;
                } else {
                    \Think\Log::write('更新发票回调URL失败: ' . $invoiceModel->getError(), 'WARN');
                }
            }

            // 返回结果
            $this->ajaxReturn($result);
        } catch (\Exception $e) {
            // 记录异常日志
            \Think\Log::write('创建发票异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'ERROR');

            // 返回错误信息
            $this->ajaxReturn([
                'status' => false,
                'message' => '创建发票失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取发票详情
     * 用于编辑发票时获取发票信息
     */
    public function getInvoiceDetail()
    {
        try {
            // 获取发票ID
            $invoiceId = I('id', 0, 'intval');

            // 参数校验
            if (empty($invoiceId)) {
                throw new \Exception('参数错误：发票ID不能为空');
            }

            // 检查用户是否有权限访问该会议
            $invoiceModel = D('OaInvoice', 'OA');
            $invoice = $invoiceModel->where(['id' => $invoiceId])->find();

            if (!empty($invoice) && !empty($invoice['cid']) && !$this->canAccessConference($invoice['cid'])) {
                throw new \Exception('您没有权限访问该发票信息');
            }

            // 使用发票服务获取发票详情
            $result = $this->invoiceService->getInvoiceDetail($invoiceId);

            // 返回结果，保持与原有格式一致
            if ($result['status']) {
                $this->ajaxReturn([
                    'status' => 1,
                    'info' => $result['message'],
                    'data' => $result['data']
                ]);
            } else {
                $this->ajaxReturn([
                    'status' => 0,
                    'info' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            // 返回错误信息
            $this->ajaxReturn([
                'status' => 0,
                'info' => $e->getMessage()
            ]);
        }
    }

    /**
     * 编辑发票
     * 用于更新发票信息
     */
    public function editInvoice()
    {
        try {
            // 获取POST数据
            $postData = I('post.');

            // 特别处理金额字段，确保它是标准格式
            if (isset($postData['amount'])) {
                $postData['amount'] = number_format((float)$postData['amount'], 2, '.', '');
            }

            // 参数校验
            if (empty($postData['invoice_id'])) {
                throw new \Exception('参数错误：发票ID不能为空');
            }

            // 检查用户是否有权限访问该会议
            $invoiceModel = D('OaInvoice', 'OA');
            $invoice = $invoiceModel->where(['id' => $postData['invoice_id']])->find();

            if (!empty($invoice) && !empty($invoice['cid']) && !$this->canAccessConference($invoice['cid'])) {
                throw new \Exception('您没有权限编辑该发票信息');
            }

            // 使用发票服务更新发票
            $result = $this->invoiceService->updateInvoice($postData);

            // 返回结果，保持与原有格式一致
            if ($result['status']) {
                $this->ajaxReturn([
                    'status' => 1,
                    'info' => $result['message'],
                    'data' => isset($result['data']) ? $result['data'] : null
                ]);
            } else {
                $this->ajaxReturn([
                    'status' => 0,
                    'info' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            // 记录异常日志
            \Think\Log::write('编辑发票异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'ERROR');

            // 返回错误信息
            $this->ajaxReturn([
                'status' => 0,
                'info' => '编辑发票失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除发票
     * 用于删除未提交的发票
     */
    public function deleteInvoice()
    {
        try {
            // 获取发票ID
            $invoiceId = I('id', 0, 'intval');

            // 参数校验
            if (empty($invoiceId)) {
                throw new \Exception('参数错误：发票ID不能为空');
            }

            // 检查用户是否有权限访问该会议
            $invoiceModel = D('OaInvoice', 'OA');
            $invoice = $invoiceModel->where(['id' => $invoiceId])->find();

            if (!empty($invoice) && !empty($invoice['cid']) && !$this->canAccessConference($invoice['cid'])) {
                throw new \Exception('您没有权限删除该发票信息');
            }

            // 使用发票服务删除发票
            $result = $this->invoiceService->deleteInvoice($invoiceId);

            // 返回结果，保持与原有格式一致
            if ($result['status']) {
                $this->ajaxReturn([
                    'status' => 1,
                    'info' => $result['message']
                ]);
            } else {
                $this->ajaxReturn([
                    'status' => 0,
                    'info' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            // 返回错误信息
            $this->ajaxReturn([
                'status' => 0,
                'info' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查用户是否有权限访问指定会议
     * @param int $conferenceId 会议ID
     * @return bool 是否有权限
     */
    protected function canAccessConference($conferenceId)
    {
        return $this->permissionService->canAccessConference($conferenceId);
    }

    /**
     * 获取回调URL
     * 从配置文件中获取当前环境的回调URL，并可选添加发票编号参数
     *
     * @param string $invoiceNo 发票编号（可选）
     * @return string 回调URL
     */
    protected function getCallbackUrl($invoiceNo = '')
    {
        // 从配置文件中获取当前环境
        $environment = C('OA_ENVIRONMENT');

        // 从配置文件中获取回调URL
        $baseUrl = C('OA_CALLBACK_URLS.' . $environment);

        // 如果未配置回调URL，记录警告并返回空字符串
        if (empty($baseUrl)) {
            \Think\Log::write('未配置回调URL，当前环境：' . $environment, 'WARN');
            return '';
        }

        // 如果提供了发票编号，添加到URL中
        if (!empty($invoiceNo)) {
            // 判断URL是否已经包含参数
            $separator = (strpos($baseUrl, '?') !== false) ? '&' : '?';
            // 添加发票编号参数
            $baseUrl .= $separator . 'no=' . urlencode($invoiceNo);
        }

        return $baseUrl;
    }
}
