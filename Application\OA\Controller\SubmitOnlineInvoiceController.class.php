<?php

namespace OA\Controller;

use Think\Controller;

use OA\Common\ResponseHelper;

class SubmitOnlineInvoiceController extends BaseController
{

    /**
     * 构造函数
     */
    public function _initialize()
    {
        parent::_initialize();
        // 构造硬编码测试数据
        $this->testData =  array(
            "no" => null,
            "userId" => 339,
            "deptId" => 101,
            "submitName" => "李雨婷",
            "submitDept" => "管理部门",
            "orderNum" => null,
            "confName" => "ICTEST2025",
            "paperCode" => "PAPER-001",
            "creditSerial" => "********-*********",
            "remitterInfo" => "王五 - 北京大学 - ***********",
            "amount" => 1000,
            "unit" => "CNY",
            "remitTime" => "2025-03-27",
            "account" => "cs",
            "remark" => "会议注册费",
            "annex" => null,
            "callbackUrl" => "http://www.test",
            "receipt" => array()
        );
        // 构造硬编码测试数据
        $this->test2Data =  array(
            "userId" => 339,
            "deptId" => 101,
            "submitName" => "李雨婷",
            "submitDept" => "管理部门",
            "no" => null,
            "orderNum" => null,
            "invoiceTitle" => "重庆大学",
            "buyerTaxNum" => "**********",
            "amount" => null,
            "buyerPhone" => "***********",
            "buyerEmail" => "<EMAIL>",
            "buyerAddress" => null,
            "buyerAccount" => null,
            "buyerAccountName" => null,
            "salerCompany" => "cs",
            "goodsInfo" => "hyzcf",
            "invoiceType" => "pc",
            "submitReturnApi" => "",
            "remark" => ""
        );

        $this->testallData = array(
            "no" => null,
            "userId" => 339,
            "deptId" => 101,
            "submitName" => "李雨婷",
            "submitDept" => "管理部门",
            "orderNum" => '021907322871164833792',
            "confName" => "ICTEST2025",
            "paperCode" => "PAPER-001",
            "creditSerial" => "********-*********",
            "remitterInfo" => "北京大学 - ***********",
            "amount" => 1000,
            "unit" => "CNY",
            "remitTime" => "2025-03-27",
            "account" => "cs",
            "remark" => "会议注册费",
            "annex" => null,
            "callbackUrl" => "http://www.test",
            "receipt" => array(
                array(
                    "userId" => 339,
                    "deptId" => 101,
                    "submitName" => "李雨婷",
                    "submitDept" => "管理部门",
                    "no" => null,
                    "orderNum" => '021907322871164833792',
                    "invoiceTitle" => "重庆大学",
                    "buyerTaxNum" => "**********",
                    "amount" => 500,
                    "buyerPhone" => "***********",
                    "buyerEmail" => "<EMAIL>",
                    "buyerAddress" => null,
                    "buyerAccount" => null,
                    "buyerAccountName" => null,
                    "salerCompany" => null,
                    "goodsInfo" => "hyzcf",
                    "invoiceType" => "pc",
                    "submitReturnApi" => "",
                    "remark" => "",
                    "invoiceRemark" => null,
                    "buyerTel" => null
                ),
                array(
                    "userId" => 339,
                    "deptId" => 101,
                    "submitName" => "李雨婷",
                    "submitDept" => "管理部门",
                    "no" => null,
                    "orderNum" => '021907322871164833792',
                    "invoiceTitle" => "重庆大学",
                    "buyerTaxNum" => "**********",
                    "amount" => 500,
                    "buyerPhone" => "***********",
                    "buyerEmail" => "<EMAIL>",
                    "buyerAddress" => null,
                    "buyerAccount" => null,
                    "buyerAccountName" => null,
                    "salerCompany" => null,
                    "goodsInfo" => "hyzcf",
                    "invoiceType" => "pc",
                    "submitReturnApi" => "",
                    "remark" => "",
                    "invoiceRemark" => null,
                    "buyerTel" => null
                )
            )
        );
    }

    //测试在线付款查账并提交发票 getOrderNum(0)
    public function index()
    {

        try {
            $orderNum = $this->OrderNumservice->getOrderNum(0);
            $postData = $this->testData;
            $postData['orderNum'] = $orderNum;
            //先提交汇款数据
            $result = $this->payDataService->post($postData);
            //支付的总额为  $postData['amount'];
            //再提交对应的发票数据，发票数据可以多次提交，只要每个发票的开票金额之和相加与汇款金额一致即可
            $post2Data = $this->test2Data;
            $post2Data['orderNum'] = $orderNum;
            $post2Data['amount'] = $postData['amount'];
            $result = $this->invoiceService->post($post2Data);
            dump($result);
        } catch (\Common\Exception\RemoteApiException $e) {
            $this->ajaxReturn(['status' => false, 'message' => 'OA处理失败：' . $e->getMessage(), 'error_source' => 'remote']);
        } catch (\Common\Exception\LocalOperationException $e) {
            $this->ajaxReturn(['status' => false, 'message' => '本地操作失败：' . $e->getMessage(), 'error_source' => 'local']);
        }
    }
    // 测试银行转账查账、并提交发票 getOrderNum(0)
    public function index2()
    {
        try {
            $orderNum = $this->OrderNumservice->getOrderNum(1);

            $postData = $this->testData;
            $postData['orderNum'] = $orderNum;
            $postallData = $this->testallData;
            $postallData['orderNum'] = $orderNum;
            $postallData['receipt']['orderNum'] = $orderNum;
            $postallData['receipt']['amount'] =  $postallData['amount'];
            $result = $this->transferDataService->post($postData);
            $post2Data = $this->test2Data;
            $post2Data['orderNum'] = $orderNum;
            $post2Data['amount'] = $postData['amount'];
            $result = $this->invoiceService->post($post2Data);
            dump($result);
        } catch (\Common\Exception\RemoteApiException $e) {
            $this->ajaxReturn(['status' => false, 'message' => 'OA处理失败：' . $e->getMessage(), 'error_source' => 'remote']);
        } catch (\Common\Exception\LocalOperationException $e) {
            $this->ajaxReturn(['status' => false, 'message' => '本地操作失败：' . $e->getMessage(), 'error_source' => 'local']);
        }
    }
    //测试线下汇款数据和发票数据一起提交
    public function index3()
    {
        try {
            $orderNum = $this->OrderNumservice->getOrderNum(1);
            $postallData = $this->testallData;
            // $postallData['orderNum'] = $orderNum;
            // $postallData['receipt']['orderNum'] = $orderNum;
            // $postallData['receipt']['amount'] =  $postallData['amount'];
            //return $this->ajaxReturn($postallData);
            $result = $this->transferDataService->post($postallData);
            dump($result);
        } catch (\Common\Exception\RemoteApiException $e) {
            $this->ajaxReturn(['status' => false, 'message' => 'OA处理失败：' . $e->getMessage(), 'error_source' => 'remote']);
        } catch (\Common\Exception\LocalOperationException $e) {
            $this->ajaxReturn(['status' => false, 'message' => '本地操作失败：' . $e->getMessage(), 'error_source' => 'local']);
        }
    }
    //测试游客付款和发票数据一起提交
    public function index4()
    {
        try {
            $orderNum = $this->OrderNumservice->getOrderNum(2);
            $postallData = $this->testallData;
            // $postallData['orderNum'] = $orderNum;
            // $postallData['receipt']['orderNum'] = $orderNum;
            // $postallData['receipt']['amount'] =  $postallData['amount'];
            //return $this->ajaxReturn($postallData);
            $result = $this->transferDataService->post($postallData);
            dump($result);
        } catch (\Common\Exception\RemoteApiException $e) {
            $this->ajaxReturn(['status' => false, 'message' => 'OA处理失败：' . $e->getMessage(), 'error_source' => 'remote']);
        } catch (\Common\Exception\LocalOperationException $e) {
            $this->ajaxReturn(['status' => false, 'message' => '本地操作失败：' . $e->getMessage(), 'error_source' => 'local']);
        }
    }
    public function user()
    {
        try {
            $data['workId'] = '0023';
            $data['name'] = '李雨亭';
            $result = $this->userbindingService->find($data);
            dump($result);
        } catch (\Common\Exception\RemoteApiException $e) {
            $this->ajaxReturn(['status' => false, 'message' => 'OA处理失败：' . $e->getMessage(), 'error_source' => 'remote']);
        } catch (\Common\Exception\LocalOperationException $e) {
            $this->ajaxReturn(['status' => false, 'message' => '本地操作失败：' . $e->getMessage(), 'error_source' => 'local']);
        }
    }
}
