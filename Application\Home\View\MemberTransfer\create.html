<extend name="public:member_base" />
<block name="main">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="page-title-box d-flex align-items-center justify-content-between">
          <h4 class="mb-0 font-size-18">Submit Transfer</h4>
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="javascript: void(0);">Home</a></li>
              <li class="breadcrumb-item"><a href="{:U('index')}">Transfer Records</a></li>
              <li class="breadcrumb-item active">Submit Transfer</li>
            </ol>
          </div>
        </div>
      </div>
    </div>

    <!-- 主体内容区域 - 左右栅格布局 -->
    <div class="row">
      <!-- 左侧 - 币种选择区域 (3列) -->
      <div class="col-md-3">
        <div class="card mb-4 border-0 shadow-sm">
          <div class="card-header bg-light text-white">
            <h5 class="mb-0"><i class="fas fa-credit-card mr-2"></i>Payment Options</h5>
          </div>
          <div class="card-body p-0">
            <div class="list-group list-group-flush payment-options">
              <!-- CNY 选项 -->
              <a href="javascript:void(0)"
                class="list-group-item list-group-item-action py-3 px-4 currency-option active" id="cny-payment-option"
                data-currency="cny" <?php echo ($register_fee['cny']=='0.00' ? 'disabled' : '' ); ?>>
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <div class="d-flex align-items-center">
                      <div class="currency-icon mr-3">
                        <i class="fas fa-yen-sign"></i>
                      </div>
                      <div>
                        <div class="currency-title">CNY Payment</div>
                        <small class="currency-subtitle">Domestic Account</small>
                      </div>
                    </div>
                  </div>
                  <div class="text-right">
                    <if condition="$register_fee['cny'] != '0.00'">
                      <div class="currency-amount">￥{$register_fee.cny}</div>
                      <else />
                      <span class="badge badge-light">Not Available</span>
                    </if>
                  </div>
                </div>
              </a>

              <!-- USD 选项 -->
              <a href="javascript:void(0)" class="list-group-item list-group-item-action py-3 px-4 currency-option"
                id="usd-payment-option" data-currency="usd" <?php echo ($register_fee['usd']=='0.00' ? 'disabled' : ''
                ); ?>>
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <div class="d-flex align-items-center">
                      <div class="currency-icon mr-3">
                        <i class="fas fa-dollar-sign"></i>
                      </div>
                      <div>
                        <div class="currency-title">USD Payment</div>
                        <small class="currency-subtitle">International Account</small>
                      </div>
                    </div>
                  </div>
                  <div class="text-right">
                    <if condition="$register_fee['usd'] != '0.00'">
                      <div class="currency-amount">${$register_fee.usd}</div>
                      <else />
                      <span class="badge badge-light">Not Available</span>
                    </if>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>

        <!-- 注册信息摘要 -->
        <div class="card border-0 shadow-sm mb-4">
          <div class="card-header bg-light text-white">
            <h5 class="mb-0"><i class="fas fa-clipboard-list mr-2"></i>Registration Summary</h5>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <small class="text-muted d-block">Conference</small>
              <div class="font-weight-medium">{$conference.title}</div>
            </div>
            <div class="mb-3">
              <small class="text-muted d-block">Registration Type</small>
              <div class="font-weight-medium">{$register.regtype}</div>
            </div>
            <div>
              <small class="text-muted d-block">Email</small>
              <div class="font-weight-medium">{$register.email}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧 - 付款信息和表单 (9列) -->
      <div class="col-md-9">
        <!-- 付款账户信息 -->
        <div class="card mb-4 border-0 shadow-sm">
          <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-money-check-alt mr-2"></i>Payment Details</h5>
            <div>
              <span class="badge badge-pill badge-light payment-badge" id="usd-badge" style="display: none;">USD</span>
              <span class="badge badge-pill badge-light payment-badge" id="cny-badge">CNY</span>
            </div>
          </div>
          <div class="card-body">
            <!-- CNY 账户信息 -->
            <div id="cny-payment-details">
              <if condition="$register_fee['cny'] != '0.00'">
                <div class="row mb-4">
                  <div class="col-md-6">
                    <div class="border-bottom pb-3 mb-3">
                      <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Amount</span>
                        <span class="font-weight-bold">￥{$register_fee.cny}</span>
                      </div>
                    </div>
                    <div class="alert alert-light border-left border-success mb-0">
                      <i class="fas fa-info-circle text-success mr-2"></i>
                      <small>Please transfer the exact amount shown above.</small>
                    </div>
                  </div>
                  <div class="col-md-6 border-left">
                    <h6 class="text-muted mb-3">Bank Information</h6>
                    <div class="bank-info">
                      <?php echo nl2br($bank_cn['bank_info']); ?>
                    </div>
                  </div>
                </div>
                <else />
                <div class="text-center py-4">
                  <i class="fas fa-ban fa-2x mb-3 text-muted"></i>
                  <p class="text-muted">CNY payment is not available for this registration.</p>
                </div>
              </if>
            </div>

            <!-- USD 账户信息 -->
            <div id="usd-payment-details" style="display: none;">
              <if condition="$register_fee['usd'] != '0.00'">
                <div class="row mb-4">
                  <div class="col-md-6">
                    <div class="border-bottom pb-3 mb-3">
                      <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Original Amount</span>
                        <span class="font-weight-bold">${$register_fee.original_usd}</span>
                      </div>
                      <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Service Charge</span>
                        <span class="font-weight-bold">$30.00</span>
                      </div>
                      <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">Total Amount</span>
                        <span class="font-weight-bold">${$register_fee.usd}</span>
                      </div>
                    </div>
                    <div class="alert alert-light border-left border-primary mb-0">
                      <i class="fas fa-info-circle text-primary mr-2"></i>
                      <small>30 USD service charge will be charged by the bank, please include this in your
                        transfer.</small>
                    </div>
                  </div>
                  <div class="col-md-6 border-left">
                    <h6 class="text-muted mb-3">Bank Information</h6>
                    <div class="bank-info">
                      <?php echo nl2br($bank_en['bank_info']); ?>
                    </div>
                  </div>
                </div>
                <else />
                <div class="text-center py-4">
                  <i class="fas fa-ban fa-2x mb-3 text-muted"></i>
                  <p class="text-muted">USD payment is not available for this registration.</p>
                </div>
              </if>
            </div>
          </div>
        </div>

        <!-- 转账信息表单 -->
        <div class="card border-0 shadow-sm">
          <div class="card-header bg-info text-light">
            <h5 class="mb-0"><i class="fas fa-file-invoice mr-2"></i>Payment Information Submission</h5>
          </div>
          <div class="card-body">
            <form id="transferForm" action="{:U('submit')}" method="post" class="form-horizontal">
              <input type="hidden" name="cid" value="{$cid}">
              <input type="hidden" name="reg_id" value="{$reg_id}">
              <input type="hidden" name="transfer_pic" id="transfer_pic" value="">

              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="transfer_name">Payer Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="transfer_name" name="transfer_name" required>
                    <small class="form-text text-muted">Full name of the person or organization making the payment</small>

                  </div>

                  <div class="form-group">
                    <label for="transfer_time">Payment Date <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="transfer_time" name="transfer_time" required>

                    <small class="form-text text-muted">Date when the payment was initiated or completed</small>
                  </div>

                  <div class="form-group">
                    <label for="transfer_account">Payer Account <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="transfer_account" name="transfer_account" required>
                    <small class="form-text text-muted">Bank account number, card number, or digital wallet ID used for the payment</small>

                  </div>

                  <div class="form-group">
                    <label for="total">Payment Amount <span class="text-danger">*</span></label>
                    <div class="amount-currency-container">
                      <div class="amount-wrapper">
                        <input type="number" step="0.01" class="form-control" id="total" name="total" required>
                      </div>
                      <div class="currency-wrapper">
                        <div class="btn-group btn-group-toggle" data-toggle="buttons">
                          <label class="btn btn-outline-primary active" for="currency-cny">
                            <input type="radio" name="currency" id="currency-cny" value="0" checked> CNY
                          </label>
                          <label class="btn btn-outline-info" for="currency-usd">
                            <input type="radio" name="currency" id="currency-usd" value="1"> USD
                          </label>
                        </div>
                      </div>
                    </div>
                    <div id="amount-mismatch-alert" class="alert alert-warning mt-2" style="display: none;">
                      <i class="fas fa-exclamation-triangle mr-2"></i>
                      <span>The amount you entered does not match the system calculated fee. If this is intentional or you have special circumstances, please explain in the Note field to help us process your payment faster.</span>
                    </div>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="form-group">
                    <label>Payment Proof <span class="text-danger">*</span></label>
                    <div id="drop-zone" class="upload-area custom-file-upload">
                      <input type="file" id="file" class="file-input-hidden" name="file" accept="image/*"
                        style="position: absolute; width: 0; height: 0; padding: 0; margin: 0; opacity: 0; pointer-events: none;">
                      <!-- 上传状态指示器 -->
                      <div id="upload-status" class="upload-status" style="display: none;">
                        <div class="upload-status-indicator">
                          <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Uploading...</span>
                          </div>
                          <p class="mt-2 mb-0">Uploading your payment proof...</p>
                          <p class="text-muted small">Please wait, do not close this page</p>
                        </div>
                      </div>
                      <div id="upload-prompt">
                        <div class="text-center">
                          <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-primary"></i>
                          <p>Drag and drop your payment proof here or click to upload</p>
                          <p class="text-muted small">Upload a screenshot or photo of your bank transfer, payment confirmation, or transaction receipt</p>
                          <p class="text-muted small">Supported formats: JPG, PNG, PDF (Max size: 5MB)</p>
                        </div>
                        <div class="mt-3 text-center">
                          <button type="button" class="btn btn-sm btn-outline-primary browse-btn">
                            <i class="fas fa-folder-open mr-1"></i>Browse Files
                          </button>
                        </div>
                      </div>
                      <div id="preview-container" class="mt-3" style="display: none;"
                        onclick="event.stopPropagation();">
                        <div class="preview-wrapper">
                          <img id="preview-img" class="preview-img mb-3" src="" alt="Preview"
                            style="width: 100%; height: auto;">
                        </div>
                        <p id="file-name" class="mb-2 small font-weight-medium"></p>
                        <button type="button" id="remove-file" class="btn btn-sm btn-outline-danger">
                          <i class="fas fa-times mr-1"></i>Remove
                        </button>
                      </div>
                    </div>

                  </div>

                  <div class="form-group">
                    <label for="remark">Note (Optional)</label>
                    <textarea class="form-control" id="remark" name="remark" rows="3" placeholder="Any additional information about your payment"></textarea>
                    <small class="form-text text-muted">Include transaction reference numbers, special circumstances, or any other relevant details</small>
                  </div>
                </div>
              </div>

              <div class="form-group mb-0 mt-3 text-center">
                <div id="upload-required-alert" class="alert alert-warning mb-3" style="display: none;">
                  <i class="fas fa-exclamation-triangle mr-2"></i>
                  <span>Please upload your payment proof before submitting the form</span>
                </div>
                <button type="submit" class="btn btn-primary px-4 py-2" id="submitBtn" disabled>
                  <i class="fas fa-paper-plane mr-1"></i>Submit Payment Information
                </button>
              </div>

              <!-- 添加提示信息 -->
              <div class="mt-4">
                <div class="alert alert-light border-left border-warning">
                  <i class="fas fa-exclamation-triangle text-warning mr-2"></i>
                  <small>After submission, the system will automatically process your transfer information. Please
                    ensure that the information you provide is accurate.</small>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</block>

<block name="style">
  <style>
    /* 上传状态指示器样式 */
    .upload-status {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.9);
      z-index: 10;
    }
    
    .upload-status-indicator {
      text-align: center;
      padding: 20px;
    }
    
    .upload-success-icon {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #28a745;
      color: white;
      border-radius: 50%;
      font-size: 16px;
    }
    
    /* 上传区域状态样式 */
    #drop-zone.uploading {
      background-color: rgba(0, 123, 255, 0.05);
    }
    
    #drop-zone.border-success {
      background-color: rgba(40, 167, 69, 0.05);
    }
    
    #drop-zone.border-warning {
      animation: pulse-warning 1s infinite;
    }
    
    @keyframes pulse-warning {
      0% {
        border-color: #ffc107;
        background-color: rgba(255, 193, 7, 0);
      }
      50% {
        border-color: #ffc107;
        background-color: rgba(255, 193, 7, 0.2);
      }
      100% {
        border-color: #ffc107;
        background-color: rgba(255, 193, 7, 0);
      }
    }
    
    /* 金额和货币容器样式 */
    .amount-currency-container {
      display: flex;
      align-items: stretch;
      background-color: #f8f9fa;
      border-radius: 8px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      margin-bottom: 0.5rem;
      border: 1px solid #e9ecef;
    }
    
    .amount-wrapper {
      flex: 1;
      padding: 0.25rem;
    }
    
    .amount-wrapper input {
      height: 50px;
      font-size: 1.25rem;
      font-weight: 500;
      text-align: right;
      border: none;
      background-color: white;
      border-radius: 6px;
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    }
    
    .amount-wrapper input:focus {
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05), 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
    }
    
    /* 货币选择按钮样式 */
    .currency-wrapper {
      display: flex;
      align-items: center;
      padding: 0.25rem 0.5rem;
      background-color: #f8f9fa;
      border-left: 1px solid #e9ecef;
    }
    
    .currency-wrapper .btn-group {
      width: 120px;
    }
    
    .currency-wrapper .btn {
      width: 50%;
      font-weight: 500;
      padding: 0.5rem 0;
      transition: all 0.2s ease;
    }
    
    .currency-wrapper .btn.active {
      font-weight: 600;
    }
    
    /* 金额不匹配提示样式 */
    #amount-mismatch-alert {
      font-size: 0.9rem;
      border-left: 4px solid #ffc107;
      background-color: rgba(255, 193, 7, 0.1);
      padding: 0.75rem;
      transition: all 0.3s ease;
    }
    
    /* 金额输入框样式 */
    #total {
      font-size: 1.25rem;
      font-weight: 500;
      text-align: right;
      padding-right: 1rem;
      height: 48px;
    }
    
    #total:focus {
      box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
      border-color: #4299e1;
    }
    /* Variable definitions */
    :root {
      --primary: #4e73df;
      --success: #28a745;
      --info: #17a2b8;
      --warning: #ffc107;
      --danger: #dc3545;
      --light: #f8f9fa;
      --gray: #6c757d;
      --border-color: #dee2e6;
      --transition: all 0.25s ease;
      --orange: #ff8f00;
      --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
      --radius: 0.25rem;
    }

    /* 基础卡片样式 */
    .card {
      border-radius: var(--radius);
      border: none;
      transition: var(--transition);
      box-shadow: var(--shadow);
    }

    .card-header {
      padding: 1rem 1.5rem;
    }

    /* Payment Options 样式 */
    .payment-options .list-group-item {
      border: 0;
      padding: 1.25rem;
      position: relative;
      transition: var(--transition);
      margin-right: 6px;
    }

    .payment-options .list-group-item:hover:not([disabled]) {
      background-color: rgba(255, 143, 0, 0.05);
    }

    /* 选中状态样式 */
    .payment-options .list-group-item.active {
      background-color: var(--orange) !important;
      color: white !important;
      border: none;
    }

    /* 修改指示器样式 */
    .payment-options .list-group-item.active::after {
      content: '';
      position: absolute;
      right: -15px;
      top: 50%;
      transform: translateY(-50%);
      border: 8px solid transparent;
      border-left: 8px solid var(--orange);
      filter: drop-shadow(1px 0 1px rgba(0, 0, 0, 0.05));
    }

    /* 选中状态下的样式 */
    .payment-options .list-group-item.active .currency-title,
    .payment-options .list-group-item.active .currency-subtitle,
    .payment-options .list-group-item.active .currency-amount,
    .payment-options .list-group-item.active .currency-icon i {
      color: white !important;
    }

    .payment-options .list-group-item.active .currency-icon {
      background-color: rgba(255, 255, 255, 0.2);
    }

    /* 处理卡片溢出 */
    .payment-options,
    .card-body.p-0 {
      position: relative;
      overflow: visible;
    }

    /* 货币图标样式 */
    .currency-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      transition: var(--transition);
    }

    /* CNY 样式 */
    #cny-payment-option .currency-icon {
      background-color: rgba(40, 167, 69, 0.1);
      color: var(--success);
    }

    #cny-payment-option.active .currency-icon {
      background-color: var(--success);
      color: white;
    }

    /* USD 样式 */
    #usd-payment-option .currency-icon {
      background-color: rgba(0, 123, 255, 0.1);
      color: var(--primary);
    }

    #usd-payment-option.active .currency-icon {
      background-color: var(--primary);
      color: white;
    }

    /* 金额显示样式 */
    .currency-amount {
      font-weight: 700;
      font-size: 1.25rem;
      line-height: 1;
    }

    .currency-amount,
    .currency-title {
      color: var(--gray);
    }

    #cny-payment-option .currency-amount {
      color: var(--success);
    }

    #usd-payment-option .currency-amount {
      color: var(--primary);
    }

    .list-group-item.active .currency-amount {
      color: inherit;
    }

    /* Payment Details 样式 */
    .payment-details {
      padding: 2rem;
    }

    .amount-display {
      font-size: 2.5rem;
      font-weight: 700;
      line-height: 1;
      color: #2c3e50;
      margin-bottom: 0.5rem;
    }

    .amount-display .currency-symbol {
      font-size: 1.5rem;
      vertical-align: super;
      margin-right: 0.25rem;
    }

    /* Bank Information 样式 */
    .bank-info {
      background-color: var(--light);
      border-radius: 0.5rem;
      padding: 1.5rem;
      margin-top: 1rem;
    }

    .bank-info .table td {
      padding: 0.5rem 0;
      border: none;
    }

    .bank-info .table td:first-child {
      color: var(--gray);
      font-weight: 500;
      width: 40%;
    }

    /* 上传区域样式 */
    .upload-area {
      border: 2px dashed var(--border-color);
      border-radius: 0.25rem;
      padding: 2rem 1rem;
      text-align: center;
      cursor: pointer;
      transition: var(--transition);
      background-color: var(--light);
      min-height: 180px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .upload-area:hover,
    .upload-area.border-primary {
      border-color: var(--primary);
      background-color: rgba(78, 115, 223, 0.05);
    }

    .upload-area.has-file {
      border-color: var(--success);
      background-color: rgba(40, 167, 69, 0.05);
    }

    /* 文件预览样式 */
    .preview-img {
      max-width: 100%;
      max-height: 300px;
      border-radius: 0.25rem;
      border: 1px solid var(--border-color);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      object-fit: contain;
    }

    /* 通用工具类 */
    .badge-pill {
      padding: 0.5em 1em;
    }

    .alert-info {
      background-color: rgba(23, 162, 184, 0.1);
      border-color: rgba(23, 162, 184, 0.2);
      color: var(--info);
    }

    .alert-light {
      background-color: var(--light);
      border-left: 4px solid;
    }

    .border-left {
      border-left-width: 4px !important;
    }

    /* 银行信息样式 */
    .bank-info {
      font-size: 0.9rem;
      line-height: 1.6;
    }

    .bank-info br {
      display: block;
      margin-bottom: 0.5rem;
      content: "";
    }

    /* 隐藏文件输入框 */
    .file-input-hidden,
    .custom-file-upload input[type="file"] {
      position: absolute;
      width: 0;
      height: 0;
      padding: 0;
      margin: 0;
      opacity: 0;
      overflow: hidden;
      z-index: -1;
      pointer-events: none;
    }

    /* Just-Validate 样式 */
    .just-validate-error-label {
      color: var(--danger);
      font-size: 0.85rem;
      margin-top: 0.25rem;
    }

    .is-invalid {
      border-color: var(--danger);
    }

    .is-valid {
      border-color: var(--success);
    }
  </style>
</block>

<block name="script">
  <script>
    $(document).ready(function () {
      // Form validation using Just-Validate
      const validator = new JustValidate('#transferForm', {
        errorFieldCssClass: 'is-invalid',
        successFieldCssClass: 'is-valid',
        focusInvalidField: true,
        lockForm: false
      });

      // Add validation rules
      validator
        .addField('#transfer_name', [
          {
            rule: 'required',
            errorMessage: 'Please enter sender name'
          }
        ])
        .addField('#transfer_time', [
          {
            rule: 'required',
            errorMessage: 'Please select transfer date and time'
          }
        ])
        .addField('#transfer_account', [
          {
            rule: 'required',
            errorMessage: 'Please enter transfer account'
          }
        ])
        .addField('#total', [
          {
            rule: 'required',
            errorMessage: 'Please enter transfer amount'
          },
          {
            rule: 'number',
            errorMessage: 'Please enter a valid number'
          },
          {
            rule: 'minNumber',
            value: 0.01,
            errorMessage: 'Amount must be greater than 0'
          }
        ])
        .addField('input[name="currency"]', [
          {
            rule: 'required',
            errorMessage: 'Please select a currency'
          }
        ]);

      // 监听提交按钮点击事件
      $('#submitBtn').on('click', function(e) {
        e.preventDefault();

        // 验证表单
        validator.validate().then(function(isValid) {
          // 检查转账凭证
          if (isValid && $('#transfer_pic').val() === '') {
            Swal.fire({
              icon: 'error',
              title: 'Upload Required',
              text: 'Please upload a transfer receipt',
              confirmButtonText: 'OK'
            });
            return;
          }

          // 如果验证通过，提交表单
          if (isValid && $('#transfer_pic').val() !== '') {
            $('#transferForm')[0].submit();
          }
        });
      });

      // 存储系统默认金额
      const defaultAmounts = {
        cny: parseFloat('{$register_fee.cny}'),
        usd: parseFloat('{$register_fee.usd}')
      };
      
      // 检查金额是否匹配
      function checkAmountMatch() {
        const currentAmount = parseFloat($('#total').val());
        const currentCurrency = $('input[name="currency"]:checked').val() === '1' ? 'usd' : 'cny';
        const expectedAmount = defaultAmounts[currentCurrency];
        
        // 检查金额是否匹配（允许0.01的误差）
        const isMatch = Math.abs(currentAmount - expectedAmount) < 0.01;
        
        // 显示或隐藏提示
        if (!isMatch && !isNaN(currentAmount) && currentAmount > 0) {
          $('#amount-mismatch-alert').slideDown();
        } else {
          $('#amount-mismatch-alert').slideUp();
        }
      }
      
      // Update payment details display and amount
      function updatePaymentDetailsDisplay(currency, syncMode = 'auto') {
        const isUsd = (currency === 'usd' || currency === 1);
        
        // Update selection
        $('.currency-option').removeClass('active');
        $(`#${isUsd ? 'usd' : 'cny'}-payment-option`).addClass('active');

        // Update display elements - merchant info changes
        $('#cny-payment-details, #usd-payment-details').hide();
        $(`#${isUsd ? 'usd' : 'cny'}-payment-details`).show();

        $('#cny-badge, #usd-badge').hide();
        $(`#${isUsd ? 'usd' : 'cny'}-badge`).show();
        
        // 更新Transfer Amount文本框的默认值
        $('#total').val(isUsd ? '{$register_fee.usd}' : '{$register_fee.cny}');
        
        // 同步模式：auto - 自动同步货币单选按钮，manual - 保持用户手动选择
        if (syncMode === 'auto') {
          // 同时更新对应的货币单选按钮
          if(isUsd) {
            $('#currency-usd').prop('checked', true);
          } else {
            $('#currency-cny').prop('checked', true);
          }
        }
        
        // 隐藏金额不匹配提示（因为刚刚更新为默认值）
        $('#amount-mismatch-alert').hide();
      }

      // Currency selection event - updates payment details and currency selection
      $('.currency-option').click(function() {
        if ($(this).attr('disabled')) return;

        const currency = $(this).data('currency');
        // 自动模式：点击TAB时自动同步货币单选按钮
        updatePaymentDetailsDisplay(currency, 'auto');
      });
      
      // 货币单选按钮切换事件
      $('input[name="currency"]').change(function() {
        const currencyValue = $(this).val();
        const isUsd = currencyValue === '1';
        
        // 更新金额为对应货币的默认值
        $('#total').val(isUsd ? '{$register_fee.usd}' : '{$register_fee.cny}');
        
        // 同步更新Payment Options TAB的选择状态
        $('.currency-option').removeClass('active');
        $(`#${isUsd ? 'usd' : 'cny'}-payment-option`).addClass('active');
        
        // 更新显示元素 - 商户信息变化
        $('#cny-payment-details, #usd-payment-details').hide();
        $(`#${isUsd ? 'usd' : 'cny'}-payment-details`).show();
        
        // 更新货币标签
        $('#cny-badge, #usd-badge').hide();
        $(`#${isUsd ? 'usd' : 'cny'}-badge`).show();
        
        // 隐藏金额不匹配提示（因为刚刚更新为默认值）
        $('#amount-mismatch-alert').hide();
      });
      
      // 监听金额输入变化
      $('#total').on('input', function() {
        checkAmountMatch();
      });
      
      // File upload handling
      function initFileUpload() {
        // Get elements
        const elements = {
          dropZone: document.getElementById('drop-zone'),
          fileInput: document.getElementById('file'),
          uploadPrompt: document.getElementById('upload-prompt'),
          uploadStatus: document.getElementById('upload-status'),
          previewContainer: document.getElementById('preview-container'),
          previewImg: document.getElementById('preview-img'),
          fileName: document.getElementById('file-name'),
          removeFile: document.getElementById('remove-file'),
          transferPic: document.getElementById('transfer_pic'),
          submitBtn: document.getElementById('submitBtn'),
          uploadRequiredAlert: document.getElementById('upload-required-alert')
        };

        // Check if all elements exist
        for (const [key, element] of Object.entries(elements)) {
          if (!element) {
            alert(`File upload initialization failed: ${key} element not found`);
            return;
          }
        }

        const { dropZone, fileInput, uploadPrompt, uploadStatus, previewContainer, previewImg, fileName, removeFile, transferPic, submitBtn, uploadRequiredAlert } = elements;
        const browseBtn = dropZone.querySelector('.browse-btn');

        // Format file size helper
        function formatFileSize(bytes) {
          if (bytes < 1024) return bytes + ' bytes';
          if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
          return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }

        // Reset file upload
        function resetFileUpload() {
          fileInput.value = '';
          previewImg.src = '';
          fileName.textContent = '';
          uploadPrompt.style.display = 'block';
          uploadStatus.style.display = 'none';
          previewContainer.style.display = 'none';
          transferPic.value = '';
          dropZone.classList.remove('has-file', 'border-primary', 'border-success');
          dropZone.style.borderStyle = 'dashed';
          dropZone.style.borderWidth = '2px';
          dropZone.style.borderColor = '#dee2e6';
          
          // 禁用提交按钮
          submitBtn.disabled = true;
          uploadRequiredAlert.style.display = 'block';
        }

        // Upload image to server
        function uploadImageToServer(file, callback) {
          // 显示上传状态指示器
          uploadPrompt.style.display = 'none';
          uploadStatus.style.display = 'flex';
          previewContainer.style.display = 'none';
          
          // 设置上传区域样式
          dropZone.classList.add('uploading');
          dropZone.style.borderStyle = 'solid';
          dropZone.style.borderWidth = '2px';
          dropZone.style.borderColor = '#007bff';
          
          // 禁用提交按钮
          submitBtn.disabled = true;
          uploadRequiredAlert.style.display = 'block';
          
          const formData = new FormData();
          formData.append('file', file);
          // 添加场景和关联类型参数，用于记录日志
          formData.append('scene', 'register');
          formData.append('related_type', 'transfer_proof');
          // 添加注册ID作为关联业务ID
          const regId = document.querySelector('input[name="reg_id"]').value;
          formData.append('related_id', regId);

          const xhr = new XMLHttpRequest();

          xhr.addEventListener('load', function() {
            if (xhr.status === 200) {
              try {
                const response = JSON.parse(xhr.responseText);
                callback(response);
              } catch (e) {
                callback({ status: 0, message: 'Error parsing server response' });
              }
            } else {
              callback({ status: 0, message: 'Server error: ' + xhr.status });
            }
          });

          xhr.addEventListener('error', () => callback({ status: 0, message: 'Network error' }));
          xhr.addEventListener('abort', () => callback({ status: 0, message: 'Upload aborted' }));

          // 使用新的上传接口
          xhr.open('POST', '{:U("/uploadOSS/uploadTransferProof")}', true);
          xhr.send(formData);
        }

        // Handle files
        function handleFiles(files) {
          if (!files || files.length === 0) return;

          const file = files[0];

          // Validate file
          if (!file.type.match('image.*')) {
            Swal.fire({
              icon: 'error',
              title: 'Invalid File Type',
              text: 'Please upload an image file (JPG, PNG, etc.)',
              confirmButtonText: 'OK'
            });
            return;
          }

          if (file.size > 5 * 1024 * 1024) {
            Swal.fire({
              icon: 'error',
              title: 'File Too Large',
              text: 'File size exceeds 5MB limit',
              confirmButtonText: 'OK'
            });
            return;
          }

          // Read and preview file
          const reader = new FileReader();

          reader.onload = function(e) {
            // Set preview image
            previewImg.src = e.target.result;
            fileName.textContent = file.name + ' (' + formatFileSize(file.size) + ')';

            // Show preview
            uploadPrompt.style.display = 'none';
            previewContainer.style.display = 'block';

            // Upload to server
            uploadImageToServer(file, function(response) {
              if (response.status === 'success') {
                // OSS 上传接口返回的是 url 字段，而不是 data.path
                transferPic.value = response.url;
                
                // 隐藏上传状态指示器
                uploadStatus.style.display = 'none';
                previewContainer.style.display = 'block';
                
                // 设置成功样式
                dropZone.classList.add('has-file');
                dropZone.classList.remove('uploading');
                dropZone.classList.add('border-success');
                dropZone.style.borderStyle = 'solid';
                dropZone.style.borderWidth = '2px';
                dropZone.style.borderColor = '#28a745';
                
                // 添加成功标记
                const successIcon = document.createElement('div');
                successIcon.className = 'upload-success-icon';
                successIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
                if (!document.querySelector('.upload-success-icon')) {
                  previewContainer.appendChild(successIcon);
                }
                
                // 启用提交按钮
                submitBtn.disabled = false;
                uploadRequiredAlert.style.display = 'none';
                
                // 显示成功提示
                Swal.fire({
                  icon: 'success',
                  title: 'Upload Successful',
                  text: 'Your payment proof has been uploaded successfully',
                  confirmButtonText: 'OK'
                });
              } else {
                // 隐藏上传状态指示器
                uploadStatus.style.display = 'none';
                uploadPrompt.style.display = 'block';
                
                // 显示错误提示
                Swal.fire({
                  icon: 'error',
                  title: 'Upload Failed',
                  text: response.info || 'Failed to upload image',
                  confirmButtonText: 'OK'
                });
                resetFileUpload();
              }
            });
          };

          reader.onerror = function() {
            Swal.fire({
              icon: 'error',
              title: 'File Read Error',
              text: 'Failed to read the selected file',
              confirmButtonText: 'OK'
            });
          };

          reader.readAsDataURL(file);
        }

        // Event Listeners

        // Drag and drop
        dropZone.addEventListener('dragover', function(e) {
          e.preventDefault();
          e.stopPropagation();
          this.classList.add('border-primary');
        });

        dropZone.addEventListener('dragleave', function(e) {
          e.preventDefault();
          e.stopPropagation();
          this.classList.remove('border-primary');
        });

        dropZone.addEventListener('drop', function(e) {
          e.preventDefault();
          e.stopPropagation();
          this.classList.remove('border-primary');

          if (e.dataTransfer.files.length) {
            fileInput.files = e.dataTransfer.files;
            handleFiles(fileInput.files);
          }
        });

        // Browse button click
        if (browseBtn) {
          browseBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (!dropZone.classList.contains('has-file')) {
              fileInput.click();
            }
          });
        }

        // Drop zone click
        dropZone.addEventListener('click', function(e) {
          // Don't trigger if clicking on specific elements
          if (e.target === fileInput ||
              (browseBtn && (e.target === browseBtn || browseBtn.contains(e.target))) ||
              previewContainer.contains(e.target) ||
              e.target === removeFile || removeFile.contains(e.target) ||
              dropZone.classList.contains('has-file')) {
            return;
          }

          fileInput.click();
        });

        // File input change
        fileInput.addEventListener('change', function() {
          if (this.files.length) {
            handleFiles(this.files);
          }
        });

        // Remove file button
        removeFile.addEventListener('click', function(e) {
          e.stopPropagation();
          e.preventDefault();
          resetFileUpload();
        });
      }

      // Initialize file upload
      $(document).ready(function() {
        initFileUpload();
      });

      // 设置默认日期为当前日期
      function setDefaultDateTime() {
        const now = new Date();
        // 格式化为 YYYY-MM-DD，只保留年月日
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const defaultDate = `${year}-${month}-${day}`;
        $('#transfer_time').val(defaultDate);
      }

      setDefaultDateTime();

      // 初始化货币选择
      function initCurrencySelection() {
        const cnyDisabled = $('#cny-payment-option').attr('disabled');
        const usdDisabled = $('#usd-payment-option').attr('disabled');

        // 选择USD如果CNY不可用但USD可用
        const currency = (cnyDisabled && !usdDisabled) ? 'usd' : 'cny';

        // 更新商户信息显示和金额，使用自动模式
        updatePaymentDetailsDisplay(currency, 'auto');
        
        // 存储初始货币选择，用于后续判断是否为用户手动切换
        window.initialCurrency = currency;
      }

      // 初始化货币选择
      initCurrencySelection();
      
      // 表单提交验证
      $('#transferForm').on('submit', function(e) {
        const transferPic = $('#transfer_pic').val();
        if (!transferPic) {
          e.preventDefault();
          $('#upload-required-alert').show();
          Swal.fire({
            icon: 'warning',
            title: 'Upload Required',
            text: 'Please upload your payment proof before submitting the form',
            confirmButtonText: 'OK'
          });
          
          // 滚动到上传区域
          $('html, body').animate({
            scrollTop: $('#drop-zone').offset().top - 100
          }, 500);
          
          // 高亮上传区域
          $('#drop-zone').addClass('border-warning').removeClass('border-primary');
          setTimeout(function() {
            $('#drop-zone').removeClass('border-warning');
          }, 2000);
          
          return false;
        }
        return true;
      });
    });
  </script>
</block>