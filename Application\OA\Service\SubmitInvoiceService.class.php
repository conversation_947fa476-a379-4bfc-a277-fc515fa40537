<?php

namespace OA\Service;

use Common\Lib\InvoiceStatusConstants;

/**
 * OA发票提交服务类
 * 处理发票提交到OA系统的业务逻辑
 */
class SubmitInvoiceService extends BaseService
{
    /**
     * 提交发票数据到OA系统
     * 
     * @param array $invoices 发票数据数组
     * @return array 提交结果
     */
    public function submit($invoices)
    {
        try {
            // 使用BuildInvoiceDataService处理发票数据
            $buildInvoiceDataService = new BuildInvoiceDataService();
            
            // 解析发票数据，转换为OA系统需要的格式
            $formattedInvoices = $buildInvoiceDataService->parseInvoiceData($invoices);
            
            if (empty($formattedInvoices)) {
                return ['status' => false, 'msg' => '没有可提交的发票数据'];
            }
            
            // 提交每一张发票
            $results = [];
            foreach ($formattedInvoices as $invoice) {
                $result = $this->postInvoice($invoice);
                $results[] = $result;
                
                // 如果提交失败，记录错误信息
                if (!isset($result['code']) || $result['code'] != 200) {
                    \Think\Log::write('发票提交失败: ' . json_encode($result), 'ERROR');
                }
            }
            
            // 返回提交结果
            return ['status' => true, 'data' => $results];
        } catch (\Exception $e) {
            \Think\Log::write('发票提交异常: ' . $e->getMessage(), 'ERROR');
            return ['status' => false, 'msg' => $e->getMessage()];
        }
    }
    
    /**
     * 提交单个发票到OA系统
     * 
     * @param array $data 发票数据
     * @return array 提交结果
     */
    protected function postInvoice($data)
    {
        // 验证参数
        $validateResult = $this->validate(
            $data,
            array(
                'userId' => array(
                    'required' => true,
                    'message' => '用户ID不能为空',
                ),
                'deptId' => array(
                    'required' => true,
                    'message' => '部门ID不能为空',
                ),
                'submitName' => array(
                    'required' => true,
                    'message' => '提交人姓名不能为空',
                ),
                'submitDept' => array(
                    'required' => true,
                    'message' => '提交部门不能为空',
                ),
                'orderNum' => array(
                    'required' => true,
                    'message' => '订单号不能为空',
                ),
                'invoiceTitle' => array(
                    'required' => true,
                    'message' => '发票抬头不能为空',
                ),
                'buyerTaxNum' => array(
                    'required' => true,
                    'message' => '购买方税号不能为空',
                ),
                'amount' => array(
                    'required' => true,
                    'message' => '金额不能为空',
                    'validator' => function ($value) {
                        return is_numeric($value) && floatval($value) > 0;
                    },
                    'validator_message' => '金额必须为大于0的数字',
                ),
                'buyerPhone' => array(
                    'required' => true,
                    'message' => '购买方电话不能为空',
                ),
                'buyerEmail' => array(
                    'required' => true,
                    'message' => '购买方邮箱不能为空',
                    'validator' => function ($value) {
                        return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
                    },
                    'validator_message' => '购买方邮箱格式不正确',
                ),
                'goodsInfo' => array(
                    'required' => true,
                    'message' => '商品信息不能为空',
                ),
            )
        );

        if (!$validateResult['status']) {
            throw new \Common\Exception\LocalOperationException($validateResult['msg']);
        }

        // 构建请求数据，确保字段顺序和类型正确
        $requestData = array(
            'userId' => intval($data['userId']),
            'deptId' => intval($data['deptId']),
            'submitName' => $data['submitName'],
            'submitDept' => $data['submitDept'],
            'no' => isset($data['no']) ? $data['no'] : null,
            'orderNum' => $data['orderNum'],
            'invoiceTitle' => $data['invoiceTitle'],
            'buyerTaxNum' => $data['buyerTaxNum'],
            'amount' => floatval($data['amount']),
            'buyerPhone' => $data['buyerPhone'],
            'buyerEmail' => $data['buyerEmail'],
            'buyerAddress' => isset($data['buyerAddress']) ? $data['buyerAddress'] : null,
            'buyerAccount' => isset($data['buyerAccount']) ? $data['buyerAccount'] : null,
            'buyerAccountName' => isset($data['buyerAccountName']) ? $data['buyerAccountName'] : null,
            'salerCompany' => isset($data['salerCompany']) ? $data['salerCompany'] : null,
            'goodsInfo' => $data['goodsInfo'],
            'invoiceType' => isset($data['invoiceType']) ? $data['invoiceType'] : null,
            'submitReturnApi' => isset($data['submitReturnApi']) ? $data['submitReturnApi'] : null,
            'remark' => isset($data['remark']) ? $data['remark'] : null,
            'invoiceRemark' => isset($data['invoiceRemark']) ? $data['invoiceRemark'] : null, // 新增：发票备注信息
            'buyerTel' => isset($data['buyerTel']) ? $data['buyerTel'] : null // 新增：买方电话
        );
        

        // 调用API提交发票数据
        $result = $this->sendOaApiRequest('addReceipt', $requestData);
        
        return $result;
    }
    
    /**
     * 发送请求到OA API
     * 
     * @param string $action API动作
     * @param array $data 请求数据
     * @return array 响应结果
     */
    protected function sendOaApiRequest($action, $data)
    {
        // 获取当前环境
        $environment = C('OA_ENVIRONMENT');
        
        // 获取API基础URL
        $baseUrl = C('OA_API_URLS.' . $environment);
        if (empty($baseUrl)) {
            throw new \Common\Exception\LocalOperationException('OA API URL未配置');
        }
        
        // 构建完整URL
        $url = 'http://' . $baseUrl . $action;
        
        // 获取API密钥
        $secretKey = C('OA_API_SECRET_KEY');
        if (empty($secretKey)) {
            throw new \Common\Exception\LocalOperationException('OA API密钥未配置');
        }
        
        // 添加时间戳和签名
        $timestamp = time();
        $data['timestamp'] = $timestamp;
        $data['sign'] = $this->generateSign($data, $secretKey);
        
        // 发送POST请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Accept: application/json'
        ));
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        // 记录请求和响应
        \Think\Log::write('OA API请求: ' . $url . ', 数据: ' . json_encode($data), 'INFO');
        \Think\Log::write('OA API响应: ' . $response . ', HTTP状态码: ' . $httpCode, 'INFO');
        
        if ($error) {
            \Think\Log::write('OA API错误: ' . $error, 'ERROR');
            throw new \Common\Exception\RemoteApiException('API请求失败: ' . $error);
        }
        
        // 解析响应
        $result = json_decode($response, true);
        if (!$result) {
            throw new \Common\Exception\RemoteApiException('无法解析API响应: ' . $response);
        }
        
        return $result;
    }
    
    /**
     * 生成API请求签名
     * 
     * @param array $data 请求数据
     * @param string $secretKey 密钥
     * @return string 签名
     */
    protected function generateSign($data, $secretKey)
    {
        // 按键名排序
        ksort($data);
        
        // 构建签名字符串
        $signStr = '';
        foreach ($data as $key => $value) {
            if ($key != 'sign' && !is_null($value) && $value !== '') {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr .= 'key=' . $secretKey;
        
        // 生成MD5签名
        return md5($signStr);
    }
    
    /**
     * 更新发票状态
     * 
     * @param int $invoiceId 发票ID
     * @param int $status 新状态
     * @return bool 是否成功
     */
    public function updateInvoiceStatus($invoiceId, $status)
    {
        $invoiceModel = D('OaInvoice', 'OA');
        
        $data = [
            'status' => $status,
            'update_time' => time()
        ];
        
        $result = $invoiceModel->where(['id' => $invoiceId])->save($data);
        
        return $result !== false;
    }
    
    /**
     * 处理OA回调
     * 
     * @param array $callbackData 回调数据
     * @return bool 处理是否成功
     */
    public function handleCallback($callbackData)
    {
        try {
            // 验证回调数据
            if (empty($callbackData) || !isset($callbackData['orderNum']) || !isset($callbackData['status'])) {
                \Think\Log::write('无效的回调数据: ' . json_encode($callbackData), 'ERROR');
                return false;
            }
            
            // 查找对应的发票记录
            $invoiceModel = D('OaInvoice', 'OA');
            $invoice = $invoiceModel->where(['oa_id' => $callbackData['orderNum']])->find();
            
            if (empty($invoice)) {
                \Think\Log::write('未找到对应的发票记录: ' . $callbackData['orderNum'], 'ERROR');
                return false;
            }
            
            // 根据回调状态更新发票状态
            $newStatus = $this->mapCallbackStatus($callbackData['status']);
            
            // 更新发票状态
            $updateData = [
                'status' => $newStatus,
                'update_time' => time()
            ];
            
            // 如果回调中包含发票号，也更新发票号
            if (isset($callbackData['invoiceNo']) && !empty($callbackData['invoiceNo'])) {
                $updateData['no'] = $callbackData['invoiceNo'];
            }
            
            $result = $invoiceModel->where(['id' => $invoice['id']])->save($updateData);
            
            \Think\Log::write('发票状态更新: ID=' . $invoice['id'] . ', 新状态=' . $newStatus . ', 结果=' . ($result ? '成功' : '失败'), 'INFO');
            
            return $result !== false;
        } catch (\Exception $e) {
            \Think\Log::write('处理回调异常: ' . $e->getMessage(), 'ERROR');
            return false;
        }
    }
    
    /**
     * 将OA回调状态映射为系统状态
     * 
     * @param string $callbackStatus OA回调状态
     * @return int 系统状态码
     */
    protected function mapCallbackStatus($callbackStatus)
    {
        switch ($callbackStatus) {
            case 'success':
                return InvoiceStatusConstants::INVOICE_SUCCESS;
            case 'failed':
                return InvoiceStatusConstants::INVOICE_FAILED;
            default:
                return InvoiceStatusConstants::INVOICE_PENDING;
        }
    }
}
