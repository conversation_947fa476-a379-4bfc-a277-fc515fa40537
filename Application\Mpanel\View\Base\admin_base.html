<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <block name="title">
        <title>{$page_title|default="管理后台"}</title>
    </block>
    <link rel="stylesheet" href="__PUBLIC__/admin_style/css/templatemo_main.css">
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 核心JS库 -->
    <script src="__PUBLIC__/admin_style/js/jquery.min.js"></script>
    <script src="__ROOT__/static/js/just-validate.production.min.js"></script>
    <script src="__ROOT__/static/js/accounting.min.js"></script>
    <script src="__ROOT__/static/js/<EMAIL>"></script>
    <!-- Bootstrap Table -->
    <!-- <link href="https://unpkg.com/bootstrap-table@1.18.3/dist/bootstrap-table.min.css" rel="stylesheet">
    <script src="https://unpkg.com/bootstrap-table@1.18.3/dist/bootstrap-table.min.js"></script>
    <script src="https://unpkg.com/bootstrap-table@1.18.3/dist/locale/bootstrap-table-zh-CN.min.js"></script> -->

    <!-- Toastr -->
    <!-- <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script> -->

    <!-- 自定义状态标签样式 -->
    <link href="__PUBLIC__/css/custom-status-labels.css" rel="stylesheet">


    <!-- 自定义CSS -->

    <style>
        body {
            padding-top: 50px;
            background-color: #f9f9f9;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* 导航栏 */
        .navbar {
            margin-bottom: 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .navbar-inverse {
            background: linear-gradient(to right, #1a5276, #2874a6);
        }

        .navbar-header .logo h1 {
            color: #fff;
            font-size: 22px;
            margin: 15px 0;
            font-weight: 300;
            letter-spacing: 0.5px;
            padding: 0 15px;
        }

        .navbar-right .navbar-text {
            color: #ecf0f1;
            margin: 15px 10px;
        }

        .navbar-right .navbar-text a {
            color: lightblue;
            margin-left: 5px;
            transition: color 0.3s;
        }

        .navbar-right .navbar-text a:hover {
            color: #2980b9;
            text-decoration: none;
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            top: 51px;
            bottom: 0;
            left: 0;
            z-index: 1000;
            display: block;
            padding: 0;
            overflow-x: hidden;
            overflow-y: auto;
            background-color: #4e91cd;
            border-right: 1px solid #3a7ab7;
            width: 250px;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        /* 主内容区 */
        .main {
            padding: 25px;
            margin-left: 250px;
            width: calc(100% - 250px);
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-top: 15px;
            margin-bottom: 15px;
            min-height: calc(100vh - 80px);
        }

        /* 菜单样式 */
        .templatemo-sidebar-menu {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .templatemo-sidebar-menu li {
            border-bottom: 1px solid #5e9fd6;
            transition: background-color 0.3s;
        }

        .templatemo-sidebar-menu li:last-child {
            border-bottom: none;
        }

        .templatemo-sidebar-menu li a {
            display: block;
            padding: 15px;
            color: #ffffff;
            text-decoration: none;
            border-left: 4px solid transparent;
            transition: all 0.3s ease;
        }

        .templatemo-sidebar-menu li a:hover,
        .templatemo-sidebar-menu li a.active {
            background-color: #3a7ab7;
            border-left-color: #ffffff;
            color: #ffffff;
        }

        .templatemo-sidebar-menu li.active a {
            background-color: #3a7ab7;
            border-left-color: #ffffff;
            color: #ffffff;
        }

        .templatemo-sidebar-menu li a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
            font-size: 16px;
        }

        .templatemo-sidebar-menu .badge {
            float: right;
            margin-top: 2px;
            background-color: #e74c3c;
            padding: 3px 7px;
            border-radius: 10px;
        }

        /* 面包屑导航 */
        .breadcrumb {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 12px 15px;
            margin-bottom: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .breadcrumb li a {
            color: #3498db;
        }

        /* 按钮样式 */
        .btn-primary {
            background-color: #3498db;
            border-color: #2980b9;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2573a7;
        }

        /* Beta 标签 */
        .beta {
            background-color: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            margin-left: 5px;
            text-transform: uppercase;
            position: relative;
            top: -2px;
        }

        @keyframes blink {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

        .blink {
            animation: blink 2s ease-in-out infinite;
        }

        /* 响应式调整 */
        @media (max-width: 991px) {
            .sidebar {
                position: static;
                width: 100%;
                margin-bottom: 20px;
                box-shadow: none;
            }

            .main {
                margin-left: 0;
                width: 100%;
            }
        }

        /* 更小的屏幕 */
        @media (max-width: 767px) {
            .navbar-header {
                float: none;
            }

            .navbar-left,
            .navbar-right {
                float: none !important;
            }

            .navbar-toggle {
                display: block;
            }

            .collapse.navbar-collapse {
                display: none !important;
            }

            .collapse.in {
                display: block !important;
            }

            .navbar-right .navbar-text {
                margin: 10px 15px;
            }
        }
    </style>
    <block name="style"></block>

</head>

<body>
    <!-- 顶部导航 -->
    <div class="navbar navbar-inverse" role="navigation">
        <div class="navbar-header">
            <div class="logo">
                <h1><i class="fa fa-university mr-2"></i> Conference Admin</h1>
            </div>
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
        </div>
        <div class="navbar-collapse collapse">

            <ul class="nav navbar-nav navbar-right">
                <li>
                    <p class="navbar-text">
                        <span><i class="fa fa-user-circle"></i> {~$uid=session('userid')} {$uid,fullname|admin_info}
                            ({$uid,username|admin_info}) | </span>
                        <a href="{:U('Mpanel/index/myaccount')}"><i class="fa fa-user" aria-hidden="true"></i> My
                            account </a>
                        <a href="{:U('Mpanel/login/login_out')}"><i class="fa fa-power-off" aria-hidden="true"></i>
                            Logout </a> |
                    </p>


                </li>
                <li>
                    <div class="btn-group" style="margin: 8px 15px;">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                            <i class="fa fa-money"></i> API查账 <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu" role="menu">
                            <li><a href="/Pays/inquiry/"><i class="fa fa-credit-card"></i> 易支付查账</a></li>
                            <li><a href="/awxpay/checkingForm"><i class="fa fa-check-square-o"></i> AWX 查账</a></li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 左侧菜单 -->
            <div class="col-md-3 sidebar">
                <div class="navbar-collapse collapse templatemo-sidebar">
                    <ul class="templatemo-sidebar-menu">
                        <?php $grade = session('grade')?>
                        <li <if condition="$action eq index"> class="active"</if>>
                            <a href="{:U('Mpanel/Index/index')}" class="menu-item">
                                <i class="fa fa-dashboard"></i>
                                <span>管理首页</span>
                            </a>
                        </li>
                        <li>
                            <a href="{:U('Mpanel/Index/conference')}" class="menu-item">
                                <i class="fa fa-calendar"></i>
                                <span>会议管理</span>
                                <if condition="get_paper_total() gt 0">
                                    <span class="badge">
                                        <?php echo get_paper_total() ?>
                                    </span>
                                </if>
                            </a>
                        </li>

                        <li>
                            <a href="{:U('Mpanel/AdminConfRegister/index')}" class="menu-item">
                                <i class="fa fa-users"></i>
                                <span>注册管理</span>
                                <if condition="get_reg_total() gt 0">
                                    <span class="badge">
                                        <?php echo get_reg_total() ?>
                                    </span>
                                </if>
                            </a>
                        </li>

                        <li>
                            <a href="{:U('Mpanel/Index/pay_info')}" class="menu-item">
                                <i class="fa fa-money"></i>
                                <span>在线支付管理</span>
                                <if condition="get_pay_total_fast() gt 0">
                                    <span class="badge">
                                        {:get_pay_total_fast()}
                                    </span>
                                </if>
                            </a>
                        </li>
                        <li>
                            <a href="{:U('Mpanel/Claim/index')}" class="menu-item">
                                <i class="fa fa-link"></i>
                                <span>订单认领</span>
                            </a>
                        </li>
                        <li>
                            <a href="{:U('Mpanel/TransferAccounts/index')}">
                                <i class="fa fa-exchange"></i>
                                <span>转账汇款管理</span>
                                <if condition="get_transfer_pending_total() gt 0">
                                    <span class="badge">
                                        <?php echo get_transfer_pending_total() ?>
                                    </span>
                                </if>
                            </a>
                        </li>
                        <li>
                            <a href="{:U('OA/Invoice/index')}">
                                <i class="fa fa-file-text"></i>
                                <span>发票管理</span>
                                <if condition="get_oa_invoice_total() gt 0">
                                    <span class="badge">
                                        <?php echo get_oa_invoice_total() ?>
                                    </span>
                                </if>
                            </a>
                        </li>




                        <li>
                            <a href="{:U('OA/Audit/auditList')}">
                                <i class="fa fa-list-alt"></i> OA查账记录
                            </a>
                        </li>

                        <if condition="($grade eq 1)">
                            <li>
                                <a href="{:U('Mpanel/Index/member_list')}" class="menu-item">
                                    <i class="fa fa-user"></i>
                                    <span>会员信息</span>
                                </a>
                            </li>
                        </if>
                        <if condition="($grade neq 3)">
                            <li>
                                <a href="{:U('Mpanel/Index/manager')}" class="menu-item">
                                    <i class="fa fa-users"></i>
                                    <span>管理员管理</span>
                                </a>
                            </li>
                        </if>
                        <if condition="session('grade') eq 2">
                            <php>
                                $ministerModel = M('sub_admin_minister');
                                $isMinister = $ministerModel->where(array('minister_id' => session('userid')))->count();
                            </php>
                            <if condition="$isMinister gt 0">
                                <li>
                                    <a href="{:U('Mpanel/Minister/index')}" class="menu-item">
                                        <i class="fa fa-briefcase"></i>
                                        <span>我关注的会</span>
                                    </a>
                                </li>
                            </if>
                        </if>
                        <if condition="($grade eq 1)">
                            <li>
                                <a href="{:U('Mpanel/Index/minister_list')}" class="menu-item">
                                    <i class="fa fa-shield"></i>
                                    <span>部长设置</span>
                                </a>
                            </li>
                        </if>
                        <if condition="($grade eq 1)">
                            <li>
                                <a href="{:U('Mpanel/Index/group_list')}" class="menu-item">
                                    <i class="fa fa-building"></i>
                                    <span>协会管理</span>
                                </a>
                            </li>
                        </if>
                        <if condition="($grade eq 1)">
                            <li>
                                <a href="{:U('Mpanel/Index/mail_list')}" class="menu-item">
                                    <i class="fa fa-envelope"></i>
                                    <span>邮件设置</span>
                                </a>
                            </li>
                        </if>

                        <if condition="($grade eq 1)">
                            <li>
                                <a href="{:U('Mpanel/BankAccount/index')}" class="menu-item">
                                    <i class="fa fa-bank"></i>
                                    <span>银行账户管理</span>
                                </a>
                            </li>
                        </if>
                        <if condition="($grade eq 1)">
                            <li>
                                <a href="{:U('Mpanel/Action/attachments')}" class="menu-item">
                                    <i class="fa fa-paperclip"></i>
                                    <span>附件管理</span>
                                </a>
                            </li>
                        </if>
                        <if condition="($grade eq 1)">
                            <li>
                                <a href="{:U('Mpanel/Index/send_mail')}" class="menu-item">
                                    <i class="fa fa-paper-plane"></i>
                                    <span>邮件记录</span>
                                </a>
                            </li>
                        </if>

                        <if condition="($grade eq 1)">
                            <li>
                                <a href="{:U('Mpanel/Sysmonitor/operation_record')}" class="menu-item">
                                    <i class="fa fa-tachometer" aria-hidden="true"></i> 系统监控
                                </a>
                            </li>


                        </if>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9 main">
                <!-- 面包屑导航 -->
                <block name="breadcrumb">
                    <ol class="breadcrumb">
                        <li><a href="{:U('Index/index')}"><i class="fa fa-home"></i> 首页</a></li>
                        <li class="active">{$page_title}</li>
                    </ol>
                </block>

                <!-- 主要内容 -->
                <block name="content"></block>
            </div>
        </div>
    </div>

    <!-- 核心JS -->
    <script src="__PUBLIC__/admin_style/js/bootstrap.min.js"></script>
    <script src="__PUBLIC__/admin_style/js/Chart.min.js"></script>
    <script src="__PUBLIC__/admin_style/js/templatemo_script.js"></script>

    <!-- 页面特定JS -->
    <block name="script"></block>

    <!-- 全局JS -->
    <script>
        $(document).ready(function () {
            // 激活当前菜单
            $('.menu-item').each(function () {
                if (window.location.href.indexOf($(this).attr('href')) >= 0) {
                    $(this).addClass('active');
                    $(this).parent().addClass('active');
                }
            });
        });
    </script>
</body>

</html>