<?php
// 引入Mpanel模块的公共函数
require_once(APP_PATH . 'Mpanel/Common/function.php');

/**
 * 清理数据中的特殊字符，确保API交互正常
 * 主要清理换行符、回车符和制表符等不可见字符
 * 
 * @param string|array $data 需要清理的数据
 * @return string|array 清理后的数据
 */
function sanitize_oa_data($data) 
{
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $data[$key] = trim(str_replace(array("\r", "\n", "\t"), '', $value));
            } else if (is_array($value)) {
                $data[$key] = sanitize_oa_data($value);
            }
        }
        return $data;
    } else if (is_string($data)) {
        return trim(str_replace(array("\r", "\n", "\t"), '', $data));
    }
    return $data;
}
/**
 * OA模块公共函数
 * 用于提供OA模块的公共函数
 */

/**
 * 获取OA API的基础URL
 * @return string 当前环境下的API基础URL
 */
function get_oa_api_base_url()
{
    $environment = C('OA_ENVIRONMENT');
    $urls = C('OA_API_URLS');
    return isset($urls[$environment]) ? $urls[$environment] : $urls['testing']; // 默认使用测试环境
}

/**
 * 构建完整的OA API请求URL
 * @param string $path API路径（例如：findUser）
 * @return string 完整的API请求URL
 */
function build_oa_api_url($path)
{
    return get_oa_api_base_url() . $path;
}

/**
 * 记录API请求日志
 * @param string $url 请求的URL
 * @param array $data 请求的数据
 * @param mixed $response 响应结果
 * @param string $method 请求方法（GET/POST等）
 */
function log_oa_api_request($url, $data, $response, $method = 'POST')
{
    // 在测试环境下记录更详细的日志
    if (C('OA_ENVIRONMENT') == 'testing' || C('OA_DEBUG') === true) {
        \Think\Log::write("OA API {$method}请求: {$url}", 'INFO');
        \Think\Log::write("OA API请求数据: " . json_encode($data, JSON_UNESCAPED_UNICODE), 'INFO');
        \Think\Log::write("OA API响应: " . json_encode($response, JSON_UNESCAPED_UNICODE), 'INFO');
    } else {
        // 在生产环境下只记录错误
        if (is_array($response) && isset($response['code']) && $response['code'] != 200) {
            \Think\Log::write("OA API错误: {$url}, 数据: " . json_encode($data, JSON_UNESCAPED_UNICODE) .
                ", 响应: " . json_encode($response, JSON_UNESCAPED_UNICODE), 'ERROR');
        }
    }
}

/**
 * 发送HTTP请求到OA API
 * @param string $path API路径
 * @param array $data 请求数据
 * @param string $method 请求方法（GET/POST等）
 * @return array 响应结果
 */
function send_oa_api_request($path, $data, $method = 'POST')
{
    $url = build_oa_api_url($path);
    $timeout = C('OA_API_TIMEOUT') ?: 30; // 默认30秒超时

    // 1. 生成随机字符串
    $apiNonce = bin2hex(openssl_random_pseudo_bytes(16));

    // 2. 获取当前时间戳
    $apiTimestamp = time();

    // 3. 清理数据中的特殊字符（换行符、回车符等）
    $cleanData = sanitize_oa_data($data);
    
    // 4. 将清理后的数据转换为JSON（使用紧凑格式，不带格式化）
    $jsonData = json_encode($cleanData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

    // 4. 获取密钥 - 从配置中获取
    $secretKey = C('OA_API_SECRET_KEY');

    // 记录调试信息
    if (C('OA_ENVIRONMENT') == 'testing' || C('OA_DEBUG') === true) {
        \Think\Log::write("OA API 密钥: {$secretKey}", 'INFO');
        \Think\Log::write("OA API 请求数据(原始): " . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), 'INFO');
        \Think\Log::write("OA API 请求数据(JSON): {$jsonData}", 'INFO');
    }

    // 5. 生成签名
    $signString = $jsonData . $apiNonce . $apiTimestamp;
    $apiSign = hash_hmac('sha256', $signString, $secretKey);

    // 6. 设置请求头
    $headers = [
        'api-nonce: ' . $apiNonce,
        'api-timestamp: ' . $apiTimestamp,
        'api-sign: ' . $apiSign,
        'Content-Type: application/json; charset=utf-8'
    ];

    // 记录签名信息
    if (C('OA_ENVIRONMENT') == 'testing' || C('OA_DEBUG') === true) {
        \Think\Log::write("OA API 随机字符串(api-nonce): {$apiNonce}", 'INFO');
        \Think\Log::write("OA API 时间戳(api-timestamp): {$apiTimestamp}", 'INFO');
        \Think\Log::write("OA API 签名字符串: {$signString}", 'INFO');
        \Think\Log::write("OA API 签名结果(api-sign): {$apiSign}", 'INFO');
    }

    // 使用curl发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);

    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        // 使用JSON格式发送请求体
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    } else if ($method == 'GET' && !empty($data)) {
        $url .= (strpos($url, '?') === false ? '?' : '&') . http_build_query($data);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    // 处理响应
    $result = array();
    if ($error) {
        $result = array('code' => 500, 'msg' => "请求失败: {$error}");
    } else {
        // 尝试解析JSON响应
        $decoded = json_decode($response, true);
        if ($decoded !== null) {
            $result = $decoded;
        } else {
            $result = array('code' => $httpCode, 'data' => $response);
        }
    }

    // 记录日志
    log_oa_api_request($url, $data, $result, $method);

    return $result;
}

/**
 * 发送获取订单号请求到OA API（专用函数）
 * @param int $type 订单类型：0-投稿线上，1-投稿线下，2-游客支付
 * @return array 响应结果
 */
function send_oa_order_num_request($type)
{
    // 构建URL（包含查询参数）
    $url = build_oa_api_url('getOrderNum') . '?type=' . $type;
    $timeout = C('OA_API_TIMEOUT') ?: 30; // 默认30秒超时

    // 1. 生成随机字符串
    $apiNonce = bin2hex(openssl_random_pseudo_bytes(16));

    // 2. 获取当前时间戳
    $apiTimestamp = time();

    // 3. 直接使用type值作为签名数据（不转JSON）
    $data = $type;

    // 4. 获取密钥
    $secretKey = C('OA_API_SECRET_KEY');

    // 5. 生成签名
    $signString = $data . $apiNonce . $apiTimestamp;
    $apiSign = hash_hmac('sha256', $signString, $secretKey);

    // 6. 设置请求头
    $headers = [
        'api-nonce: ' . $apiNonce,
        'api-timestamp: ' . $apiTimestamp,
        'api-sign: ' . $apiSign,
        'Content-Type: application/json; charset=utf-8'
    ];

    // 记录调试信息
    if (C('OA_ENVIRONMENT') == 'testing' || C('OA_DEBUG') === true) {
        \Think\Log::write("OA API 订单号请求 - 密钥: {$secretKey}", 'INFO');
        \Think\Log::write("OA API 订单号请求 - 类型: {$type}", 'INFO');
        \Think\Log::write("OA API 订单号请求 - 随机字符串: {$apiNonce}", 'INFO');
        \Think\Log::write("OA API 订单号请求 - 时间戳: {$apiTimestamp}", 'INFO');
        \Think\Log::write("OA API 订单号请求 - 签名字符串: {$signString}", 'INFO');
        \Think\Log::write("OA API 订单号请求 - 签名结果: {$apiSign}", 'INFO');
    }

    // 使用curl发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data); // 直接发送type值
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    // 处理响应
    $result = array();
    if ($error) {
        $result = array('code' => 500, 'msg' => "请求失败: {$error}");
    } else {
        // 尝试解析JSON响应
        $decoded = json_decode($response, true);
        if ($decoded !== null) {
            $result = $decoded;
        } else {
            $result = array('code' => $httpCode, 'data' => $response);
        }
    }

    // 记录日志
    log_oa_api_request($url, array('type' => $type), $result, 'POST');

    return $result;
}

/**
 * 快速检查OA API服务器连通性
 * 使用较短的超时时间，快速判断OA服务器是否可访问
 * 
 * @param int $timeout 超时时间（秒），默认为3秒
 * @return array 检查结果，包含status(true/false)和message
 */
function check_oa_server_connectivity($timeout = 3)
{
    // 获取OA API基础URL
    $baseUrl = get_oa_api_base_url();

    // 记录开始检查的日志
    \Think\Log::write("开始检查OA服务器连通性: {$baseUrl}", 'INFO');

    // 初始化CURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头信息，不下载主体内容
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout); // 使用较短的超时时间
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout); // 连接超时时间

    // 执行请求
    curl_exec($ch);

    // 获取请求信息
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $connectTime = curl_getinfo($ch, CURLINFO_CONNECT_TIME);

    curl_close($ch);

    // 判断连接结果
    if ($error) {
        // 连接失败
        \Think\Log::write("OA服务器连通性检查失败: {$error}, 连接时间: {$connectTime}秒", 'ERROR');
        return array(
            'status' => false,
            'message' => "无法连接到OA服务器，请检查网络或联系管理员。错误: {$error}",
            'connect_time' => $connectTime,
            'error' => $error
        );
    } else {
        // 连接成功
        \Think\Log::write("OA服务器连通性检查成功: HTTP状态码 {$httpCode}, 连接时间: {$connectTime}秒", 'INFO');
        return array(
            'status' => true,
            'message' => "OA服务器连接正常，连接时间: {$connectTime}秒",
            'http_code' => $httpCode,
            'connect_time' => $connectTime
        );
    }
}

/**
 * 生成成功响应数组
 */
function oa_success_response($data = null, $message = '操作成功', $code = 200, $source = 'local')
{
    return \OA\Common\ResponseHelper::success($data, $message, $code, $source);
}

/**
 * 生成错误响应数组
 */
function oa_error_response($message = '操作失败', $code = 400, $data = null, $errors = array(), $source = 'local')
{
    return \OA\Common\ResponseHelper::error($message, $code, $data, $errors, $source);
}

/**
 * 转换OA API响应为统一格式
 */
function oa_convert_response($oaResponse, $successMessage = null, $errorMessage = null)
{
    return \OA\Common\ResponseHelper::convertOaResponse($oaResponse, $successMessage, $errorMessage);
}
/**
 * 将各种格式的日期字符串转换为标准的YYYY-MM-DD格式
 * 支持的格式包括：
 * - 2018.7.5
 * - 2018-7-5
 * - 2018/7/5
 * - 7.5.2018
 * - 7-5-2018
 * - 7/5/2018
 * - 03/28/2018
 * - July 5, 2018
 * - 5 July 2018
 * 等多种常见格式
 * 
 * @param string $dateStr 输入的日期字符串
 * @return string 标准格式的日期字符串(YYYY-MM-DD)，如果转换失败则返回原字符串
 */
function standardizeDate($dateStr)
{
    // 去除多余空格
    $dateStr = trim($dateStr);

    // 尝试使用strtotime解析日期
    $timestamp = strtotime($dateStr);

    // 如果解析成功，直接返回标准格式
    if ($timestamp !== false) {
        return date('Y-m-d', $timestamp);
    }

    // 如果strtotime解析失败，尝试常见格式的正则匹配

    // 匹配 DD.MM.YYYY 或 D.M.YYYY 格式
    if (preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $dateStr, $matches)) {
        return sprintf('%04d-%02d-%02d', $matches[3], $matches[2], $matches[1]);
    }

    // 匹配 YYYY.MM.DD 或 YYYY.M.D 格式
    if (preg_match('/^(\d{4})\.(\d{1,2})\.(\d{1,2})$/', $dateStr, $matches)) {
        return sprintf('%04d-%02d-%02d', $matches[1], $matches[2], $matches[3]);
    }

    // 匹配 MM.DD.YYYY 或 M.D.YYYY 格式
    if (preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $dateStr, $matches)) {
        return sprintf('%04d-%02d-%02d', $matches[3], $matches[1], $matches[2]);
    }

    // 匹配中文日期格式，如 2018年7月5日
    if (preg_match('/^(\d{4})年(\d{1,2})月(\d{1,2})日$/', $dateStr, $matches)) {
        return sprintf('%04d-%02d-%02d', $matches[1], $matches[2], $matches[3]);
    }

    // 如果所有尝试都失败，返回原始字符串
    return $dateStr;
}
