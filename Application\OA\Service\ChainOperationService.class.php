<?php

namespace OA\Service;

use Common\Exception\LocalOperationException;
use Common\Lib\OaAuditStatusConstants;

/**
 * 链式操作服务类
 * 用于处理查账和开票的链式操作
 */
class ChainOperationService
{

    protected $auditService;
    protected $invoiceService;
    protected $buildOaDataService;
    protected $sendAuditToOAService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->auditService = new AuditService();
        $this->invoiceService = new InvoiceService();
        $this->buildOaDataService = new BuildOaDataService();
        $this->sendAuditToOAService = new SendAuditToOAService();
    }

    /**
     * 处理链式操作
     * @param array $postData 表单数据
     * @return array 处理结果
     * @throws \Common\Exception\LocalOperationException 本地操作异常
     * @throws \Common\Exception\RemoteApiException 远程API异常
     */
    public function processChainOperation($postData)
    {
        try {
            // 记录开始日志
            $this->logOperation('开始链式操作', $postData);

            // 验证数据并处理会议名称（移除空格）
            $this->validateChainData($postData);

            // 确保会议名称不包含空格
            if (isset($postData['confName']) && strpos($postData['confName'], ' ') !== false) {
                $postData['confName'] = str_replace(' ', '', $postData['confName']);
                $this->logOperation('处理后的会议名称', ['confName' => $postData['confName']]);
            }

            // 执行查账步骤
            $auditResult = $this->executeAuditStep($postData);

            // 如果查账失败，直接返回
            if (!$auditResult['status']) {
                $this->logOperation('查账步骤失败', $auditResult);
                return $auditResult;
            }

            // 获取OA流水号
            $oaId = '';

            // 尝试从不同位置获取OA流水号
            if (isset($auditResult['data']) && isset($auditResult['data']['orderNum'])) {
                $oaId = $auditResult['data']['orderNum'];
                $this->logOperation('从 data.orderNum 获取到OA流水号', ['oa_id' => $oaId]);
            } elseif (isset($auditResult['data']) && isset($auditResult['data']['oa_id'])) {
                $oaId = $auditResult['data']['oa_id'];
                $this->logOperation('从 data.oa_id 获取到OA流水号', ['oa_id' => $oaId]);
            }

            // 如果仍然没有获取到OA流水号，尝试从数据库中查询
            if (empty($oaId)) {
                $this->logOperation('从API响应中未获取到OA流水号，尝试从数据库查询', ['audit_result' => $auditResult]);

                $oaAuditAccountModel = D('OaAuditAccount', 'OA');
                $conditions = [];

                // 构建查询条件
                if (isset($postData['reg_id']) && !empty($postData['reg_id'])) {
                    $conditions['reg_id'] = $postData['reg_id'];
                }

                if (isset($postData['pay_id']) && !empty($postData['pay_id'])) {
                    $conditions['pay_id'] = $postData['pay_id'];
                }

                if (!empty($conditions)) {
                    // 查询最新的记录
                    $auditRecord = $oaAuditAccountModel->where($conditions)->order('id desc')->find();

                    if (!empty($auditRecord) && !empty($auditRecord['oa_id'])) {
                        $oaId = $auditRecord['oa_id'];
                        $this->logOperation('从数据库中获取到OA流水号', [
                            'oa_id' => $oaId,
                            'conditions' => $conditions,
                            'audit_record_id' => $auditRecord['id']
                        ]);
                    } else {
                        $this->logOperation('数据库中未找到有效的查账记录', ['conditions' => $conditions]);
                    }
                } else {
                    $this->logOperation('无法构建有效的查询条件', ['post_data' => $postData]);
                }
            }

            // 如果没有获取到OA流水号，返回错误
            if (empty($oaId)) {
                $this->logOperation('未获取到OA流水号，无法继续执行开票', ['audit_result' => $auditResult]);
                return [
                    'status' => false,
                    'message' => '未获取到OA流水号，无法继续执行开票',
                    'audit_result' => $auditResult
                ];
            }

            // 检查是否只执行查账
            $invoiceResult = null;
            $auditOnly = isset($postData['audit_only']) && $postData['audit_only'] == '1';

            if ($auditOnly) {
                // 只执行查账，不处理发票
                $this->logOperation('用户选择只执行查账，跳过发票处理', ['oa_id' => $oaId]);
                $invoiceResult = [
                    'status' => true,
                    'message' => '查账成功，未提交发票'
                ];
            } else if (!empty($postData['selected_invoices']) && is_array($postData['selected_invoices'])) {
                // 使用选中的发票
                $invoiceResult = $this->submitSelectedInvoices($oaId, $postData['selected_invoices']);
            } else {
                // 没有选中的发票，也没有指定只执行查账，返回错误
                throw new \Common\Exception\LocalOperationException('请至少选择一张发票或选择只执行查账');
            }

            // 记录完成日志
            $this->logOperation('链式操作完成', [
                'audit_result' => $auditResult,
                'invoice_result' => $invoiceResult
            ]);

            // 构建返回消息
            $message = '';
            $errorType = '';

            if ($invoiceResult['status']) {
                $message = '链式操作成功完成';
            } else {
                // 如果开票结果中包含详细错误信息，使用该信息
                if (isset($invoiceResult['message'])) {
                    $message = $invoiceResult['message'];
                } else if (isset($invoiceResult['msg'])) {
                    $message = '开票步骤失败：' . $invoiceResult['msg'];
                } else {
                    $message = '开票步骤失败：未知错误';
                }

                // 获取错误类型
                if (isset($invoiceResult['error_type'])) {
                    $errorType = $invoiceResult['error_type'];
                } else if (isset($invoiceResult['error_source'])) {
                    // 根据错误来源设置错误类型
                    if ($invoiceResult['error_source'] === 'remote') {
                        $errorType = '远程错误';
                    } else if ($invoiceResult['error_source'] === 'local') {
                        $errorType = '本地错误';
                    } else {
                        $errorType = '系统错误';
                    }
                } else {
                    $errorType = '未知错误';
                }

                // 记录详细的错误信息
                $this->logOperation('链式操作返回错误', [
                    'message' => $message,
                    'error_type' => $errorType,
                    'invoice_result' => $invoiceResult
                ]);
            }

            // 返回结果
            return [
                'status' => $invoiceResult['status'],
                'message' => $message,
                'error_type' => $errorType,
                'audit_result' => $auditResult,
                'invoice_result' => $invoiceResult
            ];
        } catch (\Common\Exception\RemoteApiException $e) {
            // 记录远程API异常
            $this->logOperation('链式操作远程API异常', ['error' => $e->getMessage()]);
            // 重新抛出异常，让控制器处理
            throw $e;
        } catch (\Common\Exception\LocalOperationException $e) {
            // 记录本地操作异常
            $this->logOperation('链式操作本地异常', ['error' => $e->getMessage()]);
            // 重新抛出异常，让控制器处理
            throw $e;
        } catch (\Exception $e) {
            // 记录其他异常
            $this->logOperation('链式操作未预期异常', ['error' => $e->getMessage()]);
            // 重新抛出异常，让控制器处理
            throw $e;
        }
    }

    /**
     * 验证链式操作数据
     * @param array $postData 表单数据
     * @throws LocalOperationException 如果数据验证失败
     */
    protected function validateChainData($postData)
    {
        // 验证查账数据
        if (empty($postData['reg_id'])) {
            throw new LocalOperationException('参数错误：注册ID不能为空');
        }

        // 验证会议名称
        if (empty($postData['confName'])) {
            throw new LocalOperationException('参数错误：会议名称不能为空');
        }

        // 验证会议名称不包含空格
        if (strpos($postData['confName'], ' ') !== false) {
            // 自动移除空格
            $original = $postData['confName'];
            $postData['confName'] = str_replace(' ', '', $postData['confName']);
            $this->logOperation('会议名称包含空格，已自动移除', ['original' => $original, 'cleaned' => $postData['confName']]);
        }

        // 验证货币类型，只有人民币订单才能开发票
        if (!empty($postData['pay_id']) && !empty($postData['selected_invoices']) && is_array($postData['selected_invoices']) && !empty($postData['selected_invoices'])) {
            $paymentModel = M('SubPay');
            $payment = $paymentModel->where(['id' => $postData['pay_id']])->find();

            if (!empty($payment)) {
                if ($payment['moneytype'] != \Common\Lib\CurrencyTypeConstants::CURRENCY_CNY) {
                    throw new LocalOperationException('美元订单不支持开具发票，只能执行查账操作');
                }
            }
        }

        // 检查是否只执行查账
        $auditOnly = isset($postData['audit_only']) && $postData['audit_only'] == '1';

        if ($auditOnly) {
            // 用户选择只执行查账，不需要验证发票
            $this->logOperation('用户选择只执行查账，跳过发票验证', []);
        } else if (!empty($postData['selected_invoices']) && is_array($postData['selected_invoices'])) {
            if (empty($postData['selected_invoices'])) {
                throw new LocalOperationException('参数错误：请至少选择一张发票');
            }

            // 验证发票是否存在
            $invoiceModel = D('OaInvoice', 'OA');
            $validInvoices = $invoiceModel->where(['id' => ['in', $postData['selected_invoices']]])->count();

            if ($validInvoices === 0) {
                throw new LocalOperationException('参数错误：选中的发票不存在');
            }

            $this->logOperation('使用选中的发票', [
                'selected_count' => count($postData['selected_invoices']),
                'valid_count' => $validInvoices
            ]);
        } else {
            throw new LocalOperationException('参数错误：请至少选择一张发票或选择只执行查账');
        }
    }

    /**
     * 执行查账步骤
     * @param array $postData 表单数据
     * @return array 查账结果
     */
    protected function executeAuditStep($postData)
    {
        $this->logOperation('开始执行查账步骤', $postData);

        // 处理货币类型
        if (isset($postData['unit'])) {
            // 确保货币类型符合OA系统要求
            if ($postData['unit'] === 'CNY') {
                $postData['unit'] = 'CNY';
            } else if ($postData['unit'] === 'USD') {
                $postData['unit'] = 'USD';
            }
        }

        // 确保账户字段存在
        if (empty($postData['account'])) {
            $postData['account'] = 'cs'; // 默认使用cs账户
            $this->logOperation('未提供账户信息，使用默认账户', ['account' => $postData['account']]);
        }

        // 确保金额字段正确
        if (isset($postData['amount'])) {
            // 记录原始金额
            $this->logOperation('原始金额', ['amount' => $postData['amount']]);

            // 确保金额是数字
            if (!is_numeric($postData['amount'])) {
                $this->logOperation('金额不是数字，尝试转换', ['amount' => $postData['amount']]);
                $postData['amount'] = floatval($postData['amount']);
            }

            // 确保金额大于0
            if ($postData['amount'] <= 0) {
                $this->logOperation('金额小于等于0，使用默认值', ['amount' => $postData['amount']]);
                // 尝试从支付信息中获取金额
                $registerModel = D('Register');
                $registerInfo = $registerModel->relation(true)->find($postData['reg_id']);
                if (!empty($registerInfo['payments']['total'])) {
                    $postData['amount'] = $registerInfo['payments']['total'];
                    $this->logOperation('使用支付信息中的金额', ['amount' => $postData['amount']]);
                } else {
                    $postData['amount'] = 0.01; // 使用最小值
                    $this->logOperation('使用最小金额', ['amount' => $postData['amount']]);
                }
            }
        }

        // 调用查账服务
        $auditResult = $this->auditService->processOnlineAudit($postData);

        $this->logOperation('查账步骤完成', $auditResult);

        // 如果查账成功，使用轮询方式检查数据库记录是否已保存
        if ($auditResult['status']) {
            $this->logOperation('查账成功，开始检查数据库记录', []);

            // 构建查询条件
            $oaAuditAccountModel = D('OaAuditAccount', 'OA');
            $conditions = [];

            if (!empty($postData['reg_id'])) {
                $conditions['reg_id'] = $postData['reg_id'];
            }

            if (!empty($postData['pay_id'])) {
                $conditions['pay_id'] = $postData['pay_id'];
            }

            if (!empty($conditions)) {
                // 轮询检查数据库记录，最多尝试3次，每次等待100毫秒
                $maxAttempts = 3;
                $waitTime = 100000; // 100毫秒 = 100000微秒
                $auditRecord = null;

                for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
                    // 查询数据库
                    $auditRecord = $oaAuditAccountModel->where($conditions)->order('id desc')->find();

                    if (!empty($auditRecord)) {
                        $this->logOperation('数据库中已找到查账记录（尝试次数：'.$attempt.'）', [
                            'audit_id' => $auditRecord['id'],
                            'oa_id' => $auditRecord['oa_id']
                        ]);

                        // 确保返回结果中包含 OA 流水号
                        if (!empty($auditRecord['oa_id'])) {
                            if (!isset($auditResult['data'])) {
                                $auditResult['data'] = [];
                            }
                            $auditResult['data']['orderNum'] = $auditRecord['oa_id'];
                            $auditResult['data']['oa_id'] = $auditRecord['oa_id'];
                        }

                        // 找到记录，跳出循环
                        break;
                    } else if ($attempt < $maxAttempts) {
                        // 未找到记录且未达到最大尝试次数，等待一段时间后重试
                        $this->logOperation('数据库中未找到查账记录，等待后重试（尝试次数：'.$attempt.'）', ['conditions' => $conditions]);
                        usleep($waitTime);
                    } else {
                        // 达到最大尝试次数仍未找到记录
                        $this->logOperation('达到最大尝试次数仍未在数据库中找到查账记录', ['conditions' => $conditions]);
                    }
                }
            }
        }

        return $auditResult;
    }



    /**
     * 提交选中的发票
     * @param string $oaId OA流水号
     * @param array $invoiceIds 选中的发票ID数组
     * @return array 处理结果
     */
    protected function submitSelectedInvoices($oaId, $invoiceIds)
    {
        $this->logOperation('开始提交选中的发票', ['oa_id' => $oaId, 'invoice_ids' => $invoiceIds]);

        try {
            // 实例化发票模型
            $invoiceModel = D('OaInvoice', 'OA');

            // 查询查账记录
            $auditAccountModel = D('OaAuditAccount', 'OA');
            $auditRecord = $auditAccountModel->where(['oa_id' => $oaId])->find();

            if (empty($auditRecord)) {
                throw new \Exception('未找到相关的查账记录');
            }

            // 验证选中的发票
            $selectedInvoices = $invoiceModel->where(['id' => ['in', $invoiceIds]])->select();

            if (empty($selectedInvoices)) {
                throw new \Exception('未找到选中的发票');
            }

            // 获取订单总金额
            $orderTotal = 0;
            if (!empty($auditRecord['pay_id'])) {
                $paymentModel = M('SubPay');
                $payment = $paymentModel->where(['id' => $auditRecord['pay_id']])->find();
                if (!empty($payment)) {
                    $orderTotal = floatval($payment['total']);

                    // 验证货币类型，只有人民币订单才能开发票
                    if ($payment['moneytype'] != \Common\Lib\CurrencyTypeConstants::CURRENCY_CNY) {
                        throw new \Exception('美元订单不支持开具发票，只能执行查账操作');
                    }
                }
            }

            $successCount = count($selectedInvoices);
            $totalAmount = 0;

            foreach ($selectedInvoices as $invoice) {
                $totalAmount += floatval($invoice['amount']);
            }

            // 验证发票总金额不超过订单总金额
            if ($orderTotal > 0 && $totalAmount > $orderTotal) {
                throw new \Exception('发票总金额 ' . $totalAmount . ' 元超过订单总金额 ' . $orderTotal . ' 元');
            }

            // 开始事务
            $invoiceModel->startTrans();

            try {
                // 获取当前管理员的OA信息
                $submitName = '';
                $adminModel = D('Admin');
                $adminInfo = $adminModel->where(['id' => session('userid')])->find();

                if (!empty($adminInfo) && !empty($adminInfo['work_id']) && !empty($adminInfo['oa_info'])) {
                    $oaInfo = json_decode($adminInfo['oa_info'], true);
                    if ($oaInfo && !empty($oaInfo['workId']) && !empty($oaInfo['name'])) {
                        $submitName = $oaInfo['workId'] . '#' . $oaInfo['name'];
                    }
                }

                // 如果没有获取到OA信息，使用用户名
                if (empty($submitName)) {
                    $submitName = session('username');
                }

                // 获取订单信息、用户ID和文章ID
                $orderid = '';
                $userid = '';
                $paperid = '';

                if (!empty($auditRecord['pay_id'])) {
                    $paymentModel = M('SubPay');
                    $payment = $paymentModel->where(['id' => $auditRecord['pay_id']])->find();

                    if (!empty($payment)) {
                        if (!empty($payment['orderid'])) {
                            $orderid = $payment['orderid'];
                        }

                        if (!empty($payment['userid'])) {
                            $userid = $payment['userid'];
                        }
                    }
                }

                // 如果没有从支付记录中获取到用户ID，尝试从注册记录中获取
                if (!empty($auditRecord['reg_id'])) {
                    $registerModel = M('SubRegister');
                    $registerInfo = $registerModel->where(['id' => $auditRecord['reg_id']])->find();

                    if (!empty($registerInfo)) {
                        // 获取用户ID
                        if (empty($userid) && !empty($registerInfo['userid'])) {
                            $userid = $registerInfo['userid'];
                        }

                        // 获取文章ID
                        if (!empty($registerInfo['paperid'])) {
                            $paperid = $registerInfo['paperid'];
                        }
                    }
                }

                // 获取当前管理员的session id
                $adminId = session('userid');
                if (empty($adminId)) {
                    $adminId = 0; // 如果没有管理员ID，设置为0
                }
                
                // 更新选中的发票记录，添加 oa_id、order_id、user_id、audit_id、submit_id 和 submitName
                // 注意：不再强制设置 add_by_admin 字段，保留原有值
                $updateData = [
                    'oa_id' => $oaId,
                    'update_time' => time(),
                    'audit_id' => $auditRecord['id'], // 添加查账记录的主键id
                    'submit_id' => $adminId // 设置提交者为当前管理员的session id
                ];

                // 只有在有值的情况下才添加字段，避免覆盖已有值
                if (!empty($orderid)) {
                    $updateData['order_id'] = $orderid;
                }

                if (!empty($userid)) {
                    $updateData['user_id'] = $userid;
                }

                if (!empty($submitName)) {
                    $updateData['submit_name'] = $submitName;
                }

                // 设置文章ID
                if (!empty($paperid)) {
                    $updateData['paper_id'] = $paperid;
                }

                $updateInvoiceResult = $invoiceModel->where(['id' => ['in', $invoiceIds]])->save($updateData);

                if ($updateInvoiceResult === false) {
                    throw new \Exception('更新发票记录的OA流水号失败: ' . $invoiceModel->getError());
                }

                $this->logOperation('成功更新发票记录的OA流水号', [
                    'oa_id' => $oaId,
                    'invoice_ids' => $invoiceIds,
                    'affected_rows' => $updateInvoiceResult
                ]);
                
                // 获取发票服务类实例，用于生成回调URL
                $invoiceService = new \OA\Service\InvoiceService();
                
                // 查询发票详细信息，获取发票编号
                $selectedInvoicesDetails = $invoiceModel->where(['id' => ['in', $invoiceIds]])->select();
                if (!empty($selectedInvoicesDetails)) {
                    // 更新每个发票的回调URL
                    foreach ($selectedInvoicesDetails as $invoice) {
                        if (!empty($invoice['no'])) {
                            // 生成包含发票编号的完整回调URL
                            $callbackUrl = $invoiceService->getCallbackUrl($invoice['no']);
                            if (!empty($callbackUrl)) {
                                // 更新单个发票的回调URL
                                $invoiceModel->where(['id' => $invoice['id']])->save([
                                    'call_back_url' => $callbackUrl
                                ]);
                                $this->logOperation('更新发票回调URL', [
                                    'invoice_id' => $invoice['id'],
                                    'invoice_no' => $invoice['no'],
                                    'callback_url' => $callbackUrl
                                ]);
                            }
                        }
                    }
                }

                // 更新查账记录的发票状态
                $updateResult = $auditAccountModel->where(['id' => $auditRecord['id']])->save([
                    'invoice_status' => \Common\Lib\AuditAccountInvoiceStatusConstants::INVOICE_APPLIED,
                    'update_time' => time()
                ]);

                if ($updateResult === false) {
                    throw new \Exception('更新查账记录发票状态失败: ' . $auditAccountModel->getError());
                }

                // 提交发票到OA
                $result = $this->invoiceService->submitInvoicesToApi($invoiceIds);

                // 提交事务
                $invoiceModel->commit();

                $this->logOperation('提交选中的发票完成', [
                    'success_count' => $successCount,
                    'total_amount' => $totalAmount,
                    'result' => $result
                ]);

                // 添加成功信息
                $result['message'] = "成功提交 {$successCount} 张发票，总金额 {$totalAmount} 元";

                return $result;
            } catch (\Exception $e) {
                // 回滚事务
                $invoiceModel->rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            $this->logOperation('提交选中的发票失败', ['error' => $e->getMessage()]);

            return [
                'status' => false,
                'message' => '提交选中的发票失败: ' . $e->getMessage()
            ];
        }
    }



    /**
     * 生成发票编号
     * 格式：INV-[日期]-[序列号]-[哈希]
     * @return string 发票编号
     */
    protected function generateInvoiceNo()
    {
        $date = date('Ymd');
        $sequence = mt_rand(1000, 9999);
        $hash = substr(md5(uniqid(mt_rand(), true)), 0, 6);

        return "INV-{$date}-{$sequence}-{$hash}";
    }

    /**
     * 记录操作日志
     * @param string $action 操作描述
     * @param array $data 相关数据
     */
    protected function logOperation($action, $data)
    {
        $logMessage = '[链式操作] ' . $action . ': ' . json_encode($data, JSON_UNESCAPED_UNICODE);
        \Think\Log::write($logMessage, 'INFO');
    }

    /**
     * 处理线下一键查账和开票
     * @param array $postData 表单数据
     * @return array 处理结果
     * @throws \Common\Exception\LocalOperationException 本地操作异常
     * @throws \Common\Exception\RemoteApiException 远程API异常
     */
    public function processOfflineChainOperation($postData)
    {
        try {
            // 记录开始日志
            $this->logOperation('开始线下一键查账和开票', $postData);

            // 验证数据并处理会议名称（移除空格）
            $this->validateOfflineChainData($postData);

            // 确保会议名称不包含空格
            if (isset($postData['confName']) && strpos($postData['confName'], ' ') !== false) {
                $postData['confName'] = str_replace(' ', '', $postData['confName']);
                $this->logOperation('处理后的会议名称', ['confName' => $postData['confName']]);
            }

            // 确保账户字段存在
            if (empty($postData['account'])) {
                $postData['account'] = 'cs'; // 默认使用cs账户
                $this->logOperation('未提供账户信息，使用默认账户', ['account' => $postData['account']]);
            }

            // 获取转账信息
            $transferModel = D('SubTransfer');
            $transferInfo = $transferModel->where(['id' => $postData['transfer_id']])->find();

            if (empty($transferInfo)) {
                throw new \Common\Exception\LocalOperationException('未找到相关的转账记录');
            }

            // 检查币种，如果是美元则不允许开票
            $isUSD = ($transferInfo['currency'] == \Common\Lib\CurrencyTypeConstants::CURRENCY_USD);
            if ($isUSD && !empty($postData['selected_invoices'])) {
                throw new \Common\Exception\LocalOperationException('美元订单不支持开具发票，只能执行查账操作');
            }

            // 执行查账步骤
            $auditResult = $this->executeOfflineAuditStep($postData, $transferInfo);

            // 如果查账失败，直接返回
            if (!$auditResult['status']) {
                $this->logOperation('线下查账步骤失败', $auditResult);
                return $auditResult;
            }

            // 获取OA流水号
            $oaId = '';

            // 尝试从不同位置获取OA流水号
            if (isset($auditResult['data']) && isset($auditResult['data']['orderNum'])) {
                $oaId = $auditResult['data']['orderNum'];
            } elseif (isset($auditResult['data']) && isset($auditResult['data']['oa_id'])) {
                $oaId = $auditResult['data']['oa_id'];
            }

            // 如果仍然没有获取到OA流水号，尝试从数据库中查询
            if (empty($oaId)) {
                $oaAuditAccountModel = D('OaAuditAccount', 'OA');
                $auditRecord = $oaAuditAccountModel->where(['reg_id' => $postData['reg_id']])->order('id desc')->find();

                if (!empty($auditRecord) && !empty($auditRecord['oa_id'])) {
                    $oaId = $auditRecord['oa_id'];
                }
            }

            // 如果没有获取到OA流水号，返回错误
            if (empty($oaId)) {
                return [
                    'status' => false,
                    'message' => '未获取到OA流水号，无法继续执行开票',
                    'audit_result' => $auditResult
                ];
            }

            // 检查是否只执行查账或是美元订单
            $invoiceResult = null;
            $auditOnly = isset($postData['audit_only']) && $postData['audit_only'] == '1';

            if ($auditOnly || $isUSD) {
                // 只执行查账，不处理发票
                $invoiceResult = [
                    'status' => true,
                    'message' => '查账成功，未提交发票'
                ];
            } else if (!empty($postData['selected_invoices']) && is_array($postData['selected_invoices'])) {
                // 检查查账请求是否已包含发票数据
                $hasIncludedInvoices = false;

                // 获取查账记录
                $oaAuditAccountModel = D('OaAuditAccount', 'OA');
                $auditRecord = $oaAuditAccountModel->where(['oa_id' => $oaId])->find();

                if (!empty($auditRecord)) {
                    // 检查是否有发票数据
                    $this->logOperation('检查查账请求是否已包含发票数据', [
                        'audit_id' => $auditRecord['id'],
                        'oa_id' => $auditRecord['oa_id']
                    ]);

                    // 如果查账请求已包含发票数据，只更新本地发票状态，不再重复提交
                    $hasIncludedInvoices = true;

                    // 更新本地发票状态
                    $invoiceResult = $this->updateOfflineInvoicesStatus($oaId, $postData['selected_invoices']);
                }

                if (!$hasIncludedInvoices) {
                    // 使用选中的发票，单独提交
                    $invoiceResult = $this->submitOfflineInvoices($oaId, $postData['selected_invoices'], $transferInfo);
                }
            } else {
                // 没有选中的发票，也没有指定只执行查账，返回错误
                throw new \Common\Exception\LocalOperationException('请至少选择一张发票或选择只执行查账');
            }

            // 记录完成日志
            $this->logOperation('线下一键查账和开票完成', [
                'audit_result' => $auditResult,
                'invoice_result' => $invoiceResult
            ]);

            // 构建返回消息
            $message = '';
            $errorType = '';

            if ($invoiceResult['status']) {
                $message = '一键查账和开票操作成功完成';
            } else {
                // 如果开票结果中包含详细错误信息，使用该信息
                if (isset($invoiceResult['message'])) {
                    $message = $invoiceResult['message'];
                } else if (isset($invoiceResult['msg'])) {
                    $message = '开票步骤失败：' . $invoiceResult['msg'];
                } else {
                    $message = '开票步骤失败：未知错误';
                }

                // 获取错误类型
                if (isset($invoiceResult['error_type'])) {
                    $errorType = $invoiceResult['error_type'];
                } else if (isset($invoiceResult['error_source'])) {
                    // 根据错误来源设置错误类型
                    if ($invoiceResult['error_source'] === 'remote') {
                        $errorType = '远程错误';
                    } else if ($invoiceResult['error_source'] === 'local') {
                        $errorType = '本地错误';
                    } else {
                        $errorType = '系统错误';
                    }
                } else {
                    $errorType = '未知错误';
                }
            }

            // 返回结果
            return [
                'status' => $invoiceResult['status'],
                'message' => $message,
                'error_type' => $errorType,
                'audit_result' => $auditResult,
                'invoice_result' => $invoiceResult
            ];
        } catch (\Common\Exception\RemoteApiException $e) {
            // 记录远程API异常
            $this->logOperation('一键查账和开票远程API异常', ['error' => $e->getMessage()]);
            // 重新抛出异常，让控制器处理
            throw $e;
        } catch (\Common\Exception\LocalOperationException $e) {
            // 记录本地操作异常
            $this->logOperation('一键查账和开票本地异常', ['error' => $e->getMessage()]);
            // 重新抛出异常，让控制器处理
            throw $e;
        } catch (\Exception $e) {
            // 记录其他异常
            $this->logOperation('一键查账和开票未预期异常', ['error' => $e->getMessage()]);
            // 重新抛出异常，让控制器处理
            throw $e;
        }
    }

    /**
     * 验证线下一键查账和开票数据
     * @param array $postData 表单数据
     * @throws LocalOperationException 如果数据验证失败
     */
    protected function validateOfflineChainData($postData)
    {
        // 验证查账数据
        if (empty($postData['reg_id'])) {
            throw new LocalOperationException('参数错误：注册ID不能为空');
        }

        // 验证会议名称
        if (empty($postData['confName'])) {
            throw new LocalOperationException('参数错误：会议名称不能为空');
        }

        // 验证转账ID
        if (empty($postData['transfer_id'])) {
            throw new LocalOperationException('参数错误：转账ID不能为空');
        }

        // 检查是否只执行查账
        $auditOnly = isset($postData['audit_only']) && $postData['audit_only'] == '1';

        if ($auditOnly) {
            // 用户选择只执行查账，不需要验证发票
            $this->logOperation('用户选择只执行查账，跳过发票验证', []);
        } else if (!empty($postData['selected_invoices']) && is_array($postData['selected_invoices'])) {
            if (empty($postData['selected_invoices'])) {
                throw new LocalOperationException('参数错误：请至少选择一张发票');
            }

            // 验证发票是否存在
            $invoiceModel = D('OaInvoice', 'OA');
            $validInvoices = $invoiceModel->where(['id' => ['in', $postData['selected_invoices']]])->count();

            if ($validInvoices === 0) {
                throw new LocalOperationException('参数错误：选中的发票不存在');
            }

            $this->logOperation('使用选中的发票', [
                'selected_count' => count($postData['selected_invoices']),
                'valid_count' => $validInvoices
            ]);
        } else {
            throw new LocalOperationException('参数错误：请至少选择一张发票或选择只执行查账');
        }
    }

    /**
     * 执行线下查账步骤
     * @param array $postData 表单数据
     * @param array $transferInfo 转账信息
     * @return array 查账结果
     */
    protected function executeOfflineAuditStep($postData, $transferInfo)
    {
        $this->logOperation('开始执行线下查账步骤', $postData);

        // 处理货币类型
        if (isset($transferInfo['currency'])) {
            // 根据转账记录设置货币类型
            $postData['unit'] = ($transferInfo['currency'] == \Common\Lib\CurrencyTypeConstants::CURRENCY_CNY) ? 'CNY' : 'USD';
        }

        // 确保账户字段存在
        if (empty($postData['account'])) {
            $postData['account'] = 'cs'; // 默认使用cs账户
            $this->logOperation('未提供账户信息，使用默认账户', ['account' => $postData['account']]);
        }

        // 确保金额字段正确
        if (isset($transferInfo['total'])) {
            // 使用转账记录中的金额
            $postData['amount'] = $transferInfo['total'];
            $this->logOperation('使用转账记录中的金额', ['amount' => $postData['amount']]);
        }

        // 获取发票信息（如果有）
        $invoices = [];
        if (!empty($postData['selected_invoices']) && is_array($postData['selected_invoices'])) {
            $invoiceModel = D('OaInvoice', 'OA');
            $invoices = $invoiceModel->where(['id' => ['in', $postData['selected_invoices']]])->select();
        }

        // 获取OA订单号
        $orderNumService = new OrderNumService();
        $orderNum = $orderNumService->getOrderNum(\Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_OFFLINE);

        // 获取用户OA信息
        $userOaInfo = $this->getUserOaInfo();

        // 构造包含发票信息的请求数据
        $requestData = $this->buildOaDataService->offline($orderNum, $transferInfo, $userOaInfo);

        // 添加收款账户信息
        if (!empty($postData['account'])) {
            $requestData['account'] = $postData['account'];
        }

        // 添加备注信息
        if (!empty($postData['remark'])) {
            $requestData['remark'] = $postData['remark'];
        }

        // 如果有发票信息，添加到请求数据中
        if (!empty($invoices)) {
            $requestData['receipt'] = [];
            foreach ($invoices as $invoice) {
                // 生成带有发票编号的回调URL
                $callbackUrl = $this->getCallbackUrl($invoice['no']);

                $invoiceData = [
                    'userId' => intval($userOaInfo['userId']), // 确保是整型
                    'deptId' => intval($userOaInfo['deptId']), // 确保是整型
                    'submitName' => $userOaInfo['submitName'],
                    'submitDept' => $userOaInfo['deptName'],
                    'no' => null, // 设置为null，让OA系统自动生成发票编号
                    'orderNum' => $orderNum,
                    'invoiceTitle' => $invoice['invoice_title'],
                    'buyerTaxNum' => $invoice['buyer_tax_num'],
                    'amount' => floatval($invoice['amount']), // 转换为浮点数，确保与主体的amount类型一致
                    'buyerPhone' => $invoice['buyer_phone'],
                    'buyerEmail' => $invoice['buyer_email'],
                    'buyerAddress' => $invoice['buyer_address'],
                    'buyerAccount' => $invoice['buyer_account'],
                    'buyerAccountName' => $invoice['buyer_account_name'],
                    'salerCompany' => isset($invoice['saler_company']) ? $invoice['saler_company'] : '',
                    'goodsInfo' => $invoice['goods_info'],
                    'invoiceType' => $invoice['invoice_type'],
                    'submitReturnApi' => $callbackUrl,
                    'remark' => isset($invoice['remark']) ? $invoice['remark'] : '',
                    'invoiceRemark' => isset($invoice['invoice_remark']) ? $invoice['invoice_remark'] : null, // 新增：发票备注信息
                    'buyerTel' => isset($invoice['buyer_tel']) ? $invoice['buyer_tel'] : null // 新增：买方电话
                ];

                $requestData['receipt'][] = $invoiceData;
            }
        }

        // 发送包含发票信息的查账请求
        $result = $this->sendAuditToOAService->send($requestData, 'offline');

        // 处理结果
        if ($result['code'] == 200) {
            // 记录查账请求
            $oaAuditAccountService = new OaAuditAccountService();
            $oaAuditAccountService->recordAuditRequest($requestData, $postData['reg_id'], 0, $result);

            // 如果有发票，更新发票状态
            if (!empty($invoices)) {
                $this->logOperation('查账请求包含发票数据，更新发票状态', [
                    'invoice_count' => count($invoices),
                    'oa_id' => $orderNum
                ]);

                $invoiceModel = D('OaInvoice', 'OA');
                foreach ($invoices as $invoice) {
                    $invoiceModel->where(['id' => $invoice['id']])->save([
                        'oa_id' => $orderNum,
                        'status' => \Common\Lib\InvoiceStatusConstants::INVOICE_WAITING, // 等待开票状态
                        'update_time' => time()
                    ]);
                }

                // 更新查账记录的发票状态
                $auditAccountModel = D('OaAuditAccount', 'OA');
                $auditAccountModel->where(['oa_id' => $orderNum])->save([
                    'invoice_status' => 1, // 已申请状态
                    'update_time' => time()
                ]);
            }

            // 更新转账状态
            $transferAuditSyncService = new TransferAuditSyncService();
            $transferAuditSyncService->updateTransferStatusByOaAudit(
                $transferInfo['id'],
                \Common\Lib\OaAuditStatusConstants::AUDIT_PROCESSING // 处理中状态
            );

            $auditResult = [
                'status' => true,
                'message' => '成功发送到OA',
                'data' => [
                    'orderNum' => $orderNum,
                    'oa_id' => $orderNum
                ]
            ];

            // 如果查账成功，使用轮询方式检查数据库记录是否已保存
            $this->logOperation('线下查账成功，开始检查数据库记录', []);

            // 构建查询条件
            $oaAuditAccountModel = D('OaAuditAccount', 'OA');
            $conditions = ['reg_id' => $postData['reg_id']];

            // 轮询检查数据库记录，最多尝试3次，每次等待100毫秒
            $maxAttempts = 3;
            $waitTime = 100000; // 100毫秒 = 100000微秒
            $auditRecord = null;

            for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
                // 查询数据库
                $auditRecord = $oaAuditAccountModel->where($conditions)->order('id desc')->find();

                if (!empty($auditRecord)) {
                    $this->logOperation('数据库中已找到线下查账记录（尝试次数：'.$attempt.'）', [
                        'audit_id' => $auditRecord['id'],
                        'oa_id' => $auditRecord['oa_id']
                    ]);

                    // 确保返回结果中包含 OA 流水号
                    if (!empty($auditRecord['oa_id'])) {
                        if (!isset($auditResult['data'])) {
                            $auditResult['data'] = [];
                        }
                        $auditResult['data']['orderNum'] = $auditRecord['oa_id'];
                        $auditResult['data']['oa_id'] = $auditRecord['oa_id'];
                    }

                    // 找到记录，跳出循环
                    break;
                } else if ($attempt < $maxAttempts) {
                    // 未找到记录且未达到最大尝试次数，等待一段时间后重试
                    $this->logOperation('数据库中未找到线下查账记录，等待后重试（尝试次数：'.$attempt.'）', ['conditions' => $conditions]);
                    usleep($waitTime);
                } else {
                    // 达到最大尝试次数仍未找到记录
                    $this->logOperation('达到最大尝试次数仍未在数据库中找到线下查账记录', ['conditions' => $conditions]);
                }
            }

            return $auditResult;
        } else {
            return [
                'status' => false,
                'message' => '发送到OA失败：' . $result['msg']
            ];
        }
    }

    /**
     * 更新线下发票状态
     * 当查账请求已包含发票数据时，只更新本地发票状态，不再重复提交
     * @param string $oaId OA流水号
     * @param array $invoiceIds 发票ID数组
     * @return array 更新结果
     */
    protected function updateOfflineInvoicesStatus($oaId, $invoiceIds)
    {
        try {
            $this->logOperation('开始更新线下发票状态', [
                'oa_id' => $oaId,
                'invoice_ids' => $invoiceIds
            ]);

            // 获取发票信息
            $invoiceModel = D('OaInvoice', 'OA');
            $invoices = $invoiceModel->where(['id' => ['in', $invoiceIds]])->select();

            if (empty($invoices)) {
                throw new \Exception('未找到选中的发票');
            }

            // 计算总金额
            $totalAmount = 0;
            foreach ($invoices as $invoice) {
                $totalAmount += floatval($invoice['amount']);
            }

            // 开始事务
            $invoiceModel->startTrans();

            try {
                // 更新发票状态和OA流水号
                $successCount = 0;
                foreach ($invoices as $invoice) {
                    // 更新发票信息
                    $updateData = [
                        'oa_id' => $oaId,
                        'status' => \Common\Lib\InvoiceStatusConstants::INVOICE_WAITING, // 等待开票状态
                        'update_time' => time()
                    ];

                    $updateResult = $invoiceModel->where(['id' => $invoice['id']])->save($updateData);

                    if ($updateResult !== false) {
                        $successCount++;
                    }
                }

                // 更新查账记录的发票状态
                $auditAccountModel = D('OaAuditAccount', 'OA');
                $updateResult = $auditAccountModel->where(['oa_id' => $oaId])->save([
                    'invoice_status' => 1, // 已申请状态
                    'update_time' => time()
                ]);

                if ($updateResult === false) {
                    throw new \Exception('更新查账记录发票状态失败: ' . $auditAccountModel->getError());
                }

                // 提交事务
                $invoiceModel->commit();

                $this->logOperation('更新线下发票状态完成', [
                    'success_count' => $successCount,
                    'total_amount' => $totalAmount
                ]);

                // 返回成功信息
                return [
                    'status' => true,
                    'message' => "成功更新 {$successCount} 张发票状态，总金额 {$totalAmount} 元"
                ];
            } catch (\Exception $e) {
                // 回滚事务
                $invoiceModel->rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            $this->logOperation('更新线下发票状态失败', ['error' => $e->getMessage()]);

            return [
                'status' => false,
                'message' => '更新线下发票状态失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 提交线下发票
     * @param string $oaId OA流水号
     * @param array $invoiceIds 发票ID数组
     * @param array $transferInfo 转账信息
     * @return array 提交结果
     */
    protected function submitOfflineInvoices($oaId, $invoiceIds, $transferInfo)
    {
        try {
            $this->logOperation('开始提交线下发票', [
                'oa_id' => $oaId,
                'invoice_ids' => $invoiceIds
            ]);

            // 获取发票信息
            $invoiceModel = D('OaInvoice', 'OA');
            $invoices = $invoiceModel->where(['id' => ['in', $invoiceIds]])->select();

            if (empty($invoices)) {
                throw new \Exception('未找到选中的发票');
            }

            // 验证发票总金额是否等于转账金额
            $totalAmount = 0;
            foreach ($invoices as $invoice) {
                $totalAmount += floatval($invoice['amount']);
            }

            // 格式化金额，保留两位小数
            $totalAmount = number_format($totalAmount, 2, '.', '');
            $transferAmount = number_format(floatval($transferInfo['total']), 2, '.', '');

            if ($totalAmount != $transferAmount) {
                throw new \Exception("发票总金额({$totalAmount})与转账金额({$transferAmount})不一致");
            }

            // 开始事务
            $invoiceModel->startTrans();

            try {
                // 更新发票状态和OA流水号
                $successCount = 0;
                foreach ($invoices as $invoice) {
                    // 更新发票信息
                    $updateData = [
                        'oa_id' => $oaId,
                        'status' => \Common\Lib\InvoiceStatusConstants::INVOICE_WAITING, // 等待开票状态
                        'update_time' => time()
                    ];

                    $updateResult = $invoiceModel->where(['id' => $invoice['id']])->save($updateData);

                    if ($updateResult !== false) {
                        $successCount++;
                    }
                }

                // 更新查账记录的发票状态
                $auditAccountModel = D('OaAuditAccount', 'OA');
                $updateResult = $auditAccountModel->where(['oa_id' => $oaId])->save([
                    'invoice_status' => 1, // 已申请状态
                    'update_time' => time()
                ]);

                if ($updateResult === false) {
                    throw new \Exception('更新查账记录发票状态失败: ' . $auditAccountModel->getError());
                }

                // 提交发票到OA
                $result = $this->invoiceService->submitInvoicesToApi($invoiceIds);

                // 提交事务
                $invoiceModel->commit();

                $this->logOperation('提交线下发票完成', [
                    'success_count' => $successCount,
                    'total_amount' => $totalAmount,
                    'result' => $result
                ]);

                // 添加成功信息
                $result['message'] = "成功提交 {$successCount} 张发票，总金额 {$totalAmount} 元";

                return $result;
            } catch (\Exception $e) {
                // 回滚事务
                $invoiceModel->rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            $this->logOperation('提交线下发票失败', ['error' => $e->getMessage()]);

            return [
                'status' => false,
                'message' => '提交线下发票失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取当前管理员的OA信息
     * @return array 用户OA信息
     * @throws \Common\Exception\LocalOperationException 如果无法获取OA信息
     */
    protected function getUserOaInfo()
    {
        $adminModel = D('Admin');
        $adminInfo = $adminModel->where(['id' => session('userid')])->find();

        if (empty($adminInfo) || empty($adminInfo['oa_info'])) {
            throw new \Common\Exception\LocalOperationException('无法获取管理员OA信息，请联系系统管理员');
        }

        $oaInfo = json_decode($adminInfo['oa_info'], true);
        if (empty($oaInfo) || !isset($oaInfo['userId']) || !isset($oaInfo['deptId']) || !isset($oaInfo['workId']) || !isset($oaInfo['name']) || !isset($oaInfo['deptName'])) {
            throw new \Common\Exception\LocalOperationException('管理员OA信息不完整，请联系系统管理员');
        }

        // 构造提交人信息
        $userOaInfo = [
            'userId' => intval($oaInfo['userId']), // 确保是整型
            'deptId' => intval($oaInfo['deptId']), // 确保是整型
            'workId' => $oaInfo['workId'],
            'name' => $oaInfo['name'],
            'deptName' => $oaInfo['deptName'],
            'submitName' => $oaInfo['workId'] . '#' . $oaInfo['name']
        ];

        return $userOaInfo;
    }

    /**
     * 获取回调URL
     * 从配置文件中获取当前环境的回调URL，并可选添加发票编号参数
     *
     * @param string $invoiceNo 发票编号（可选）
     * @return string 回调URL
     */
    protected function getCallbackUrl($invoiceNo = '')
    {
        // 从配置文件中获取当前环境
        $environment = C('OA_ENVIRONMENT');

        // 从配置文件中获取回调URL
        $baseUrl = C('OA_CALLBACK_URLS.' . $environment);

        // 如果未配置回调URL，记录警告并返回空字符串
        if (empty($baseUrl)) {
            \Think\Log::write('未配置回调URL，当前环境：' . $environment, 'WARN');
            return '';
        }

        // 如果提供了发票编号，添加到URL中
        if (!empty($invoiceNo)) {
            // 判断URL是否已经包含参数
            $separator = (strpos($baseUrl, '?') !== false) ? '&' : '?';
            // 添加发票编号参数
            $baseUrl .= $separator . 'no=' . urlencode($invoiceNo);
        }

        return $baseUrl;
    }

    /**
     * 处理游客付款一键查账和开票
     * @param array $postData 表单数据
     * @return array 处理结果
     * @throws \Common\Exception\LocalOperationException 本地操作异常
     * @throws \Common\Exception\RemoteApiException 远程API异常
     */
    public function processGuestChainOperation($postData)
    {
        try {
            // 记录开始日志
            $this->logOperation('开始游客付款一键查账和开票', $postData);

            // 验证数据并处理会议名称（移除空格）
            $this->validateGuestChainData($postData);

            // 确保会议名称不包含空格
            if (isset($postData['confName']) && strpos($postData['confName'], ' ') !== false) {
                $postData['confName'] = str_replace(' ', '', $postData['confName']);
                $this->logOperation('处理后的会议名称', ['confName' => $postData['confName']]);
            }

            // 执行查账步骤（已包含发票提交）
            $auditResult = $this->executeGuestAuditStep($postData);

            // 如果查账失败，直接返回
            if (!$auditResult['status']) {
                $this->logOperation('查账步骤失败', $auditResult);
                return $auditResult;
            }

            // 获取OA流水号
            $oaId = '';

            // 尝试从不同位置获取OA流水号
            if (isset($auditResult['data']) && isset($auditResult['data']['orderNum'])) {
                $oaId = $auditResult['data']['orderNum'];
                $this->logOperation('从 data.orderNum 获取到OA流水号', ['oa_id' => $oaId]);
            } elseif (isset($auditResult['data']) && isset($auditResult['data']['oa_id'])) {
                $oaId = $auditResult['data']['oa_id'];
                $this->logOperation('从 data.oa_id 获取到OA流水号', ['oa_id' => $oaId]);
            }

            // 如果仍然没有获取到OA流水号，尝试从数据库中查询
            if (empty($oaId)) {
                $oaAuditAccountModel = D('OaAuditAccount', 'OA');
                $auditRecord = $oaAuditAccountModel->where(['pay_id' => $postData['pay_id']])->order('id desc')->find();

                if (!empty($auditRecord) && !empty($auditRecord['oa_id'])) {
                    $oaId = $auditRecord['oa_id'];
                }
            }

            // 如果没有获取到OA流水号，返回错误
            if (empty($oaId)) {
                return [
                    'status' => false,
                    'message' => '未获取到OA流水号，无法完成操作',
                    'audit_result' => $auditResult
                ];
            }

            // 检查是否只执行查账或是美元订单
            $auditOnly = isset($postData['audit_only']) && $postData['audit_only'] == '1';
            $isUSD = isset($postData['unit']) && $postData['unit'] == 'USD';

            // 记录完成日志
            $this->logOperation('游客付款链式操作完成', [
                'audit_result' => $auditResult,
                'oa_id' => $oaId,
                'audit_only' => $auditOnly,
                'is_usd' => $isUSD
            ]);

            // 构建返回消息
            $message = '';

            // 成功
            if ($auditOnly || $isUSD) {
                $message = '查账申请已成功提交到OA系统';
            } else {
                $message = '查账和发票申请已成功提交到OA系统';
            }

            // 返回结果
            return [
                'status' => true,
                'message' => $message,
                'audit_result' => $auditResult
            ];
        } catch (\Common\Exception\RemoteApiException $e) {
            // 记录远程API异常
            $this->logOperation('游客付款链式操作远程API异常', ['error' => $e->getMessage()]);
            // 重新抛出异常，让控制器处理
            throw $e;
        } catch (\Common\Exception\LocalOperationException $e) {
            // 记录本地操作异常
            $this->logOperation('游客付款链式操作本地异常', ['error' => $e->getMessage()]);
            // 重新抛出异常，让控制器处理
            throw $e;
        } catch (\Exception $e) {
            // 记录其他异常
            $this->logOperation('游客付款链式操作未预期异常', ['error' => $e->getMessage()]);
            // 重新抛出异常，让控制器处理
            throw $e;
        }
    }

    /**
     * 验证游客链式操作数据
     * @param array $postData 表单数据
     * @throws LocalOperationException 如果数据验证失败
     */
    protected function validateGuestChainData($postData)
    {
        // 验证查账数据
        if (empty($postData['pay_id'])) {
            throw new \Common\Exception\LocalOperationException('参数错误：支付ID不能为空');
        }

        // 验证会议名称
        if (empty($postData['confName'])) {
            throw new \Common\Exception\LocalOperationException('参数错误：会议名称不能为空');
        }

        // 验证会议名称不包含空格
        if (strpos($postData['confName'], ' ') !== false) {
            // 自动移除空格
            $original = $postData['confName'];
            $postData['confName'] = str_replace(' ', '', $postData['confName']);
            $this->logOperation('会议名称包含空格，已自动移除', ['original' => $original, 'cleaned' => $postData['confName']]);
        }

        // 验证文章ID
        if (empty($postData['paperCode'])) {
            throw new \Common\Exception\LocalOperationException('参数错误：文章ID不能为空');
        }

        // 验证订单号
        if (empty($postData['creditSerial'])) {
            throw new \Common\Exception\LocalOperationException('参数错误：订单号不能为空');
        }

        // 验证金额
        if (empty($postData['amount'])) {
            throw new \Common\Exception\LocalOperationException('参数错误：金额不能为空');
        }

        // 验证汇款时间
        if (empty($postData['remitTime'])) {
            throw new \Common\Exception\LocalOperationException('参数错误：汇款时间不能为空');
        }

        // 验证汇款人信息
        if (empty($postData['remitterInfo'])) {
            throw new \Common\Exception\LocalOperationException('参数错误：汇款人信息不能为空');
        }

        // 验证收款账户
        if (empty($postData['account'])) {
            throw new \Common\Exception\LocalOperationException('参数错误：收款账户不能为空');
        }
    }

    /**
     * 执行游客查账步骤
     * @param array $postData 表单数据
     * @return array 查账结果
     */
    protected function executeGuestAuditStep($postData)
    {
        try {
            // 记录开始日志
            $this->logOperation('开始执行游客查账步骤', $postData);

            // 确保金额是数字
            if (isset($postData['amount']) && !is_numeric($postData['amount'])) {
                $postData['amount'] = floatval(preg_replace('/[^0-9.]/', '', $postData['amount']));
                $this->logOperation('转换金额为数字', ['amount' => $postData['amount']]);
            }

            // 确保金额大于0
            if (isset($postData['amount']) && $postData['amount'] <= 0) {
                $this->logOperation('金额小于等于0，使用默认值', ['amount' => $postData['amount']]);
                // 尝试从支付信息中获取金额
                $payModel = D('Pay');
                $payInfo = $payModel->find($postData['pay_id']);
                if (!empty($payInfo['total'])) {
                    $postData['amount'] = $payInfo['total'];
                    $this->logOperation('使用支付信息中的金额', ['amount' => $postData['amount']]);
                } else {
                    $postData['amount'] = 0.01; // 使用最小值
                    $this->logOperation('使用最小金额', ['amount' => $postData['amount']]);
                }
            }

            // 获取OA订单号
            $orderNumService = new OrderNumService();
            $orderNum = $orderNumService->getOrderNum(\Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_GUEST);

            // 获取用户OA信息
            $userOaInfo = $this->getUserOaInfo();

            // 获取支付信息
            $payModel = D('Pay');
            $payInfo = $payModel->find($postData['pay_id']);
            if (empty($payInfo)) {
                throw new \Exception('支付信息不存在');
            }

            // 构造基本查账数据
            $buildOaDataService = new BuildOaDataService();
            $requestData = $buildOaDataService->guest($orderNum, $payInfo, $userOaInfo);

            // 更新请求数据中的字段（使用表单提交的值覆盖）
            if (!empty($postData['confName'])) {
                $requestData['confName'] = $postData['confName'];
            }
            if (!empty($postData['paperCode'])) {
                $requestData['paperCode'] = $postData['paperCode'];
            }
            if (!empty($postData['creditSerial'])) {
                $requestData['creditSerial'] = $postData['creditSerial'];
            }
            if (!empty($postData['amount'])) {
                $requestData['amount'] = floatval($postData['amount']);
            }
            if (!empty($postData['unit'])) {
                $requestData['unit'] = $postData['unit'];
            }
            if (!empty($postData['remitTime'])) {
                $requestData['remitTime'] = $postData['remitTime'];
            }
            if (!empty($postData['remitterInfo'])) {
                $requestData['remitterInfo'] = $postData['remitterInfo'];
            }
            if (!empty($postData['account'])) {
                $requestData['account'] = $postData['account'];
            }
            if (!empty($postData['remark'])) {
                $requestData['remark'] = $postData['remark'];
            }

            // 获取发票信息（如果有）
            $invoices = [];
            if (!empty($postData['selected_invoices']) && is_array($postData['selected_invoices'])) {
                $invoiceModel = D('OaInvoice', 'OA');
                $invoices = $invoiceModel->where(['id' => ['in', $postData['selected_invoices']]])->select();
            }

            // 如果有发票信息，添加到请求数据中
            if (!empty($invoices)) {
                $requestData['receipt'] = [];
                foreach ($invoices as $invoice) {
                    // 生成带有发票编号的回调URL
                    $callbackUrl = $this->getCallbackUrl($invoice['no']);

                    $invoiceData = [
                        'userId' => intval($userOaInfo['userId']), // 确保是整型
                        'deptId' => intval($userOaInfo['deptId']), // 确保是整型
                        'submitName' => $userOaInfo['submitName'],
                        'submitDept' => $userOaInfo['deptName'],
                        'no' => null, // 设置为null，让OA系统自动生成发票编号
                        'orderNum' => $orderNum,
                        'invoiceTitle' => $invoice['invoice_title'],
                        'buyerTaxNum' => $invoice['buyer_tax_num'],
                        'amount' => floatval($invoice['amount']), // 转换为浮点数，确保与主体的amount类型一致
                        'buyerPhone' => $invoice['buyer_phone'],
                        'buyerEmail' => $invoice['buyer_email'],
                        'buyerAddress' => $invoice['buyer_address'],
                        'buyerAccount' => $invoice['buyer_account'],
                        'buyerAccountName' => $invoice['buyer_account_name'],
                        'salerCompany' => isset($invoice['saler_company']) ? $invoice['saler_company'] : '',
                        'goodsInfo' => $invoice['goods_info'],
                        'invoiceType' => $invoice['invoice_type'],
                        'submitReturnApi' => $callbackUrl,
                        'remark' => isset($invoice['remark']) ? $invoice['remark'] : '',
                        'invoiceRemark' => isset($invoice['invoice_remark']) ? $invoice['invoice_remark'] : null, // 新增：发票备注信息
                        'buyerTel' => isset($invoice['buyer_tel']) ? $invoice['buyer_tel'] : null // 新增：买方电话
                    ];

                    $requestData['receipt'][] = $invoiceData;
                }

                $this->logOperation('添加发票信息到查账请求', [
                    'invoice_count' => count($invoices),
                    'oa_id' => $orderNum
                ]);
            }

            // 发送包含发票信息的查账请求
            $sendAuditToOAService = new SendAuditToOAService();
            $result = $sendAuditToOAService->sendGuest($requestData);

            // 处理结果
            if ($result['code'] == 200) {
                // 记录查账请求
                $oaAuditAccountService = new OaAuditAccountService();
                $oaAuditAccountService->recordAuditRequest($requestData, 0, $postData['pay_id'], $result);

                // 如果有发票，更新发票状态
                if (!empty($invoices)) {
                    $this->logOperation('查账请求包含发票数据，更新发票状态', [
                        'invoice_count' => count($invoices),
                        'oa_id' => $orderNum
                    ]);

                    $invoiceModel = D('OaInvoice', 'OA');
                    foreach ($invoices as $invoice) {
                        $invoiceModel->where(['id' => $invoice['id']])->save([
                            'oa_id' => $orderNum,
                            'status' => \Common\Lib\InvoiceStatusConstants::INVOICE_WAITING, // 等待开票状态
                            'update_time' => time()
                        ]);
                    }

                    // 更新查账记录的发票状态
                    $auditAccountModel = D('OaAuditAccount', 'OA');
                    $auditAccountModel->where(['oa_id' => $orderNum])->save([
                        'invoice_status' => 1, // 已申请状态
                        'update_time' => time()
                    ]);
                }

                $auditResult = [
                    'status' => true,
                    'message' => '成功发送到OA',
                    'data' => [
                        'orderNum' => $orderNum,
                        'oa_id' => $orderNum
                    ]
                ];

                // 如果查账成功，使用轮询方式检查数据库记录是否已保存
                $this->logOperation('游客查账成功，开始检查数据库记录', []);

                // 构建查询条件
                $oaAuditAccountModel = D('OaAuditAccount', 'OA');
                $conditions = ['pay_id' => $postData['pay_id']];

                // 轮询检查数据库记录，最多尝试3次，每次等待100毫秒
                $maxAttempts = 3;
                $waitTime = 100000; // 100毫秒 = 100000微秒
                $auditRecord = null;

                for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
                    // 查询数据库
                    $auditRecord = $oaAuditAccountModel->where($conditions)->order('id desc')->find();

                    if (!empty($auditRecord)) {
                        $this->logOperation('数据库中已找到游客查账记录（尝试次数：'.$attempt.'）', [
                            'audit_id' => $auditRecord['id'],
                            'oa_id' => $auditRecord['oa_id']
                        ]);

                        // 确保返回结果中包含 OA 流水号
                        if (!empty($auditRecord['oa_id'])) {
                            if (!isset($auditResult['data'])) {
                                $auditResult['data'] = [];
                            }
                            $auditResult['data']['orderNum'] = $auditRecord['oa_id'];
                            $auditResult['data']['oa_id'] = $auditRecord['oa_id'];
                        }

                        // 找到记录，跳出循环
                        break;
                    } else if ($attempt < $maxAttempts) {
                        // 未找到记录且未达到最大尝试次数，等待一段时间后重试
                        $this->logOperation('数据库中未找到游客查账记录，等待后重试（尝试次数：'.$attempt.'）', ['conditions' => $conditions]);
                        usleep($waitTime);
                    } else {
                        // 达到最大尝试次数仍未找到记录
                        $this->logOperation('达到最大尝试次数仍未在数据库中找到游客查账记录', ['conditions' => $conditions]);
                    }
                }

                $this->logOperation('游客查账步骤完成', $auditResult);
                return $auditResult;
            } else {
                $this->logOperation('游客查账步骤失败', $result);
                return [
                    'status' => false,
                    'message' => '发送到OA失败：' . $result['msg']
                ];
            }
        } catch (\Exception $e) {
            $this->logOperation('游客查账步骤异常', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
}
