<extend name="Base/admin_base" />

<block name="title">
  <title>我关注的会议</title>
</block>

<block name="breadcrumb">
  <ol class="breadcrumb">
    <li><a href="{:U('Index/index')}">首页</a></li>
    <li><a href="{:U('Minister/index')}">我关注的会议</a></li>
  </ol>
</block>

<block name="content">
  <div class="panel panel-default">
    <div class="panel-heading">
      <h3 class="panel-title">会议列表</h3>
    </div>
    <div class="panel-body">
      <div class="table-responsive">
        <table class="table table-striped table-hover table-bordered">
          <thead>
            <tr>
              <th style="width:100px;">简称</th>
              <th>会议全称</th>
              <th>管理信息</th>
              <th>投稿数量</th>
              <th>注册数量</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <volist name="list" id="r">
              <tr>
                <td>
                  {$r.event}

                </td>
                <td>
                  {$r.title}
                </td>
                <td>
                  <?php $uid=$r[userid]?>
                  <span class="label label-primary">组长：{$uid,fullname|admin_info}</span>

                  <!-- 显示组员信息 - 使用下拉菜单 -->
                  <if condition="$r['admin_count'] gt 0">
                    <div class="btn-group">
                      <button type="button" class="btn btn-xs btn-success dropdown-toggle" data-toggle="dropdown">
                        组员: {$r['admin_list'][0]['name']}
                        <if condition="$r['admin_count'] gt 1">
                          <span class="caret"></span>
                        </if>
                      </button>
                      <if condition="$r['admin_count'] gt 1">
                        <ul class="dropdown-menu" role="menu">
                          <foreach name="r['admin_list']" item="admin" key="k">
                            <if condition="$k gt 0">
                              <li><a href="#">{$admin['name']}</a></li>
                            </if>
                          </foreach>
                        </ul>
                      </if>
                    </div>
                  </if>
                </td>
                <td><b>{$r.paper_count}</b></td>
                <td><b>{$r.reg_count}</b></td>
                <td>
                  <a href="{:U('Minister/paper_list',array('id'=>$r['id']))}" class="btn btn-info btn-sm">
                    <i class="fa fa-file-text-o"></i> 投稿列表
                  </a>
                  <a href="{:U('AdminConfRegister/regShow',array('id'=>$r['id']))}" class="btn btn-success btn-sm">
                    <i class="fa fa-users"></i> 注册列表
                  </a>
                </td>
              </tr>
            </volist>
            <empty name="list">
              <tr>
                <td colspan="6" class="text-center">暂无可管理的会议</td>
              </tr>
            </empty>
          </tbody>
        </table>
        <div class="paging text-right">{$page}</div>
      </div>
    </div>
  </div>
</block>