<?php

/**
 * OA模块配置文件
 * 用于配置OA模块的环境和API地址
 */

// 环境设置：testing(测试环境) 或 production(生产环境)
$environment = 'testing';  // 手动修改此变量即可切换环境

// 基础配置
$config = array(
    // 模块基本配置
    'MODULE_ALLOW_LIST' => array('Home', 'Mpanel', 'OA'),
    'DEFAULT_MODULE'    => 'Home',
    'URL_MODEL'         => 2,

    // 环境标识
    'OA_ENVIRONMENT' => $environment,

    // API地址配置
    'OA_API_URLS' => array(
        'testing' => '*************:8787/paperSystem/', // 测试环境API地址
        'production' => 'http://oa.zmeeting.com/prod-api/paperSystem/', // 生产环境API地址，请替换为实际地址
    ),
    // 回调地址配置
    'OA_CALLBACK_URLS' => array(
        'testing' => 'http://caosir.natapp1.cc/OA/Callback/', // 测试环境回调地址，确保以斜杠结尾
        'production' => 'https://confsys.iconf.org/OA/Callback/', // 生产环境回调地址，请替换为实际地址
    ),
    // 定义OA系统API签名密钥
    'OA_API_SECRET_KEY' => 'Yaang^@#^sg66(e6',

    // 回调接口密钥（与API密钥保持一致）
    'PASSWROD' => 'Yaang^@#^sg66(e6)',

    // 允许的IP地址列表
    'ALLOWED_IPS' => array(
        // '127.0.0.1',
        // 'localhost',
        // '::1',
        // '*************', // OA系统测试环境IP
        // '*************', // OA系统测试环境IP
    ),

    // 发票状态常量
    'INVOICE_STATUS' => array(
        'PENDING' => 0,      // 待处理
        'REJECTED' => 10,    // 已驳回
        'WAITING' => 20,     // 等待开票
        'SUCCESS' => 30,     // 开票成功
        'FAILED' => 40,      // 开票失败
        'VOIDED' => 50,      // 已作废
    )
);

// 环境特定的配置
if ($environment == 'testing') {
    // 测试环境特定配置
    $config['LOG_LEVEL'] = 'DEBUG'; // 测试环境下记录更详细的日志
    $config['OA_API_TIMEOUT'] = 10; // 测试环境下API请求超时时间（秒）
    $config['OA_DEBUG'] = true;     // 测试环境下开启调试模式
} else {
    // 生产环境特定配置
    $config['LOG_LEVEL'] = 'ERROR'; // 生产环境下只记录错误日志
    $config['OA_API_TIMEOUT'] = 30; // 生产环境下API请求超时时间（秒）
    $config['OA_DEBUG'] = false;    // 生产环境下关闭调试模式
}

return $config;
