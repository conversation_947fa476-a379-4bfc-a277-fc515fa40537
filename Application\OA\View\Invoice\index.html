<extend name="Mpanel@Base/admin_base" />
<block name="title">
    <title>发票管理 （新版） </title>
</block>

<block name="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-file-text-o"></i> 发票管理</h3>
                        <a href="{:U('Mpanel/Invoice/index')}" class="pull-right"
                            style="margin-top: -18px;">旧版发票管理点击这里</a>
                    </div>
                    <div class="panel-body">
                        <!-- 搜索表单 -->
                        <form method="get" action="{:U('OA/Invoice/index')}" class="search-form mb-4"
                            style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; border: 1px solid #e0e0e0;">
                            <div class="row mb-4">
                                <div class="col-md-3 mb-3">
                                    <label for="search" class="form-label">发票抬头/OA订单号</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                        placeholder="发票抬头/OA流水号" value="{$search}">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-control" id="status" name="status">
                                        <foreach name="statusList" item="text" key="value">
                                            <option value="{$value}" <if condition="$status eq $value">selected</if>
                                                >{$text}</option>
                                        </foreach>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="startTime" class="form-label">开始日期</label>
                                    <input type="date" class="form-control" id="startTime" name="startTime"
                                        placeholder="开始日期" value="{$startTime}">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="endTime" class="form-label">结束日期</label>
                                    <input type="date" class="form-control" id="endTime" name="endTime"
                                        placeholder="结束日期" value="{$endTime}">
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-3 mb-3">
                                    <label for="event" class="form-label">会议简称</label>
                                    <input type="text" class="form-control" id="event" name="event" placeholder="会议简称"
                                        value="{$event}">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="paper_id" class="form-label">文章编号</label>
                                    <input type="text" class="form-control" id="paper_id" name="paper_id"
                                        placeholder="文章编号" value="{$paper_id}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fa fa-search"></i> 搜索
                                        </button>
                                        <a href="{:U('OA/Invoice/index')}" class="btn btn-default">
                                            <i class="fa fa-refresh"></i> 重置
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <!-- 数据表格 -->
                        <div class="table-responsive" style="margin-top: 15px;">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th width="16%">发票抬头</th>
                                        <th width="9%" class="text-center">金额</th>
                                        <th width="10%">发票状态</th>
                                        <th width="10%">查账状态</th>
                                        <th width="10%">支付途径</th>
                                        <th width="10%">会议简称</th>
                                        <th width="10%">文章编号</th>
                                        <th width="10%">申请时间</th>
                                        <th width="5%">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <notempty name="list">
                                        <foreach name="list" item="item">
                                            <tr <if condition="$item.status eq 0">class="pending-row" style="background-color: #fff8e1 !important;"</if>>
                                                <td> <span class="label label-default">{$item.invoice_type_text}</span>
                                                    <a
                                                        href="{:U('OA/Invoice/detail', array('id' => $item['id']))}">{$item.invoice_title}</a>
                                                    <if condition="$item.status eq 0">
                                                        <span class="new-badge">New</span>
                                                    </if>
                                                </td>
                                                <td class="text-center">
                                                    <span class="amount-highlight">
                                                        ¥{$item.formatted_amount}
                                                    </span>
                                                </td>

                                                <td class="cell-status">
                                                    <span class="status-label invoice-status-label label-{$item.status_class}">
                                                        {$item.status_text}
                                                    </span>
                                                </td>
                                                <td class="cell-status">
                                                    <span class="status-label audit-status-label label-{$item.audit_status_class}">
                                                        {$item.audit_status_text}
                                                    </span>
                                                </td>
                                                <td class="cell-info">
                                                    <span>
                                                        {$item.pay_type_text}
                                                    </span>
                                                </td>
                                                <td class="cell-info">
                                                    <a href="{:U('OA/Invoice/detail', array('id' => $item['id']))}" title="查看详情" class="event-link">
                                                        {$item.event|default="未知"}
                                                    </a>
                                                </td>
                                                <td class="cell-info">{$item.paper_id|default="无"}</td>
                                                <td class="cell-meta">{$item.create_time_text}</td>
                                                <td class="cell-meta">
                                                    <div class="btn-group btn-group-xs">
                                                        <if condition="$item.status eq 0">
                                                            <a href="{:U('OA/Invoice/detail', array('id' => $item['id']))}"
                                                                class="btn btn-warning" title="待审核，请尽快处理">
                                                                <i class="fa fa-exclamation-circle"></i> 审核
                                                            </a>
                                                        <else />
                                                            <a href="{:U('OA/Invoice/detail', array('id' => $item['id']))}"
                                                                class="btn btn-info" title="查看详情">
                                                                <i class="fa fa-eye"></i> 查看详情
                                                            </a>
                                                        </if>
                                                    </div>
                                                </td>
                                            </tr>
                                        </foreach>
                                        <else />
                                        <tr>
                                            <td colspan="9" class="text-center">暂无数据</td>
                                        </tr>
                                    </notempty>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="text-center">
                            {$page}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 样式 -->
    <style>
        /* 金额高亮样式 */
        .amount-highlight {
            font-family: 'Roboto Mono', monospace, Arial, sans-serif;
            font-weight: 600;
            color: #e53935;
            font-size: 1.1em;
            white-space: nowrap;
        }

        /* 搜索表单样式 */
        .search-form {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .search-form .form-control {
            height: 38px;
            border-radius: 4px;
            border: 1px solid #ddd;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .search-form .form-control:focus {
            border-color: #4e91cd;
            box-shadow: 0 0 0 0.2rem rgba(78, 145, 205, 0.25);
        }

        .search-form .form-label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #555;
        }

        .search-form .btn {
            height: 38px;
            margin-right: 10px;
            border-radius: 4px;
        }

        /* 表格样式增强 */
        .invoice-table {
            border-collapse: collapse;
            width: 100%;
            table-layout: fixed;
            /* 固定表格布局 */
            border: 1px solid #ddd;
        }

        .invoice-table>thead>tr>th {
            background-color: #f5f7fa;
            border-bottom: 2px solid #e4e7ed;
            border-color: #ddd;
            /* 灰色间隔线 */
            font-weight: 600;
            color: #606266;
            padding: 12px 8px;
            vertical-align: middle;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 表格分组样式 */
        .invoice-table>thead>tr>th.group-invoice {
            border-bottom: 2px solid #3498db;
            color: #2980b9;
        }

        .invoice-table>thead>tr>th.group-status {
            border-bottom: 2px solid #2ecc71;
            color: #27ae60;
        }

        .invoice-table>thead>tr>th.group-info {
            border-bottom: 2px solid #f39c12;
            color: #d35400;
        }

        .invoice-table>thead>tr>th.group-meta {
            border-bottom: 2px solid #95a5a6;
            color: #7f8c8d;
        }

        /* 表格行悬停效果 */
        .invoice-table>tbody>tr:hover {
            background-color: #f0f7ff;
        }

        /* 待审核行高亮 */
        table tr.pending-row,
        table tbody tr.pending-row,
        .table tr.pending-row,
        .table tbody tr.pending-row,
        tr.pending-row {
            background-color: #fff8e1 !important;
            /* 浅黄色背景 */
            transition: background-color 0.3s ease;
        }

        table tr.pending-row:hover,
        table tbody tr.pending-row:hover,
        .table tr.pending-row:hover,
        .table tbody tr.pending-row:hover,
        tr.pending-row:hover {
            background-color: #ffe0b2 !important;
            /* 鼠标悬停时加深背景色 */
        }

        /* NEW 标记样式 */
        .new-badge {
            display: inline-block;
            margin-left: 5px;
            padding: 2px 5px;
            font-size: 10px;
            font-weight: bold;
            color: #fff;
            background-color: #e74c3c;
            border-radius: 3px;
            animation: blink 1s infinite alternate;
            vertical-align: middle;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        /* 闪烁动画 */
        @keyframes blink {
            from { opacity: 1; }
            to { opacity: 0.7; }
        }

        /* 会议名称链接样式 */
        .event-link {
            color: #2980b9;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
            display: inline-block;
        }

        .event-link:hover {
            color: #3498db;
            text-decoration: underline;
        }

        /* 表格单元格内边距 */
        .invoice-table>tbody>tr>td {
            padding: 10px 8px;
            vertical-align: middle;
            border-color: #ddd;
            /* 灰色间隔线 */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 去除条纹效果 */
        .invoice-table>tbody>tr:nth-of-type(odd) {
            background-color: #fff;
        }

        /* 确保表格内容不会溢出 */
        .table-responsive {
            overflow-x: auto;
        }

        /* 单元格分组样式 */
        .cell-invoice {
            background-color: rgba(52, 152, 219, 0.03);
            border-left: 1px solid #ddd;
            border-right: 1px solid #ddd;
        }

        .cell-status {
            background-color: rgba(46, 204, 113, 0.03);
            border-left: 1px solid #ddd;
            border-right: 1px solid #ddd;
        }

        .cell-info {
            background-color: rgba(243, 156, 18, 0.03);
            border-left: 1px solid #ddd;
            border-right: 1px solid #ddd;
        }

        /* 鼠标悬停时增强单元格分组效果 */
        .invoice-table>tbody>tr:hover .cell-invoice {
            background-color: rgba(52, 152, 219, 0.08);
        }

        .invoice-table>tbody>tr:hover .cell-status {
            background-color: rgba(46, 204, 113, 0.08);
        }

        .invoice-table>tbody>tr:hover .cell-info {
            background-color: rgba(243, 156, 18, 0.08);
        }

        /* 相关发票分组样式 */
        /* OA流水号分组 */
        tr.oa-group {
            background-color: rgba(52, 152, 219, 0.03);
        }

        tr.oa-group-first {
            border-top: 2px solid #3498db;
        }

        tr.oa-group-last {
            border-bottom: 2px solid #3498db;
        }

        tr.oa-group:hover {
            background-color: rgba(52, 152, 219, 0.08) !important;
        }

        /* 注册ID分组 */
        tr.reg-group {
            background-color: rgba(46, 204, 113, 0.03);
        }

        tr.reg-group-first {
            border-top: 2px solid #2ecc71;
        }

        tr.reg-group-last {
            border-bottom: 2px solid #2ecc71;
        }

        tr.reg-group:hover {
            background-color: rgba(46, 204, 113, 0.08) !important;
        }

        /* 支付ID分组 */
        tr.pay-group {
            background-color: rgba(243, 156, 18, 0.03);
        }

        tr.pay-group-first {
            border-top: 2px solid #f39c12;
        }

        tr.pay-group-last {
            border-bottom: 2px solid #f39c12;
        }

        tr.pay-group:hover {
            background-color: rgba(243, 156, 18, 0.08) !important;
        }

        /* 确保所有表格单元格的边框都是灰色 */
        .invoice-table>tbody>tr>td,
        .invoice-table>thead>tr>th {
            border: 1px solid #ddd;
        }

        /* 分组标记 */
        tr.oa-group-first::before,
        tr.reg-group-first::before,
        tr.pay-group-first::before {
            content: '';
            position: absolute;
            left: 0;
            width: 4px;
            height: 100%;
        }

        tr.oa-group-first::before {
            background-color: #3498db;
        }

        tr.reg-group-first::before {
            background-color: #2ecc71;
        }

        tr.pay-group-first::before {
            background-color: #f39c12;
        }

        /* 分组指示器样式 */
        .group-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            border-radius: 50%;
            color: white;
            font-weight: bold;
            font-size: 12px;
            margin-right: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            cursor: help;
        }

        .oa-indicator {
            background-color: #3498db;
        }

        .reg-indicator {
            background-color: #2ecc71;
        }

        .pay-indicator {
            background-color: #f39c12;
        }

        /* 表格行相对定位 */
        .position-relative {
            position: relative;
        }

        /* 状态标签简化样式 */
        .status-label {
            display: inline-block;
            padding: 3px 7px;
            font-size: 12px;
            font-weight: normal;
            line-height: 1.4;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 2px;
            color: #fff;
            background-color: #95a5a6;
        }

        /* 发票类型标签 */
        .invoice-type-label {
            border-radius: 2px;
            padding: 3px 7px;
            background-color: #f8f9fa;
            color: #495057;
            border: 1px solid #ddd;
        }

        /* 支付途径标签 */
        .pay-type-label {
            padding: 3px 7px;
            background-color: #f8f9fa;
            color: #495057;
            border: 1px solid #ddd;
        }

        /* 发票状态标签 - 深色背景白色文字 */
        .invoice-status-label {
            border-radius: 2px;
            padding: 3px 8px;
            background-color: #495057;
            color: #ffffff;
            font-weight: normal;
            box-shadow: none;
        }

        /* 查账状态标签 - 底部边框样式 */
        .audit-status-label {
            border-radius: 0;
            padding: 3px 7px;
            border-bottom: 2px solid rgba(0, 0, 0, 0.2);
            background-color: #f8f9fa;
            color: #495057;
            font-weight: normal;
        }

        /* 发票状态标签颜色 - 深色背景白色文字 */
        .invoice-status-label.label-primary {
            background-color: #3498db;
            color: #ffffff;
        }

        .invoice-status-label.label-success {
            background-color: #2ecc71;
            color: #ffffff;
        }

        .invoice-status-label.label-info {
            background-color: #1abc9c;
            color: #ffffff;
        }

        .invoice-status-label.label-warning {
            background-color: #f39c12;
            color: #ffffff;
        }

        .invoice-status-label.label-danger {
            background-color: #e74c3c;
            color: #ffffff;
        }

        .invoice-status-label.label-default {
            background-color: #95a5a6;
            color: #ffffff;
        }

        /* 查账状态标签颜色 - 浅色背景深色文字 */
        .audit-status-label.label-primary {
            color: #2573a7;
            background-color: #f0f7ff;
            border-color: #b8daff;
            border-bottom-color: #3498db;
        }

        .audit-status-label.label-success {
            color: #155724;
            background-color: #f4fff8;
            border-color: #c3e6cb;
            border-bottom-color: #2ecc71;
        }

        .audit-status-label.label-info {
            color: #0c5460;
            background-color: #f0fffc;
            border-color: #bee5eb;
            border-bottom-color: #1abc9c;
        }

        .audit-status-label.label-warning {
            color: #856404;
            background-color: #fff9e6;
            border-color: #ffeeba;
            border-bottom-color: #f39c12;
        }

        .audit-status-label.label-danger {
            color: #721c24;
            background-color: #fff5f5;
            border-color: #f5c6cb;
            border-bottom-color: #e74c3c;
        }

        .audit-status-label.label-default {
            color: #555;
            background-color: #f8f9fa;
            border-color: #ddd;
            border-bottom-color: #95a5a6;
        }
    </style>

    <!-- 使用HTML5原生日期选择器和表格分组功能 -->
    <script>
        $(function () {
            // 确保日期格式符合HTML5 date输入类型的要求（YYYY-MM-DD）
            function formatDateForInput(dateString) {
                if (!dateString) return '';

                // 如果已经是正确的格式，直接返回
                if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) return dateString;

                // 尝试将字符串转换为日期对象
                var date = new Date(dateString);
                if (isNaN(date.getTime())) return ''; // 无效日期

                // 格式化为 YYYY-MM-DD
                var year = date.getFullYear();
                var month = (date.getMonth() + 1).toString().padStart(2, '0');
                var day = date.getDate().toString().padStart(2, '0');
                return year + '-' + month + '-' + day;
            }

            // 处理开始日期和结束日期输入框
            var startTimeInput = $('input[name="startTime"]');
            var endTimeInput = $('input[name="endTime"]');

            startTimeInput.val(formatDateForInput(startTimeInput.val()));
            endTimeInput.val(formatDateForInput(endTimeInput.val()));

            // 表格分组功能 - 根据OA流水号、注册ID或支付ID分组
            function groupRelatedInvoices() {
                var rows = $('.table tbody tr');
                var groupedByOaId = {};
                var groupedByRegId = {};
                var groupedByPayId = {};

                // 第一步：收集所有行的分组信息
                rows.each(function () {
                    var $row = $(this);
                    var oaId = $row.data('oa-id');
                    var regId = $row.data('reg-id');
                    var payId = $row.data('pay-id');

                    // 按OA流水号分组
                    if (oaId) {
                        if (!groupedByOaId[oaId]) {
                            groupedByOaId[oaId] = [];
                        }
                        groupedByOaId[oaId].push($row);
                    }

                    // 按注册ID分组
                    if (regId) {
                        if (!groupedByRegId[regId]) {
                            groupedByRegId[regId] = [];
                        }
                        groupedByRegId[regId].push($row);
                    }

                    // 按支付ID分组
                    if (payId) {
                        if (!groupedByPayId[payId]) {
                            groupedByPayId[payId] = [];
                        }
                        groupedByPayId[payId].push($row);
                    }
                });

                // 第二步：应用分组样式
                // 优先级：OA流水号 > 注册ID > 支付ID

                // 应用OA流水号分组
                applyGroupStyles(groupedByOaId, 'oa-group');

                // 如果没有OA流水号，则应用注册ID分组
                applyGroupStyles(groupedByRegId, 'reg-group', function ($row) {
                    return !$row.hasClass('oa-group');
                });

                // 如果没有OA流水号和注册ID，则应用支付ID分组
                applyGroupStyles(groupedByPayId, 'pay-group', function ($row) {
                    return !$row.hasClass('oa-group') && !$row.hasClass('reg-group');
                });
            }

            // 应用分组样式
            function applyGroupStyles(groups, className, filterFn) {
                $.each(groups, function (id, rows) {
                    // 只对多于1行的分组应用样式
                    if (rows.length > 1) {
                        $.each(rows, function (index, $row) {
                            // 如果有过滤函数，则应用过滤
                            if (!filterFn || filterFn($row)) {
                                $row.addClass(className);

                                // 第一行添加顶部边框
                                if (index === 0) {
                                    $row.addClass(className + '-first');
                                }

                                // 最后一行添加底部边框
                                if (index === rows.length - 1) {
                                    $row.addClass(className + '-last');
                                }

                                // 中间行
                                if (index > 0 && index < rows.length - 1) {
                                    $row.addClass(className + '-middle');
                                }
                            }
                        });
                    }
                });
            }

            // 执行分组
            groupRelatedInvoices();

            // 添加分组指示器和工具提示
            function addGroupIndicators() {
                // OA流水号分组
                $('.oa-group-first').each(function () {
                    var $row = $(this);
                    var oaId = $row.data('oa-id');
                    var groupSize = $('.oa-group[data-oa-id="' + oaId + '"]').length;

                    // 添加工具提示
                    $row.attr('title', 'OA订单号: ' + oaId + ' (共' + groupSize + '张发票)');

                    // 添加分组指示器
                    $row.find('td:first').prepend('<span class="group-indicator oa-indicator" data-toggle="tooltip" title="OA流水号: ' + oaId + ' (共' + groupSize + '张发票)">' + groupSize + '</span>');
                });

                // 注册ID分组
                $('.reg-group-first:not(.oa-group)').each(function () {
                    var $row = $(this);
                    var regId = $row.data('reg-id');
                    var groupSize = $('.reg-group[data-reg-id="' + regId + '"]').length;

                    // 添加工具提示
                    $row.attr('title', '注册ID: ' + regId + ' (共' + groupSize + '张发票)');

                    // 添加分组指示器
                    $row.find('td:first').prepend('<span class="group-indicator reg-indicator" data-toggle="tooltip" title="注册ID: ' + regId + ' (共' + groupSize + '张发票)">' + groupSize + '</span>');
                });

                // 支付ID分组
                $('.pay-group-first:not(.oa-group):not(.reg-group)').each(function () {
                    var $row = $(this);
                    var payId = $row.data('pay-id');
                    var groupSize = $('.pay-group[data-pay-id="' + payId + '"]').length;

                    // 添加工具提示
                    $row.attr('title', '支付ID: ' + payId + ' (共' + groupSize + '张发票)');

                    // 添加分组指示器
                    $row.find('td:first').prepend('<span class="group-indicator pay-indicator" data-toggle="tooltip" title="支付ID: ' + payId + ' (共' + groupSize + '张发票)">' + groupSize + '</span>');
                });

                // 初始化工具提示
                $('[data-toggle="tooltip"]').tooltip();
            }

            // 执行添加分组指示器
            addGroupIndicators();

            // 强制应用高亮样式
            setTimeout(function() {
                // 查找所有待审核行并应用高亮样式
                $('table tbody tr').each(function() {
                    var statusText = $(this).find('td:nth-child(3) .label').text().trim();
                    if (statusText === '待审核') {
                        $(this).addClass('pending-row').css('background-color', '#fff8e1');
                        console.log('应用高亮样式到行:', $(this).find('td:first').text());
                    }
                });
            }, 100);
        });
    </script>
</block>