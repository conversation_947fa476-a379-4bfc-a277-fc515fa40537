<?php

namespace Common\Lib;

use Common\Lib\CurrencyTypeConstants;
use Common\Lib\TransferInvoiceStrategyConstants;

/**
 * 发票申请资格配置类
 * 用于定义和管理发票申请资格的配置参数
 */
class InvoiceEligibilityConfig
{
    /**
     * 允许申请发票的商户号列表
     * 如果为空数组，表示允许所有商户号
     */
    const ALLOWED_MERCHANT_IDS = [
        // 在这里添加允许申请发票的商户号
        // '897438777',  // 示例商户号
        // 'AWX',        // AWX支付
    ];

    /**
     * 线下转账发票申请策略
     * 使用 TransferInvoiceStrategyConstants 中定义的常量
     * ONLY_AFTER_AUDIT_SUCCESS: 仅允许审核成功后申请发票
     * ALLOW_AFTER_SUBMISSION: 允许提交后即可申请发票
     * ALLOW_ANY_STATUS: 允许任何状态下申请发票（不推荐）
     */
    const TRANSFER_INVOICE_STRATEGY = TransferInvoiceStrategyConstants::ALLOW_ANY_STATUS;
    /**
     * 在线支付订单发票申请时间范围配置
     * 格式：['start_date' => '起始日期', 'end_date' => '结束日期']
     * 日期格式：'Y-m-d'，如 '2023-01-01'
     * 如果 start_date 为 null，表示不限制起始日期
     * 如果 end_date 为 null，表示不限制结束日期
     */
    const ONLINE_PAYMENT_DATE_RANGE = [
        'start_date' => '2023-01-01', // 2020年1月1日之后的订单可以申请发票
        'end_date' => null,           // 不限制结束日期
    ];

    /**
     * 线下转账发票申请时间范围配置
     * 格式：['start_date' => '起始日期', 'end_date' => '结束日期']
     * 日期格式：'Y-m-d'，如 '2023-01-01'
     * 如果 start_date 为 null，表示不限制起始日期
     * 如果 end_date 为 null，表示不限制结束日期
     */
    const OFFLINE_TRANSFER_DATE_RANGE = [
        'start_date' => '2020-01-01', // 2020年1月1日之后的转账可以申请发票
        'end_date' => null,           // 不限制结束日期
    ];

    /**
     * 发票申请金额限制
     * 是否允许部分金额开票
     */
    const ALLOW_PARTIAL_INVOICE = true;

    /**
     * 发票申请次数限制
     * 同一订单/转账最多可以申请的发票次数
     * 如果设置为 null，表示不限制次数
     */
    const MAX_INVOICE_TIMES = null;

    /**
     * 发票申请货币类型限制
     * 允许申请发票的货币类型
     * 使用 CurrencyTypeConstants 中定义的常量
     */
    const ALLOWED_CURRENCIES = [CurrencyTypeConstants::CURRENCY_CNY]; // 只允许人民币支付申请发票

    /**
     * 获取在线支付订单发票申请时间范围
     * @return array 时间范围配置
     */
    public static function getOnlinePaymentDateRange()
    {
        return self::ONLINE_PAYMENT_DATE_RANGE;
    }

    /**
     * 获取线下转账发票申请时间范围
     * @return array 时间范围配置
     */
    public static function getOfflineTransferDateRange()
    {
        return self::OFFLINE_TRANSFER_DATE_RANGE;
    }

    /**
     * 检查是否允许部分金额开票
     * @return bool 是否允许部分金额开票
     */
    public static function isPartialInvoiceAllowed()
    {
        return self::ALLOW_PARTIAL_INVOICE;
    }

    /**
     * 获取最大发票申请次数
     * @return int|null 最大发票申请次数，null表示不限制
     */
    public static function getMaxInvoiceTimes()
    {
        return self::MAX_INVOICE_TIMES;
    }

    /**
     * 获取允许申请发票的货币类型
     * @return array 允许申请发票的货币类型
     */
    public static function getAllowedCurrencies()
    {
        return self::ALLOWED_CURRENCIES;
    }

    /**
     * 检查货币类型是否允许申请发票
     * @param int $currency 货币类型 (0-CNY, 1-USD)
     * @return bool 是否允许申请发票
     */
    public static function isCurrencyAllowed($currency)
    {
        return in_array($currency, self::ALLOWED_CURRENCIES);
    }

    /**
     * 获取允许申请发票的商户号列表
     * @return array 允许申请发票的商户号列表
     */
    public static function getAllowedMerchantIds()
    {
        return self::ALLOWED_MERCHANT_IDS;
    }

    /**
     * 检查商户号是否允许申请发票
     * @param string $merchantId 商户号
     * @return bool 是否允许申请发票
     */
    public static function isMerchantAllowed($merchantId)
    {
        // 如果允许的商户号列表为空，表示允许所有商户号
        if (empty(self::ALLOWED_MERCHANT_IDS)) {
            return true;
        }

        return in_array($merchantId, self::ALLOWED_MERCHANT_IDS);
    }

    /**
     * 获取线下转账发票申请策略
     * @return int 策略常量
     */
    public static function getTransferInvoiceStrategy()
    {
        return self::TRANSFER_INVOICE_STRATEGY;
    }
}
