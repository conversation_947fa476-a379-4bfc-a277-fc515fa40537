<extend name="<PERSON><PERSON><PERSON>@Base/admin_base" />
<block name="title">线上支付查账</block>

<block name="head">
    <style>
        /* 自定义样式 */
        .card-header {
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
            background-color: #f5f5f5;
        }

        .card {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
        }

        .card-body {
            padding: 15px;
        }

        .amount {
            font-family: 'Arial', sans-serif;
            color: #d9534f;
            font-weight: bold;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            min-width: 80px;
        }

        .table-bordered>tbody>tr>th {
            background-color: #f9f9f9;
            vertical-align: middle;
        }

        .table-bordered>tbody>tr>td {
            vertical-align: middle;
        }

        .panel {
            box-shadow: 0 2px 5px rgba(0, 0, 0, .1);
            margin-bottom: 15px;
        }

        .panel-heading {
            padding: 8px 15px;
        }

        .panel-title {
            font-size: 16px;
            font-weight: bold;
        }

        .section-title {
            margin-top: 20px;
            margin-bottom: 10px;
            font-weight: bold;
            color: #337ab7;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }

        .form-section {
            background-color: #f9f9f9;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #5bc0de;
        }
    </style>
    <script src="/static/js/<EMAIL>"></script>
</block>

<block name="content">
    <div class="container-fluid">
        <!-- 查账状态通栏 -->
        <notempty name="hasAuditRecord">
            <div class="alert {$auditRecord.status_alert_class|default='alert-info'}">
                <div class="row">
                    <div class="col-md-8">
                        <h4><i class="fa fa-info-circle"></i>
                            查账状态：{$auditRecord.audit_status_label|default=$auditRecord.status_label}</h4>
                        <p><strong>查账类型：</strong>{$auditRecord.audit_type_label|default='线上支付'} |
                            <strong>提交时间：</strong>{$auditRecord.create_time_text}</p>
                        <notempty name="auditRecord.update_time">
                            <p><strong>更新时间：</strong>{$auditRecord.update_time_text}</p>
                        </notempty>
                        <notempty name="auditRecord.oa_id">
                            <p><strong>OA订单号：</strong>{$auditRecord.oa_id}</p>
                        </notempty>
                    </div>
                    <div class="col-md-4 text-right">
                        <notempty name="auditRecord.can_apply_invoice">
                            <a href="{:U('OA/Invoice/onlineInvoice', array('oa_id' => $auditRecord.oa_id))}"
                                class="btn btn-success">
                                <i class="fa fa-file-text"></i> 申请发票
                            </a>
                        </notempty>
                    </div>
                </div>
            </div>
        </notempty>

        <div class="row">
            <!-- 左侧查账申请表单 (9列) -->
            <div class="col-md-9">
                <div class="page-header">
                    <h3><i class="fa fa-paper-plane"></i> 线上支付查账申请</h3>
                </div>
                        <empty name="hasAuditRecord">
                            <!-- 不存在查账记录，显示提交表单 -->
                            <form id="auditForm" class="form-horizontal">
                                <!-- 会议信息区域 -->
                                <h4 class="section-title"><i class="fa fa-calendar-check-o"></i> 会议与文章信息 <small class="text-success">（可编辑）</small></h4>
                                <div class="form-section" style="border-left: 4px solid #5cb85c;">
                                    <div class="form-group">
                                        <label for="confName" class="col-sm-3 control-label">会议名称</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="confName" name="confName"
                                                required value="{$registerInfo['event']}">
                                            <p class="help-block"><i class="fa fa-info-circle"></i> 请输入完整的会议名称，例如：ICASSP2023</p>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="paperCode" class="col-sm-3 control-label">文章ID</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="paperCode" name="paperCode"
                                                required value="{$registerInfo['paperid']}">
                                            <p class="help-block"><i class="fa fa-info-circle"></i> 请输入正确的文章ID，多个ID请用英文逗号分隔</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 支付信息区域 - 只读 -->
                                <h4 class="section-title"><i class="fa fa-credit-card"></i> 支付信息 <small class="text-muted">（只读）</small></h4>
                                <div class="form-section" style="border-left: 4px solid #aaa; background-color: #f9f9f9;">
                                    <div class="form-group">
                                        <label for="creditSerial" class="col-sm-3 control-label">汇款订单号</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="creditSerial"
                                                name="creditSerial" required readonly
                                                value="{$registerInfo['payments']['orderid'] }">
                                            <p class="help-block"><i class="fa fa-lock"></i> 订单号不可修改</p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label for="amount" class="col-sm-6 control-label">金额</label>
                                                <div class="col-sm-6">
                                                    <input type="text" class="form-control" id="amount" name="amount"
                                                        required readonly value="{$registerInfo['payments']['total'] }">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label for="unit" class="col-sm-4 control-label">单位</label>
                                                <div class="col-sm-8">
                                                    <select class="form-control" id="unit_display" disabled>
                                                        <option value="CNY">CNY</option>
                                                        <option value="USD">USD</option>
                                                    </select>
                                                    <eq name="registerInfo.payments.moneytype" value="0">
                                                        <script>document.getElementById('unit_display').value = 'CNY';</script>
                                                        <input type="hidden" name="unit" value="CNY">
                                                    </eq>
                                                    <eq name="registerInfo.payments.moneytype" value="1">
                                                        <script>document.getElementById('unit_display').value = 'USD';</script>
                                                        <input type="hidden" name="unit" value="USD">
                                                    </eq>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="remitTime" class="col-sm-3 control-label">汇款时间</label>
                                        <div class="col-sm-9">
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="remitTime" name="remitTime"
                                                    required readonly
                                                    value="{$registerInfo['payments']['ordetime']|date='Y-m-d',###}">
                                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                            <p class="help-block"><i class="fa fa-info-circle"></i> 支付信息来自系统记录，不可修改</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 汇款人信息区域 - 只读 -->
                                <h4 class="section-title"><i class="fa fa-user"></i> 汇款人与收款账户信息 <small class="text-muted">（只读）</small></h4>
                                <div class="form-section" style="border-left: 4px solid #aaa; background-color: #f9f9f9;">
                                    <div class="form-group">
                                        <label for="remitterInfo" class="col-sm-3 control-label">汇款人信息</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" id="remitterInfo" name="remitterInfo" readonly
                                                rows="3">汇款方姓名： {$registerInfo['firstname']} {$registerInfo['middlename']} {$registerInfo['lastname']} | 邮箱：{$registerInfo['email']}</textarea>
                                            <p class="help-block"><i class="fa fa-info-circle"></i> 汇款人信息来自注册记录，不可修改</p>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="account" class="col-sm-3 control-label">收款账户</label>
                                        <div class="col-sm-9">
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="account" name="account" required
                                                    value="{$oaDict|default='cs'}" readonly>
                                                <span class="input-group-addon"><i class="fa fa-bank"></i></span>
                                            </div>
                                            <notempty name="merchantInfo">
                                                <p class="help-block">
                                                    <i class="fa fa-info-circle"></i> 根据订单号自动解析商户：<strong>{$merchantInfo.name}</strong>
                                                </p>
                                            </notempty>
                                            <empty name="merchantInfo">
                                                <p class="help-block">
                                                    <i class="fa fa-warning text-warning"></i> 未能从订单号解析商户信息，使用默认账户
                                                </p>
                                            </empty>
                                        </div>
                                    </div>
                                </div>

                                <!-- 附件与备注区域 -->
                                <h4 class="section-title"><i class="fa fa-paperclip"></i> 附件与备注 <small class="text-success">（备注可编辑）</small></h4>
                                <div class="form-section" style="border-left: 4px solid #5bc0de;">
                                    <div class="form-group">
                                        <label for="annex" class="col-sm-3 control-label">附件</label>
                                        <div class="col-sm-9">
                                            <notempty name="proof.pic">
                                                <a href="{$proof['pic']}" target="_blank" class="btn btn-info">
                                                    <i class="fa fa-paperclip"></i> 查看支付凭证
                                                </a>
                                                <input type="hidden" name="annex" value="{$proof['pic']}">
                                                <p class="help-block"><i class="fa fa-info-circle"></i> 点击按钮查看附件</p>
                                            </notempty>
                                            <empty name="proof.pic">
                                                <p class="text-muted"><i class="fa fa-info-circle"></i> 没有附件</p>
                                                <input type="hidden" name="annex" value="">
                                            </empty>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="remark" class="col-sm-3 control-label">备注信息</label>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" id="remark" name="remark" rows="3"
                                                placeholder="请输入需要说明的其他信息"></textarea>
                                            <p class="help-block"><i class="fa fa-edit"></i> 可以在这里添加备注信息</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 提交按钮 -->
                                <div class="form-group">
                                    <div class="col-sm-offset-3 col-sm-9">
                                        <div class="alert alert-info" style="margin-bottom: 15px;">
                                            <i class="fa fa-info-circle"></i> <strong>注意：</strong> 只有<span class="text-success">会议名称</span>、<span class="text-success">文章ID</span>和<span class="text-success">备注信息</span>可以修改，其他信息为只读状态。
                                        </div>
                                        <button type="button" class="btn btn-success btn-lg" id="submitAuditBtn">
                                            <i class="fa fa-check-square-o"></i> 提交查账申请
                                        </button>
                                        <a href="javascript:history.back();" class="btn btn-default btn-sm pull-right">
                                            <i class="fa fa-arrow-left"></i> 返回
                                        </a>
                                        <p class="help-block text-center">提交后将发送到OA系统进行处理</p>
                                    </div>
                                </div>

                                <!-- 隐藏域 -->
                                <input type="hidden" name="reg_id" value="{$registerInfo['id']}">
                                <input type="hidden" name="pay_id" value="{$registerInfo['payments']['id']}">
                            </form>

                            <script>
                                document.addEventListener('DOMContentLoaded', function() {
                                    // 获取表单和提交按钮
                                    const form = document.getElementById('auditForm');
                                    const submitBtn = document.getElementById('submitAuditBtn');

                                    // 添加提交按钮点击事件
                                    submitBtn.addEventListener('click', function(e) {
                                        e.preventDefault();

                                        // 表单验证
                                        if (!validateForm()) {
                                            return false;
                                        }

                                        // 显示确认对话框
                                        Swal.fire({
                                            title: '确认提交',
                                            html: `<div class="text-left">
                                                <p><strong>您将提交以下信息：</strong></p>
                                                <ul>
                                                    <li><strong>会议名称：</strong>${document.getElementById('confName').value}</li>
                                                    <li><strong>文章ID：</strong>${document.getElementById('paperCode').value}</li>
                                                    <li><strong>备注信息：</strong>${document.getElementById('remark').value ? document.getElementById('remark').value : '无'}</li>
                                                </ul>
                                                <p>其他信息将保持不变。是否确认提交？</p>
                                            </div>`,
                                            icon: 'question',
                                            showCancelButton: true,
                                            confirmButtonText: '确认提交',
                                            confirmButtonColor: '#28a745',
                                            cancelButtonText: '取消'
                                        }).then((result) => {
                                            if (result.isConfirmed) {
                                                submitFormAjax();
                                            }
                                        });
                                    });

                                    // 表单验证函数
                                    function validateForm() {
                                        // 只验证可编辑字段：会议名称、文章ID和备注
                                        const confName = document.getElementById('confName').value.trim();
                                        const paperCode = document.getElementById('paperCode').value.trim();
                                        const remark = document.getElementById('remark').value.trim();

                                        // 验证会议名称
                                        if (!confName) {
                                            Swal.fire('验证失败', '请填写会议名称', 'error');
                                            document.getElementById('confName').focus();
                                            return false;
                                        }

                                        // 验证会议名称格式
                                        const confNameRegex = /^[A-Za-z0-9-]+[0-9]{4}(?:,[A-Za-z0-9-]+[0-9]{4})*$/;
                                        if (!confNameRegex.test(confName)) {
                                            Swal.fire('验证失败', '会议名称格式不正确，应为英文字母数字加年份，如ICASSP2023', 'error');
                                            document.getElementById('confName').focus();
                                            return false;
                                        }

                                        // 验证文章ID
                                        if (!paperCode) {
                                            Swal.fire('验证失败', '请填写文章ID', 'error');
                                            document.getElementById('paperCode').focus();
                                            return false;
                                        }

                                        // 验证文章ID格式
                                        const paperCodeRegex = /^[A-Za-z0-9-]+(?:,[A-Za-z0-9-]+)*$/;
                                        if (!paperCodeRegex.test(paperCode)) {
                                            Swal.fire('验证失败', '文章ID格式不正确，多个ID请用英文逗号分隔', 'error');
                                            document.getElementById('paperCode').focus();
                                            return false;
                                        }

                                        // 备注信息不是必填项，但如果超过500字符则提示
                                        if (remark.length > 500) {
                                            Swal.fire('验证失败', '备注信息过长，请控制在500字符以内', 'error');
                                            document.getElementById('remark').focus();
                                            return false;
                                        }

                                        return true;
                                    }

                                    // AJAX提交表单
                                    function submitFormAjax() {
                                        // 显示加载状态
                                        Swal.fire({
                                            title: '提交中...',
                                            text: '正在将查账申请提交到OA系统',
                                            allowOutsideClick: false,
                                            didOpen: () => {
                                                Swal.showLoading();
                                            }
                                        });

                                        // 创建FormData对象
                                        const formData = new FormData(form);

                                        // 发送AJAX请求
                                        fetch("{:U('AddOnlineAudit')}", {
                                            method: 'POST',
                                            body: formData,
                                            headers: {
                                                'X-Requested-With': 'XMLHttpRequest'
                                            }
                                        })
                                        .then(response => response.json())
                                        .then(data => {
                                            // 处理响应
                                            if (data.status) {
                                                // 成功
                                                Swal.fire({
                                                    title: '提交成功',
                                                    text: data.message || '查账申请已成功提交到OA系统',
                                                    icon: 'success',
                                                    confirmButtonText: '确定'
                                                }).then(() => {
                                                    // 刷新页面
                                                    window.location.reload();
                                                });
                                            } else {
                                                // 失败
                                                Swal.fire({
                                                    title: '提交失败',
                                                    text: data.message || '查账申请提交失败',
                                                    icon: 'error',
                                                    confirmButtonText: '确定'
                                                });
                                            }
                                        })
                                        .catch(error => {
                                            // 处理错误
                                            console.error('Error:', error);
                                            Swal.fire({
                                                title: '系统错误',
                                                text: '提交过程中发生错误，请稍后重试',
                                                icon: 'error',
                                                confirmButtonText: '确定'
                                            });
                                        });
                                    }
                                });
                            </script>
                        </empty>
                        <notempty name="hasAuditRecord">
                            <!-- 已存在查账记录，显示查账记录信息 -->
                            <div class="panel panel-{$auditRecord.status_panel_class|default='info'}">
                                <div class="panel-heading">
                                    <h3 class="panel-title"><i class="fa fa-exchange"></i> 查账信息</h3>
                                </div>
                                <div class="panel-body">
                                    <table class="table table-bordered table-striped">
                                        <tr>
                                            <th width="30%"><i class="fa fa-hashtag"></i> 查账记录ID</th>
                                            <td>{$auditRecord.id}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fa fa-barcode"></i> OA订单号</th>
                                            <td><code>{$auditRecord.oa_id}</code></td>
                                        </tr>
                                        <tr>
                                            <th><i class="fa fa-tag"></i> 会议简称</th>
                                            <td>{$auditRecord.event}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fa fa-file-text"></i> 论文ID</th>
                                            <td>{$auditRecord.paper_id}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fa fa-money"></i> 金额</th>
                                            <td>
                                                <strong class="amount">{$auditRecord.total}</strong>
                                                <span class="label label-default">{$auditRecord.currency}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th><i class="fa fa-check-circle"></i> 处理状态</th>
                                            <td>
                                                <empty name="auditRecord.audit_status_label">
                                                    {$auditRecord.status_label}
                                                </empty>
                                                <notempty name="auditRecord.audit_status_label">
                                                    {$auditRecord.audit_status_label}
                                                </notempty>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th><i class="fa fa-list-alt"></i> 查账类型</th>
                                            <td>
                                                <empty name="auditRecord.audit_type_label">
                                                    <span class="badge badge-secondary">线上支付</span>
                                                </empty>
                                                <notempty name="auditRecord.audit_type_label">
                                                    {$auditRecord.audit_type_label}
                                                </notempty>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th><i class="fa fa-calendar"></i> 汇款时间</th>
                                            <td>{$auditRecord.remit_time}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fa fa-user"></i> 汇款人信息</th>
                                            <td>{$auditRecord.remitter_info}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fa fa-university"></i> 收款账户</th>
                                            <td>{$auditRecord.account}</td>
                                        </tr>
                                        <notempty name="auditRecord.remark">
                                            <tr>
                                                <th><i class="fa fa-comment"></i> 备注</th>
                                                <td>{$auditRecord.remark}</td>
                                            </tr>
                                        </notempty>
                                        <notempty name="auditRecord.annex">
                                            <tr>
                                                <th><i class="fa fa-paperclip"></i> 附件</th>
                                                <td>
                                                    <a href="{$auditRecord.annex}" target="_blank"
                                                        class="btn btn-xs btn-info">
                                                        <i class="fa fa-download"></i> 查看附件
                                                    </a>
                                                </td>
                                            </tr>
                                        </notempty>
                                        <tr>
                                            <th><i class="fa fa-clock-o"></i> 创建时间</th>
                                            <td>{$auditRecord.create_time_text}</td>
                                        </tr>
                                        <notempty name="auditRecord.update_time_text">
                                            <tr>
                                                <th><i class="fa fa-refresh"></i> 更新时间</th>
                                                <td>{$auditRecord.update_time_text}</td>
                                            </tr>
                                        </notempty>
                                    </table>
                                </div>
                            </div>

                            <!-- 发票信息卡片 -->
                            <div class="panel panel-danger">
                                <div class="panel-heading">
                                    <h4 class="panel-title"><i class="fa fa-file-text"></i> 关联发票信息</h4>
                                </div>
                                <div class="panel-body">
                                    <notempty name="invoices">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th><i class="fa fa-hashtag"></i> 编号</th>
                                                        <th><i class="fa fa-building"></i> 发票抬头</th>
                                                        <th><i class="fa fa-money"></i> 金额</th>
                                                        <th><i class="fa fa-list-alt"></i> 类型</th>
                                                        <th><i class="fa fa-check-circle"></i> 状态</th>
                                                        <th><i class="fa fa-cog"></i> 操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <volist name="invoices" id="invoice">
                                                        <tr>
                                                            <td>{$invoice.id}</td>
                                                            <td>{$invoice.invoice_title}</td>
                                                            <td class="amount">{$invoice.amount}</td>
                                                            <td>
                                                                <eq name="invoice.invoice_type" value="pc">
                                                                    <span class="status-badge label-primary">普通发票</span>
                                                                </eq>
                                                                <eq name="invoice.invoice_type" value="bs">
                                                                    <span class="status-badge label-warning">专用发票</span>
                                                                </eq>
                                                                <neq name="invoice.invoice_type" value="pc">
                                                                    <neq name="invoice.invoice_type" value="bs">
                                                                        <span
                                                                            class="status-badge label-default">未知类型</span>
                                                                    </neq>
                                                                </neq>
                                                            </td>
                                                            <td>
                                                                <span
                                                                    class="status-badge label-{$invoice.status_class}">{$invoice.status_text}</span>
                                                            </td>
                                                            <td>
                                                                <a href="{:U('OA/Invoice/detail', array('id' => $invoice['id']))}"
                                                                    class="btn btn-xs btn-info">
                                                                    <i class="fa fa-eye"></i> 查看
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    </volist>
                                                </tbody>
                                            </table>
                                        </div>
                                    </notempty>
                                    <empty name="invoices">
                                        <div class="alert alert-info">
                                            <i class="fa fa-info-circle"></i> 暂无关联的发票信息
                                        </div>
                                    </empty>

                                    <!-- 新增发票申请按钮 -->
                                    <php>$canApplyInvoice = ($auditRecord['status'] == 20 && empty($invoices));</php>
                                    <notempty name="canApplyInvoice">
                                        <div class="alert alert-success">
                                            <i class="fa fa-check-circle"></i> 查账已成功，您可以申请发票。
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <a href="{:U('OA/Invoice/onlineInvoice', array('oa_id' => $auditRecord['oa_id']))}"
                                                    class="btn btn-primary btn-block">
                                                    <i class="fa fa-edit"></i> 填写发票信息
                                                </a>
                                            </div>
                                            <div class="col-md-6">
                                                <a href="{:U('OA/Invoice/directSubmit', array('oa_id' => $auditRecord['oa_id']))}"
                                                    class="btn btn-success btn-block">
                                                    <i class="fa fa-check"></i> 直接提交发票
                                                </a>
                                            </div>
                                        </div>
                                        <div class="help-block text-center" style="margin-top: 10px;">
                                            <small><i class="fa fa-info-circle"></i> 直接提交将使用系统默认信息生成发票</small>
                                        </div>
                                    </notempty>
                                </div>
                            </div>
                        </notempty>
            </div>

            <!-- 右侧区域 (3列) -->
            <div class="col-md-3">
                <!-- 注册基础信息 -->
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h4 class="panel-title"><i class="fa fa-user"></i> 注册基础信息</h4>
                    </div>
                    <div class="panel-body" style="padding: 0;">
                        <table class="table table-bordered table-striped">
                            <tr>
                                <th width="40%"><i class="fa fa-flag"></i> 会议简称</th>
                                <td>{$registerInfo.event}</td>
                            </tr>
                            <tr>
                                <th><i class="fa fa-file-text-o"></i> 文章ID</th>
                                <td>{$registerInfo.paperid}</td>
                            </tr>
                            <tr>
                                <th><i class="fa fa-user"></i> 注册人</th>
                                <td>{$registerInfo.firstname} {$registerInfo.middlename} {$registerInfo.lastname}</td>
                            </tr>
                            <tr>
                                <th><i class="fa fa-envelope"></i> 邮箱</th>
                                <td>{$registerInfo.email}</td>
                            </tr>
                            <tr>
                                <th><i class="fa fa-institution"></i> 单位</th>
                                <td>
                                    <notempty name="registerInfo.affiliation">{$registerInfo.affiliation}</notempty>
                                </td>
                            </tr>
                            <tr>
                                <th><i class="fa fa-calendar"></i> 注册时间</th>
                                <td>{$registerInfo.datetime|date="Y-m-d H:i",###}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 支付信息卡片 -->
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h4 class="panel-title"><i class="fa fa-credit-card"></i> 支付详情</h4>
                    </div>
                    <div class="panel-body" style="padding: 0;">
                        <table class="table table-bordered table-striped">
                            <tr>
                                <th width="40%"><i class="fa fa-shopping-cart"></i> 支付方式</th>
                                <td>
                                    <eq name="registerInfo.pay_type" value="1">
                                        <span class="status-badge label-success">在线支付</span>
                                    </eq>
                                    <eq name="registerInfo.pay_type" value="2">
                                        <span class="status-badge label-info">转账汇款</span>
                                    </eq>
                                    <neq name="registerInfo.pay_type" value="1">
                                        <neq name="registerInfo.pay_type" value="2">
                                            <span class="status-badge label-default">未知</span>
                                        </neq>
                                    </neq>
                                </td>
                            </tr>
                            <tr>
                                <th><i class="fa fa-barcode"></i> 订单号</th>
                                <td>
                                    <code>{$registerInfo['payments']['orderid'] | default='N/A'}</code>
                                    <notempty name="merchantInfo">
                                        <span class="label label-success" title="已解析商户信息"><i class="fa fa-check"></i> 已解析</span>
                                    </notempty>
                                </td>
                            </tr>
                            <tr>
                                <th><i class="fa fa-money"></i> 支付金额</th>
                                <td>
                                    <span class="amount">{$registerInfo['payments']['total'] | default='0.00'}</span>
                                    <eq name="registerInfo.payments.moneytype" value="0">
                                        <span class="label label-default">CNY</span>
                                    </eq>
                                    <eq name="registerInfo.payments.moneytype" value="1">
                                        <span class="label label-default">USD</span>
                                    </eq>
                                </td>
                            </tr>
                            <tr>
                                <th><i class="fa fa-check-circle"></i> 支付状态</th>
                                <td>
                                    <span class="status-badge label-{$registerInfo.payment_status_class}"><i
                                            class="fa {$registerInfo.payment_status_icon}"></i>
                                        {$registerInfo.payment_status_text}</span>
                                </td>
                            </tr>
                            <tr>
                                <th><i class="fa fa-calendar"></i> 支付时间</th>
                                <td>{$registerInfo['payments']['ordetime']|date='Y-m-d H:i',###}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 商户信息卡片 -->
                <notempty name="merchantInfo">
                    <div class="panel panel-warning">
                        <div class="panel-heading">
                            <h4 class="panel-title"><i class="fa fa-building"></i> 商户信息</h4>
                        </div>
                        <div class="panel-body" style="padding: 0;">
                            <table class="table table-bordered table-striped">
                                <tr>
                                    <th width="40%"><i class="fa fa-tag"></i> 商户名称</th>
                                    <td>{$merchantInfo.name}</td>
                                </tr>
                                <tr>
                                    <th><i class="fa fa-building-o"></i> 公司名称</th>
                                    <td>{$merchantInfo.company}</td>
                                </tr>
                                <tr>
                                    <th><i class="fa fa-id-card-o"></i> 纳税人识别号</th>
                                    <td>{$merchantInfo.tax_num}</td>
                                </tr>
                                <tr>
                                    <th><i class="fa fa-key"></i> OA字典值</th>
                                    <td><code>{$merchantInfo.oa_dict}</code></td>
                                </tr>
                                <tr>
                                    <th><i class="fa fa-barcode"></i> 终端号</th>
                                    <td>{$merchantInfo.terminalNo}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </notempty>
            </div>

            <!-- 中间区域 (6列) -->
            <div class="col-md-6">
                <notempty name="hasAuditRecord">
                    <!-- 已存在查账记录，显示查账记录信息 -->
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <h4 class="panel-title"><i class="fa fa-search"></i> 查账记录信息</h4>
                        </div>
                        <div class="panel-body">
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> 该订单已经提交查账，下面是查账记录详情。
                            </div>

                            <!-- 查账基本信息 -->
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5 class="panel-title"><i class="fa fa-file-text-o"></i> 查账基本信息</h5>
                                </div>
                                <div class="panel-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <tbody>
                                                <tr>
                                                    <th style="width:30%;"><i class="fa fa-hashtag"></i> 查账记录ID</th>
                                                    <td>{$auditRecord.id}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-barcode"></i> OA订单号</th>
                                                    <td><code>{$auditRecord.oa_id}</code></td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-tag"></i> 会议简称</th>
                                                    <td>{$auditRecord.event}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-file-text"></i> 论文ID</th>
                                                    <td>{$auditRecord.paper_id}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-money"></i> 金额</th>
                                                    <td>
                                                        <strong class="text-danger">{$auditRecord.total}</strong>
                                                        <span class="label label-default">{$auditRecord.currency}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-check-circle"></i> 处理状态</th>
                                                    <td>
                                                        <empty name="auditRecord.audit_status_label">
                                                            {$auditRecord.status_label}
                                                        </empty>
                                                        <notempty name="auditRecord.audit_status_label">
                                                            {$auditRecord.audit_status_label}
                                                        </notempty>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-list-alt"></i> 查账类型</th>
                                                    <td>
                                                        <empty name="auditRecord.audit_type_label">
                                                            <span class="badge badge-secondary">未知类型</span>
                                                        </empty>
                                                        <notempty name="auditRecord.audit_type_label">
                                                            {$auditRecord.audit_type_label}
                                                            <notempty name="auditRecord.needs_callback">
                                                                <span class="badge badge-info">需要OA回调确认</span>
                                                            </notempty>
                                                        </notempty>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-calendar"></i> 汇款时间</th>
                                                    <td>{$auditRecord.remit_time}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-user"></i> 汇款人信息</th>
                                                    <td>{$auditRecord.remitter_info}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="fa fa-university"></i> 收款账户</th>
                                                    <td>{$auditRecord.account}</td>
                                                </tr>
                                                <notempty name="auditRecord.remark">
                                                    <tr>
                                                        <th><i class="fa fa-comment"></i> 备注</th>
                                                        <td>{$auditRecord.remark}</td>
                                                    </tr>
                                                </notempty>
                                                <notempty name="auditRecord.annex">
                                                    <tr>
                                                        <th><i class="fa fa-paperclip"></i> 附件</th>
                                                        <td>
                                                            <a href="{$auditRecord.annex}" target="_blank"
                                                                class="btn btn-xs btn-info">
                                                                <i class="fa fa-download"></i> 查看附件
                                                            </a>
                                                        </td>
                                                    </tr>
                                                </notempty>
                                                <tr>
                                                    <th><i class="fa fa-clock-o"></i> 创建时间</th>
                                                    <td>{$auditRecord.create_time_text}</td>
                                                </tr>
                                                <notempty name="auditRecord.update_time_text">
                                                    <tr>
                                                        <th><i class="fa fa-refresh"></i> 更新时间</th>
                                                        <td>{$auditRecord.update_time_text}</td>
                                                    </tr>
                                                </notempty>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- 发票状态 -->
                            <notempty name="auditRecord.can_apply_invoice">
                                <div class="panel panel-success">
                                    <div class="panel-heading">
                                        <h5 class="panel-title"><i class="fa fa-file-text"></i> 发票申请</h5>
                                    </div>
                                    <div class="panel-body">
                                        <eq name="auditRecord.invoice_status" value="0">
                                            <div class="alert alert-success">
                                                <i class="fa fa-check-circle"></i> 查账已成功，您可以申请发票。
                                            </div>
                                            <a href="{:U('OA/Invoice/onlineInvoice', array('oa_id' => $auditRecord['oa_id']))}"
                                                class="btn btn-success btn-block">
                                                <i class="fa fa-file-text"></i> 申请发票
                                            </a>
                                        </eq>
                                        <eq name="auditRecord.invoice_status" value="1">
                                            <div class="alert alert-info">
                                                <i class="fa fa-info-circle"></i> 发票已申请，正在处理中。
                                            </div>
                                        </eq>
                                        <eq name="auditRecord.invoice_status" value="2">
                                            <div class="alert alert-success">
                                                <i class="fa fa-check-circle"></i> 发票已开具。
                                            </div>
                                        </eq>
                                        <php>
                                            $unknownInvoiceStatus = ($auditRecord['invoice_status'] != 0 &&
                                            $auditRecord['invoice_status'] != 1 && $auditRecord['invoice_status'] != 2);
                                        </php>
                                        <notempty name="unknownInvoiceStatus">
                                            <div class="alert alert-default">
                                                <i class="fa fa-question-circle"></i> 发票状态未知。
                                            </div>
                                        </notempty>
                                    </div>
                                </div>
                            </notempty>
                            <empty name="auditRecord.can_apply_invoice">
                                <notempty name="auditRecord.needs_callback">
                                    <eq name="auditRecord.status" value="10">
                                        <div class="panel panel-warning">
                                            <div class="panel-heading">
                                                <h5 class="panel-title"><i class="fa fa-clock-o"></i> 等待OA审核</h5>
                                            </div>
                                            <div class="panel-body">
                                                <div class="alert alert-warning">
                                                    <i class="fa fa-info-circle"></i>
                                                    线下查账需要等待OA审核确认后才能申请发票。
                                                </div>
                                            </div>
                                        </div>
                                    </eq>
                                </notempty>
                            </empty>
                        </div>
                    </div>
                </notempty>

            </div>
        </div>
</block>