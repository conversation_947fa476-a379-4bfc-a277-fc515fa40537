<?php

namespace OA\Service;

use Common\Lib\InvoiceStatusConstants;

/**
 * OA发票申请服务类
 * 处理发票申请相关的业务逻辑
 */
class InvoiceService extends BaseService
{
    /**
     * 构建提交人信息
     *
     * @param array $userOaInfo 用户OA信息
     * @return array 提交人信息数组
     */
    protected function buildSubmitterInfo($userOaInfo)
    {
        return [
            'userId' => intval($userOaInfo['userId']),
            'submitName' => $userOaInfo['workId'] . '#' . $userOaInfo['name'],
            'deptId' => intval($userOaInfo['deptId']),
            'submitDept' => $userOaInfo['deptName']
        ];
    }


    /**
     * 提交发票申请
     * @param array $data 发票申请数据，可以是单个发票数据或多个发票数据的数组
     * @return array 提交结果
     * @throws \Common\Exception\RemoteApiException 当远程API调用失败时抛出
     * @throws \Common\Exception\LocalOperationException 当本地操作失败时抛出
     */
    public function post($data)
    {
        // 判断是否是多个发票的数组
        $isMultipleInvoices = $this->isMultiDimensionalArray($data);

        // 如果是多个发票，处理每一个发票
        if ($isMultipleInvoices) {
            $results = [];
            $hasError = false;
            $errorMessage = '';

            foreach ($data as $index => $invoiceData) {
                try {
                    $results[] = $this->processSingleInvoice($invoiceData);
                } catch (\Common\Exception\RemoteApiException $e) {
                    // 记录远程API错误
                    \Think\Log::write('处理第' . ($index + 1) . '个发票时出错(远程API异常): ' . $e->getMessage(), 'ERROR');
                    $results[] = ['status' => false, 'msg' => $e->getMessage(), 'error_source' => 'remote'];
                    $hasError = true;
                    $errorMessage = $e->getMessage();
                    // 对于远程API异常，我们继续处理其他发票，但最终会返回失败状态
                } catch (\Common\Exception\LocalOperationException $e) {
                    // 记录本地操作错误
                    \Think\Log::write('处理第' . ($index + 1) . '个发票时出错(本地操作异常): ' . $e->getMessage(), 'ERROR');
                    $results[] = ['status' => false, 'msg' => $e->getMessage(), 'error_source' => 'local'];
                    $hasError = true;
                    $errorMessage = $e->getMessage();
                    // 对于本地操作异常，我们继续处理其他发票，但最终会返回失败状态
                } catch (\Exception $e) {
                    // 记录其他未预期的错误
                    \Think\Log::write('处理第' . ($index + 1) . '个发票时出错(系统异常): ' . $e->getMessage(), 'ERROR');
                    $results[] = ['status' => false, 'msg' => $e->getMessage(), 'error_source' => 'system'];
                    $hasError = true;
                    $errorMessage = $e->getMessage();
                    // 对于其他异常，我们继续处理其他发票，但最终会返回失败状态
                }
            }

            // 如果有任何错误，返回失败状态
            if ($hasError) {
                return [
                    'status' => false,
                    'msg' => '发票处理过程中出现错误: ' . $errorMessage,
                    'data' => $results
                ];
            }

            return ['status' => true, 'data' => $results];
        } else {
            // 如果是单个发票，直接处理，让异常向上传递
            return $this->processSingleInvoice($data);
        }
    }

    /**
     * 判断是否是多维数组
     *
     * @param array $array 要检查的数组
     * @return bool 是否是多维数组
     */
    protected function isMultiDimensionalArray($array)
    {
        if (!is_array($array)) {
            return false;
        }

        // 检查第一个元素是否是数组
        reset($array);
        $firstKey = key($array);

        // 如果第一个元素是数组，并且包含发票的关键字段，则认为是多个发票的数组
        return is_array($array[$firstKey]) &&
            (isset($array[$firstKey]['invoiceTitle']) || isset($array[$firstKey]['orderNum']));
    }

    /**
     * 处理单个发票数据
     *
     * @param array $data 单个发票数据
     * @return array 处理结果
     */
    protected function processSingleInvoice($data)
    {  // 获取用户OA信息
        $userOaInfo = $this->getUserOaInfo(session('userid'));

        // 验证参数
        $validateResult = $this->validate(
            $data,
            array(
                'userId' => array(
                    'required' => true,
                    'message' => '用户ID不能为空',
                ),
                'deptId' => array(
                    'required' => true,
                    'message' => '部门ID不能为空',
                ),
                'submitName' => array(
                    'required' => true,
                    'message' => '提交人姓名不能为空',
                ),
                'submitDept' => array(
                    'required' => true,
                    'message' => '提交部门不能为空',
                ),
                'orderNum' => array(
                    'required' => true,
                    'message' => '订单号不能为空',
                ),
                'invoiceTitle' => array(
                    'required' => true,
                    'message' => '发票抬头不能为空',
                ),
                'buyerTaxNum' => array(
                    'required' => true,
                    'message' => '购买方税号不能为空',
                ),
                'amount' => array(
                    'required' => true,
                    'message' => '金额不能为空',
                    'validator' => function ($value) {
                        return is_numeric($value) && floatval($value) > 0;
                    },
                    'validator_message' => '金额必须为大于0的数字',
                ),
                // 'buyerPhone' => array(
                //     'required' => true,
                //     'message' => '购买方电话不能为空',
                // ),
                'buyerEmail' => array(
                    'required' => true,
                    'message' => '购买方邮箱不能为空',
                    'validator' => function ($value) {
                        return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
                    },
                    'validator_message' => '购买方邮箱格式不正确',
                ),
                'goodsInfo' => array(
                    'required' => true,
                    'message' => '商品信息不能为空',
                ),
            )
        );

        if (!$validateResult['status']) {
            throw new \Common\Exception\LocalOperationException($validateResult['msg']);
        }

        // 构建请求数据，确保字段顺序和类型正确
        $requestData = array(
            'userId' => intval($userOaInfo['userId']),
            'deptId' => intval($userOaInfo['deptId']),
            'submitName' => $userOaInfo['workId'] . '#' . $userOaInfo['name'],
            'submitDept' => $userOaInfo['deptName'],
            'no' => null,
            'orderNum' => isset($data['oa_id']) ? $data['oa_id'] : $data['orderNum'],
            'invoiceTitle' => $data['invoiceTitle'],
            'buyerTaxNum' => $data['buyerTaxNum'],
            'amount' => floatval($data['amount']),
            'buyerPhone' => $data['buyerPhone'],
            'buyerEmail' => $data['buyerEmail'],
            'buyerAddress' => isset($data['buyerAddress']) ? $data['buyerAddress'] : null,
            'buyerAccount' => isset($data['buyerAccount']) ? $data['buyerAccount'] : null,
            'buyerAccountName' => isset($data['buyerAccountName']) ? $data['buyerAccountName'] : null,
            'salerCompany' => isset($data['salerCompany']) ? $data['salerCompany'] : null,
            'goodsInfo' => $data['goodsInfo'],
            'invoiceType' => isset($data['invoiceType']) ? $data['invoiceType'] : null,
            'submitReturnApi' => $this->getCallbackUrl($data['no']),
            'remark' => isset($data['remark']) ? $data['remark'] : null
        );

        // dump($requestData);
        // exit;

        // 调用API提交在线开票数据
        $result = send_oa_api_request('addReceipt', $requestData);

        // 检查API返回结果
        if (!isset($result['code']) || $result['code'] != 200) {
            $errorMessage = isset($result['msg']) ? $result['msg'] : '远程API请求失败或返回错误';
            $errorCode = isset($result['code']) ? $result['code'] : 500;
            // 抛出自定义的远程API异常
            throw new \Common\Exception\RemoteApiException($errorMessage, $errorCode);
        }

        // 确保返回结果中包含正确的状态
        $result['status'] = true;

        // 记录API返回的详细信息
        \Think\Log::write('OA API返回结果: ' . json_encode($result, JSON_UNESCAPED_UNICODE), 'INFO');

        // 如果返回结果中包含"操作成功"的消息，确保不会被当作错误信息
        if (isset($result['msg']) && $result['msg'] === '操作成功') {
            $result['message'] = '发票申请提交成功';
            unset($result['msg']);
        }

        // 确保返回结果中包含发票ID
        if (!isset($result['data']['id']) && isset($result['data']) && is_array($result['data'])) {
            // 尝试从返回数据中提取ID
            if (isset($result['data']['receiptId'])) {
                $result['data']['id'] = $result['data']['receiptId'];
            } elseif (isset($result['data']['receipt_id'])) {
                $result['data']['id'] = $result['data']['receipt_id'];
            }
        }

        return $result;
    }



    /**
     * 根据查账记录查找关联的发票记录
     * 通过查账记录的reg_id或pay_id匹配发票表中的对应字段
     *
     * @param array $auditRecord 查账记录
     * @return array 关联的发票记录列表
     */
    public function findRelatedInvoices($auditRecord)
    {
        if (empty($auditRecord)) {
            return array();
        }

        // 实例化发票模型
        $invoiceModel = D('OaInvoice', 'OA');
        $invoices = array();

        // 首先尝试通过oa_id查询关联的发票记录
        if (!empty($auditRecord['oa_id'])) {
            $invoices = $invoiceModel->where(array('oa_id' => $auditRecord['oa_id']))->select();
            if (!empty($invoices)) {
                \Think\Log::write('通过oa_id找到关联发票，数量=' . count($invoices), 'INFO');
                // 继续处理这些发票记录
            }
        }

        // 如果通过oa_id没有找到，尝试通过reg_id查询
        if (empty($invoices) && !empty($auditRecord['reg_id'])) {
            $invoices = $invoiceModel->where(array('reg_id' => $auditRecord['reg_id']))->select();
            if (!empty($invoices)) {
                \Think\Log::write('通过reg_id找到关联发票，数量=' . count($invoices), 'INFO');
            }
        }

        // 如果通过reg_id没有找到，且有pay_id，尝试通过pay_id查询（游客付款的情况）
        if (empty($invoices) && !empty($auditRecord['pay_id'])) {
            $invoices = $invoiceModel->where(array('pay_id' => $auditRecord['pay_id']))->select();
            if (!empty($invoices)) {
                \Think\Log::write('通过pay_id找到关联发票，数量=' . count($invoices), 'INFO');
            }
        }

        // 如果找到了发票记录，但发票记录中没有oa_id，则更新发票记录，建立关联
        if (!empty($invoices) && !empty($auditRecord['oa_id'])) {
            foreach ($invoices as &$invoice) {
                // 如果发票记录没有oa_id，但有查账记录的oa_id，则更新发票记录
                if (empty($invoice['oa_id'])) {
                    $updateData = array(
                        'oa_id' => $auditRecord['oa_id'],
                        'update_time' => time()
                    );

                    // 更新发票记录
                    $invoiceModel->where(array('id' => $invoice['id']))->save($updateData);

                    // 更新当前记录的oa_id
                    $invoice['oa_id'] = $auditRecord['oa_id'];
                    \Think\Log::write('更新发票记录ID=' . $invoice['id'] . '的oa_id为' . $auditRecord['oa_id'], 'INFO');
                }

                // 处理状态文本和类型文本
                $invoice['status_text'] = InvoiceStatusConstants::getStatusText($invoice['status']);
                $invoice['status_class'] = InvoiceStatusConstants::getStatusClass($invoice['status']);

                // 处理发票类型文本
                if ($invoice['invoice_type'] == 'pc') {
                    $invoice['invoice_type_text'] = '普通发票';
                } else if ($invoice['invoice_type'] == 'bs') {
                    $invoice['invoice_type_text'] = '专用发票';
                } else {
                    $invoice['invoice_type_text'] = '未知类型';
                }

                // 格式化时间
                if (!empty($invoice['create_time'])) {
                    $invoice['create_time_text'] = date('Y-m-d H:i:s', $invoice['create_time']);
                }

                if (!empty($invoice['update_time'])) {
                    $invoice['update_time_text'] = date('Y-m-d H:i:s', $invoice['update_time']);
                }
            }
        }

        return $invoices;
    }

    /**
     * 查找关联的发票记录并返回详细信息
     * @param array $auditRecord 查账记录
     * @return array 关联的发票记录列表（包含详细信息）
     */
    public function findRelatedInvoicesWithDetails($auditRecord)
    {
        $invoices = $this->findRelatedInvoices($auditRecord);

        if (empty($invoices)) {
            return [];
        }

        // 处理每条发票记录，添加详细信息
        foreach ($invoices as &$invoice) {
            // 格式化金额
            $invoice['formatted_amount'] = number_format($invoice['amount'], 2);

            // 检查是否可编辑（只有待审核状态的发票可以编辑）
            $invoice['is_editable'] = ($invoice['status'] == InvoiceStatusConstants::INVOICE_PENDING);

            // 添加事件和文章ID的显示格式
            $invoice['event_display'] = $invoice['event'] ?: $auditRecord['event'];
            $invoice['paper_id_display'] = $invoice['paper_id'] ?: $auditRecord['paper_id'];
        }

        return $invoices;
    }

    /**
     * 准备并提交发票申请
     * 通过OA流水号提取记录，并将其组合成符合OA预期的格式
     *
     * @param string $oa_id OA流水号
     * @param array $formData 表单数据（可选）
     * @return array 提交结果
     */
    public function prepareAndSubmitInvoice($oa_id, $formData = [])
    {
        try {
            // 查询查账记录
            $auditAccountModel = D('OaAuditAccount', 'OA');
            $auditRecord = $auditAccountModel->where(['oa_id' => $oa_id])->find();

            if (empty($auditRecord)) {
                throw new \Exception('未找到相关的查账记录');
            }

            // 获取支付信息（只用于获取orderid）
            $payInfo = null;
            if (!empty($auditRecord['pay_id'])) {
                $payModel = M('SubPay');
                $payInfo = $payModel->find($auditRecord['pay_id']);
            }

            // 获取用户OA信息
            $userOaInfo = $this->getUserOaInfo(session('userid'));

            // 准备发票数据
            $invoiceData = $this->prepareInvoiceDataForOA($auditRecord, $payInfo, $userOaInfo);

            // 如果提供了表单数据，使用表单数据覆盖默认值
            if (!empty($formData)) {
                \Think\Log::write('使用表单数据覆盖默认发票数据: ' . json_encode($formData, JSON_UNESCAPED_UNICODE), 'INFO');

                // 覆盖发票数据
                if (!empty($formData['invoiceTitle'])) {
                    $invoiceData['invoiceTitle'] = $formData['invoiceTitle'];
                }
                if (!empty($formData['amount'])) {
                    $invoiceData['amount'] = $formData['amount'];
                }
                if (!empty($formData['invoiceType'])) {
                    $invoiceData['invoiceType'] = $formData['invoiceType'];
                }
                if (!empty($formData['buyerTaxNum'])) {
                    $invoiceData['buyerTaxNum'] = $formData['buyerTaxNum'];
                }
                if (!empty($formData['buyerEmail'])) {
                    $invoiceData['buyerEmail'] = $formData['buyerEmail'];
                }
                if (!empty($formData['buyerPhone'])) {
                    $invoiceData['buyerPhone'] = $formData['buyerPhone'];
                }
                if (!empty($formData['goodsInfo'])) {
                    $invoiceData['goodsInfo'] = $formData['goodsInfo'];
                }
                if (!empty($formData['remark'])) {
                    $invoiceData['remark'] = $formData['remark'];
                }
            }

            // 提交发票申请
            $result = $this->post($invoiceData);

            // 如果提交成功，保存发票记录
            if ($result['status']) {
                $this->saveInvoiceRecord($auditRecord, $invoiceData, $result);
            }

            return $result;
        } catch (\Exception $e) {
            // 记录详细的错误信息
            \Think\Log::write('开票失败：' . $e->getMessage(), 'ERROR');

            // 返回详细的错误信息
            return [
                'status' => false,
                'msg' => $e->getMessage(),
                'error_details' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ];
        }
    }

    /**
     * 获取用户OA信息
     *
     * @return array 用户OA信息
     * @throws \Exception 如果无法获取用户OA信息
     */
    public function getUserOaInfo()
    {
        // 初始化用户OA信息
        $this->initUserOaInfo();

        // 如果已经初始化了用户OA信息，直接返回
        if ($this->isOaBound && !empty($this->userOaInfo)) {
            return $this->userOaInfo;
        }

        // 如果没有初始化成功，抛出异常
        throw new \Exception('未能获取用户OA信息，请先绑定OA账号');
    }

    /**
     * 更新关联的查账记录的发票状态
     * 当发票成功提交到OA系统后，更新关联的查账记录的发票状态为"已申请"
     *
     * @param array $invoiceIds 发票ID数组
     * @return bool 更新是否成功
     */
    protected function updateRelatedAuditAccountInvoiceStatus($invoiceIds)
    {
        if (empty($invoiceIds)) {
            \Think\Log::write('更新查账记录发票状态失败：发票ID为空', 'ERROR');
            return false;
        }

        // 实例化发票模型
        $invoiceModel = D('OaInvoice', 'OA');

        // 查询发票记录，获取关联的OA流水号
        $invoices = $invoiceModel->where(['id' => ['in', $invoiceIds]])->select();

        if (empty($invoices)) {
            \Think\Log::write('更新查账记录发票状态失败：未找到发票记录', 'ERROR');
            return false;
        }

        // 收集所有OA流水号
        $oaIds = [];
        foreach ($invoices as $invoice) {
            if (!empty($invoice['oa_id']) && !in_array($invoice['oa_id'], $oaIds)) {
                $oaIds[] = $invoice['oa_id'];
            }
        }

        if (empty($oaIds)) {
            \Think\Log::write('更新查账记录发票状态失败：未找到关联的OA流水号', 'ERROR');
            return false;
        }

        // 实例化查账记录模型
        $auditAccountModel = D('OaAuditAccount', 'OA');

        // 更新所有关联的查账记录的发票状态为"已申请"
        $updateData = [
            'invoice_status' => \Common\Lib\AuditAccountInvoiceStatusConstants::INVOICE_APPLIED,
            'update_time' => time()
        ];

        $updateResult = $auditAccountModel->where(['oa_id' => ['in', $oaIds]])->save($updateData);

        if ($updateResult === false) {
            \Think\Log::write('更新查账记录发票状态失败：数据库操作失败', 'ERROR');
            return false;
        }

        \Think\Log::write('成功更新' . $updateResult . '条查账记录的发票状态为已申请', 'INFO');
        return true;
    }

    /**
     * 保存发票记录
     *
     * @param array $auditRecord 查账记录
     * @param array $invoiceData 发票数据
     * @param array $result 提交结果
     * @return bool 是否成功
     */
    protected function saveInvoiceRecord($auditRecord, $invoiceData, $result)
    {
        // 从提交结果中提取OA发票ID
        $oa_invoice_id = isset($result['data']['id']) ? $result['data']['id'] : '';
        $invoiceModel = D('OaInvoice', 'OA');

        $data = [
            'oa_id' => $auditRecord['oa_id'],
            'order_id' => $invoiceData['orderNum'],
            'cid' => $auditRecord['cid'],
            'reg_id' => $auditRecord['reg_id'],
            'pay_id' => $auditRecord['pay_id'],
            'submitName' => $invoiceData['submitName'],
            'submit_id' => session('userid'),
            'user_id' => session('userid'),
            'invoice_title' => $invoiceData['invoiceTitle'],
            'buyer_tax_num' => $invoiceData['buyerTaxNum'],
            'amount' => $invoiceData['amount'],
            'buyer_phone' => $invoiceData['buyerPhone'],
            'buyer_email' => $invoiceData['buyerEmail'],
            'buyer_address' => $invoiceData['buyerAddress'],
            'buyer_account' => $invoiceData['buyerAccount'],
            'buyer_account_name' => $invoiceData['buyerAccountName'],
            'saler_company' => $invoiceData['salerCompany'],
            'goods_info' => $invoiceData['goodsInfo'],
            'invoice_type' => $invoiceData['invoiceType'],
            'remark' => $invoiceData['remark'],
            'status' => InvoiceStatusConstants::INVOICE_WAITING, // 更新为等待开票状态
            'create_time' => time(),
            'update_time' => time(),
            'no' => $oa_invoice_id // 保存OA发票ID
        ];

        // 添加发票记录
        $id = $invoiceModel->add($data);

        if ($id) {
            // 更新查账记录的发票状态为"已申请"
            $auditAccountModel = D('OaAuditAccount', 'OA');
            $auditAccountModel->where(['id' => $auditRecord['id']])->save([
                'invoice_status' => \Common\Lib\AuditAccountInvoiceStatusConstants::INVOICE_APPLIED,
                'update_time' => time()
            ]);
            return true;
        }

        return false;
    }

    /**
     * 准备符合OA预期格式的发票数据
     *
     * @param array $auditRecord 查账记录
     * @param array $payInfo 支付信息
     * @param array $userOaInfo 用户OA信息
     * @return array 发票数据
     */
    protected function prepareInvoiceDataForOA($auditRecord, $payInfo, $userOaInfo)
    {
        // 直接从发票表中获取记录
        $invoiceModel = D('OaInvoice', 'OA');
        $invoiceRecord = $invoiceModel->where(['oa_id' => $auditRecord['oa_id']])->find();

        // 如果找到了发票记录，直接使用该记录
        if (!empty($invoiceRecord)) {
            return [
                'userId' => intval($userOaInfo['userId']),
                'deptId' => intval($userOaInfo['deptId']),
                'submitName' => $userOaInfo['workId'] . '#' . $userOaInfo['name'],
                'submitDept' => $userOaInfo['deptName'],
                'no' => $invoiceRecord['no'],
                'orderNum' => $auditRecord['oa_id'],
                'invoiceTitle' => $invoiceRecord['invoice_title'],
                'buyerTaxNum' => $invoiceRecord['buyer_tax_num'],
                'amount' => $invoiceRecord['amount'],
                'buyerPhone' => $invoiceRecord['buyer_phone'],
                'buyerEmail' => $invoiceRecord['buyer_email'],
                'buyerAddress' => $invoiceRecord['buyer_address'],
                'buyerAccount' => $invoiceRecord['buyer_account'],
                'buyerAccountName' => $invoiceRecord['buyer_account_name'],
                'salerCompany' => $invoiceRecord['saler_company'],
                'goodsInfo' => $invoiceRecord['goods_info'] ?: ('会议注册费，会议简称：' . $auditRecord['event']),
                'invoiceType' => $invoiceRecord['invoice_type'],
                'submitReturnApi' => $this->getCallbackUrl($invoiceRecord['no']),
                'remark' => $invoiceRecord['remark']
            ];
        }

        // 如果没有找到发票记录，使用查账记录中的信息
        $invoiceData = [
            'userId' => intval($userOaInfo['userId']),
            'deptId' => intval($userOaInfo['deptId']),
            'submitName' => $userOaInfo['workId'] . '#' . $userOaInfo['name'],
            'submitDept' => $userOaInfo['deptName'],
            'no' => null,
            'orderNum' => $auditRecord['oa_id'],
            'invoiceTitle' => $auditRecord['remitter_info'] ?: '会议参会者',
            'buyerTaxNum' => '',
            'amount' => $auditRecord['total'],
            'buyerPhone' => '',
            'buyerEmail' => '',
            'buyerAddress' => null,
            'buyerAccount' => null,
            'buyerAccountName' => null,
            'salerCompany' => null,
            'goodsInfo' => '会议注册费，会议简称：' . $auditRecord['event'],
            'invoiceType' => \Common\Lib\InvoiceTypeConstants::TYPE_NORMAL,
            'submitReturnApi' => $this->getCallbackUrl(''),
            'remark' => '',
            'invoiceRemark' => null, // 新增：发票备注信息
            'buyerTel' => null // 新增：买方电话
        ];

        return $invoiceData;
    }


    public function prepareInvoiceData($auditRecord)
    {
        if (empty($auditRecord)) {
            return array();
        }

        // 准备基本数据
        $invoiceData = array(
            'oa_id' => $auditRecord['oa_id'],
            'reg_id' => $auditRecord['reg_id'],
            'pay_id' => $auditRecord['pay_id'],
            'cid' => $auditRecord['cid'],
            'amount' => $auditRecord['total'],
            'currency' => $auditRecord['currency'],
            'event' => $auditRecord['event'],
            'paper_id' => $auditRecord['paper_id']
        );


        return $invoiceData;
    }

    /**
     * 更新发票信息
     * @param int $invoiceId 发票ID
     * @param string $invoiceTitle 发票抬头
     * @param string $buyerTaxNum 税号
     * @param string $buyerEmail 买方邮箱
     * @param string $buyerPhone 买方电话
     * @return bool 更新结果
     */
    public function updateInvoiceInfo($invoiceId, $invoiceTitle, $buyerTaxNum, $buyerEmail, $buyerPhone)
    {
        // 验证参数
        if (empty($invoiceId)) {
            throw new \Exception('参数错误：缺少发票ID');
        }

        // 实例化发票模型
        $invoiceModel = D('OaInvoice', 'OA');

        // 查询发票记录
        $invoice = $invoiceModel->find($invoiceId);

        if (empty($invoice)) {
            throw new \Exception('未找到相关的发票记录');
        }

        // 检查发票状态是否允许编辑
        if ($invoice['status'] != InvoiceStatusConstants::INVOICE_PENDING) {
            throw new \Exception('当前发票状态不允许编辑');
        }

        // 准备更新数据
        $updateData = [
            'invoice_title' => $invoiceTitle,
            'buyer_tax_num' => $buyerTaxNum,
            'buyer_email' => $buyerEmail,
            'buyer_phone' => $buyerPhone,
            'update_time' => time()
        ];

        // 开始事务
        $invoiceModel->startTrans();

        try {
            // 更新当前发票
            $result = $invoiceModel->where(['id' => $invoiceId])->save($updateData);

            // 提交事务
            $invoiceModel->commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            $invoiceModel->rollback();
            throw $e;
        }
    }

    /**
     * 批量更新发票信息
     * @param array $invoiceIds 发票ID数组
     * @param string $invoiceTitle 发票抬头
     * @param string $buyerTaxNum 税号
     * @param string $buyerEmail 买方邮箱
     * @param string $buyerPhone 买方电话
     * @return bool 更新结果
     */
    public function batchUpdateInvoiceInfo($invoiceIds, $invoiceTitle, $buyerTaxNum, $buyerEmail, $buyerPhone)
    {
        // 验证参数
        if (empty($invoiceIds) || !is_array($invoiceIds)) {
            throw new \Exception('参数错误：缺少发票ID数组');
        }

        // 实例化发票模型
        $invoiceModel = D('OaInvoice', 'OA');

        // 查询发票记录
        $invoices = $invoiceModel->where(['id' => ['in', $invoiceIds]])->select();

        if (empty($invoices)) {
            throw new \Exception('未找到相关的发票记录');
        }

        // 检查发票状态是否允许编辑
        foreach ($invoices as $invoice) {
            if ($invoice['status'] != InvoiceStatusConstants::INVOICE_PENDING) {
                throw new \Exception('发票ID为' . $invoice['id'] . '的记录状态不允许编辑');
            }
        }

        // 准备更新数据
        $updateData = [
            'invoice_title' => $invoiceTitle,
            'buyer_tax_num' => $buyerTaxNum,
            'buyer_email' => $buyerEmail,
            'buyer_phone' => $buyerPhone,
            'update_time' => time()
        ];

        // 开始事务
        $invoiceModel->startTrans();

        try {
            // 批量更新发票
            $result = $invoiceModel->where(['id' => ['in', $invoiceIds]])->save($updateData);

            // 提交事务
            $invoiceModel->commit();
            return $result !== false;
        } catch (\Exception $e) {
            // 回滚事务
            $invoiceModel->rollback();
            throw $e;
        }
    }

    /**
     * 根据OA流水号更新所有关联发票
     * @param string $oaId OA流水号
     * @param string $invoiceTitle 发票抬头
     * @param string $buyerTaxNum 税号
     * @param string $buyerEmail 买方邮箱
     * @param string $buyerPhone 买方电话
     * @param int $syncAll 是否同步更新所有关联发票（包括其他OA流水号的发票）
     * @return bool 更新结果
     * @throws \Exception 更新失败时抛出异常
     */
    public function updateAllInvoicesByOaId($oaId, $invoiceTitle, $buyerTaxNum, $buyerEmail, $buyerPhone, $syncAll = 0)
    {
        // 验证参数
        if (empty($oaId)) {
            throw new \Exception('参数错误：缺少OA流水号');
        }

        // 验证必填字段
        if (empty($invoiceTitle)) {
            throw new \Exception('参数错误：发票抬头不能为空');
        }

        if (empty($buyerTaxNum)) {
            throw new \Exception('参数错误：税号不能为空');
        }

        if (empty($buyerEmail)) {
            throw new \Exception('参数错误：买方邮箱不能为空');
        }

        // 验证邮箱格式
        if (!filter_var($buyerEmail, FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('参数错误：买方邮箱格式不正确');
        }

        // 实例化发票模型
        $invoiceModel = D('OaInvoice', 'OA');

        // 查询查账记录
        $auditAccountModel = D('OaAuditAccount', 'OA');
        $auditRecord = $auditAccountModel->where(['oa_id' => $oaId])->find();

        if (empty($auditRecord)) {
            throw new \Exception('未找到相关的查账记录');
        }

        // 查询关联的发票记录
        $invoices = $invoiceModel->where([
            'oa_id' => $oaId,
            'status' => \Common\Lib\InvoiceStatusConstants::INVOICE_PENDING // 只更新待审核状态的发票
        ])->select();

        if (empty($invoices)) {
            throw new \Exception('未找到可编辑的发票记录');
        }

        // 准备更新数据
        $updateData = [
            'invoice_title' => $invoiceTitle,
            'buyer_tax_num' => $buyerTaxNum,
            'buyer_email' => $buyerEmail,
            'buyer_phone' => $buyerPhone,
            'update_time' => time()
        ];

        // 开始事务
        $invoiceModel->startTrans();

        try {
            // 更新当前OA流水号下的所有待审核发票
            $result = $invoiceModel->where([
                'oa_id' => $oaId,
                'status' => \Common\Lib\InvoiceStatusConstants::INVOICE_PENDING
            ])->save($updateData);

            // 如果需要同步更新所有关联发票
            if ($syncAll && $result && !empty($auditRecord['reg_id'])) {
                // 更新同一注册ID下的所有待审核发票（不包括当前OA流水号的发票）
                $invoiceModel->where([
                    'reg_id' => $auditRecord['reg_id'],
                    'status' => \Common\Lib\InvoiceStatusConstants::INVOICE_PENDING,
                    'oa_id' => ['neq', $oaId] // 排除当前OA流水号的发票
                ])->save($updateData);
            }

            // 提交事务
            $invoiceModel->commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            $invoiceModel->rollback();
            throw $e;
        }
    }

    /**
     * 获取回调URL
     * 从配置文件中获取当前环境的回调URL，并添加发票编号参数
     *
     * @param string $invoiceNo 发票编号
     * @return string 回调URL
     */
    public function getCallbackUrl($invoiceNo = '')
    {
        // 从配置文件中获取当前环境
        $environment = C('OA_ENVIRONMENT');

        // 从配置文件中获取回调URL
        $baseUrl = C('OA_CALLBACK_URLS.' . $environment);

        // 如果未配置回调URL，记录警告并返回空字符串
        if (empty($baseUrl)) {
            \Think\Log::write('未配置回调URL，当前环境：' . $environment, 'WARN');
            return '';
        }

        // 确保URL以斜杠结尾
        if (substr($baseUrl, -1) !== '/') {
            $baseUrl .= '/';
        }

        // 添加invoice路径
        $callbackUrl = $baseUrl;

        // 如果提供了发票编号，添加到URL中
        if (!empty($invoiceNo)) {
            // 判断URL是否已经包含参数
            $separator = (strpos($callbackUrl, '?') !== false) ? '&' : '?';
            // 添加发票编号参数
            $callbackUrl .= $separator . 'no=' . urlencode($invoiceNo);
        }

        return $callbackUrl;
    }

    /**
     * 创建发票
     * @param array $data 发票数据
     * @return array 创建结果，包含status、message和data
     */
    public function createInvoice($data)
    {
        try {
            // 记录传入的数据
            \Think\Log::write('创建发票 - 传入数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'INFO');

            // 确保金额是浮点数，使用 number_format 处理精度问题
            if (isset($data['amount'])) {
                $data['amount'] = number_format((float)$data['amount'], 2, '.', '');
            }

            // 确定支付类型
            $payType = isset($data['pay_id']) && !empty($data['pay_id'])
                ? \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_ONLINE
                : \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_OFFLINE;

            // 准备发票数据
            $invoiceData = [
                'cid' => isset($data['cid']) ? $data['cid'] : 0,
                'reg_id' => isset($data['reg_id']) ? $data['reg_id'] : 0,
                'paper_id' => isset($data['paper_id']) ? $data['paper_id'] : '',
                'event' => isset($data['event']) ? $data['event'] : '',
                'invoice_title' => $data['invoice_title'],
                'buyer_tax_num' => $data['buyer_tax_num'],
                'amount' => $data['amount'],
                'buyer_phone' => isset($data['buyer_phone']) ? $data['buyer_phone'] : '',
                'buyer_email' => $data['buyer_email'],
                'buyer_address' => isset($data['buyer_address']) ? $data['buyer_address'] : '',
                'buyer_account' => isset($data['buyer_account']) ? $data['buyer_account'] : '',
                'buyer_account_name' => isset($data['buyer_account_name']) ? $data['buyer_account_name'] : '',
                'buyer_account_bank' => isset($data['buyer_account_bank']) ? $data['buyer_account_bank'] : '',
                'saler_company' => isset($data['saler_company']) ? $data['saler_company'] : '',
                'goods_info' => $data['goods_info'],
                'invoice_type' => $data['invoice_type'],
                'remark' => isset($data['remark']) ? $data['remark'] : '',
                'status' => \Common\Lib\InvoiceStatusConstants::INVOICE_PENDING,
                'create_time' => time(),
                'update_time' => time(),
                'no' => $this->generateInvoiceNo(),
                'call_back_url' => isset($data['call_back_url']) ? $data['call_back_url'] : '',
                'add_by_admin' => 1 // 标记为管理员添加
            ];

            // 根据支付类型设置pay_id或transfer_id
            if ($payType == \Common\Lib\OaOrderNumTypeConstants::ORDER_TYPE_ONLINE) {
                // 在线支付，设置pay_id
                $invoiceData['pay_id'] = isset($data['pay_id']) ? $data['pay_id'] : 0;
            } else {
                // 线下支付，设置transfer_id
                $invoiceData['transfer_id'] = isset($data['transfer_id']) ? $data['transfer_id'] : 0;
                // 确保pay_id为空
                $invoiceData['pay_id'] = 0;
            }

            // 添加发票记录
            $invoiceModel = D('OaInvoice', 'OA');
            $result = $invoiceModel->add($invoiceData);

            if (!$result) {
                \Think\Log::write('创建发票失败: ' . $invoiceModel->getError(), 'ERROR');
                return ['status' => false, 'message' => '添加发票失败：' . $invoiceModel->getError()];
            }

            // 记录成功创建的发票信息
            \Think\Log::write('成功创建发票 - ID: ' . $result . ', 金额: ' . $invoiceData['amount'], 'INFO');

            return [
                'status' => true,
                'message' => '发票创建成功',
                'data' => [
                    'id' => $result,
                    'invoice_title' => $invoiceData['invoice_title'],
                    'amount' => $invoiceData['amount'],
                    'invoice_type' => $invoiceData['invoice_type'],
                    'buyer_tax_num' => $invoiceData['buyer_tax_num'],
                    'goods_info' => $invoiceData['goods_info'],
                    'no' => $invoiceData['no']
                ]
            ];
        } catch (\Exception $e) {
            \Think\Log::write('创建发票异常: ' . $e->getMessage(), 'ERROR');
            return ['status' => false, 'message' => '创建发票失败：' . $e->getMessage()];
        }
    }

    /**
     * 生成发票编号
     * 格式：INV-[日期]-[序列号]-[哈希]
     * @return string 发票编号
     */
    protected function generateInvoiceNo() {
        $date = date('Ymd');
        $sequence = mt_rand(1000, 9999);
        $hash = substr(md5(uniqid(mt_rand(), true)), 0, 6);

        return "INV-{$date}-{$sequence}-{$hash}";
    }

    /**
     * 更新发票信息
     * @param array $data 发票数据
     * @return array 更新结果，包含status、message和data
     */
    public function updateInvoice($data)
    {
        try {
            // 记录传入的数据
            \Think\Log::write('更新发票 - 传入数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'INFO');

            // 参数校验
            if (empty($data['invoice_id'])) {
                return ['status' => false, 'message' => '参数错误：发票ID不能为空'];
            }

            // 特别处理金额字段，确保它是标准格式
            if (isset($data['amount'])) {
                $data['amount'] = number_format((float)$data['amount'], 2, '.', '');
            }

            // 实例化发票模型
            $invoiceModel = D('OaInvoice', 'OA');

            // 查询发票记录
            $invoice = $invoiceModel->find($data['invoice_id']);

            if (empty($invoice)) {
                return ['status' => false, 'message' => '未找到相关的发票记录'];
            }

            // 检查发票状态是否允许编辑
            if ($invoice['status'] != \Common\Lib\InvoiceStatusConstants::INVOICE_PENDING) {
                return ['status' => false, 'message' => '当前发票状态不允许编辑'];
            }

            // 准备更新数据
            $updateData = [
                'invoice_title' => $data['invoice_title'],
                'buyer_tax_num' => $data['buyer_tax_num'],
                'amount' => $data['amount'],
                'buyer_phone' => isset($data['buyer_phone']) ? $data['buyer_phone'] : '',
                'buyer_email' => $data['buyer_email'],
                'goods_info' => $data['goods_info'],
                'invoice_type' => $data['invoice_type'],
                'update_time' => time()
            ];

            // 可选字段
            if (isset($data['buyer_address'])) {
                $updateData['buyer_address'] = $data['buyer_address'];
            }

            if (isset($data['buyer_account'])) {
                $updateData['buyer_account'] = $data['buyer_account'];
            }

            if (isset($data['buyer_account_name'])) {
                $updateData['buyer_account_name'] = $data['buyer_account_name'];
            }

            if (isset($data['buyer_account_bank'])) {
                $updateData['buyer_account_bank'] = $data['buyer_account_bank'];
            }

            if (isset($data['remark'])) {
                $updateData['remark'] = $data['remark'];
            }

            // 开始事务
            $invoiceModel->startTrans();

            try {
                // 更新发票记录
                $updateResult = $invoiceModel->where(['id' => $data['invoice_id']])->save($updateData);

                if ($updateResult === false) {
                    // 更新失败，回滚事务
                    $invoiceModel->rollback();
                    return ['status' => false, 'message' => '更新发票失败：' . $invoiceModel->getError()];
                }

                // 提交事务
                $invoiceModel->commit();

                // 记录成功更新的发票信息
                \Think\Log::write('成功更新发票 - ID: ' . $data['invoice_id'], 'INFO');

                // 获取更新后的发票记录
                $updatedInvoice = $invoiceModel->find($data['invoice_id']);

                return [
                    'status' => true,
                    'message' => '发票更新成功',
                    'data' => $updatedInvoice
                ];
            } catch (\Exception $e) {
                // 发生异常，回滚事务
                $invoiceModel->rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            \Think\Log::write('更新发票异常: ' . $e->getMessage(), 'ERROR');
            return ['status' => false, 'message' => '更新发票失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取发票详情
     *
     * @param int $invoiceId 发票ID
     * @return array 包含状态和数据的结果数组
     */
    public function getInvoiceDetail($invoiceId)
    {
        try {
            if (empty($invoiceId)) {
                return ['status' => false, 'message' => '参数错误：发票ID不能为空'];
            }

            // 实例化发票模型
            $invoiceModel = D('OaInvoice', 'OA');

            // 查询发票记录
            $invoice = $invoiceModel->find($invoiceId);

            if (empty($invoice)) {
                return ['status' => false, 'message' => '未找到相关的发票记录'];
            }

            // 处理发票状态文本
            $invoice['status_text'] = \Common\Lib\InvoiceStatusConstants::getStatusText($invoice['status']);
            $invoice['status_class'] = \Common\Lib\InvoiceStatusConstants::getStatusClass($invoice['status']);

            // 处理发票类型文本
            if ($invoice['invoice_type'] == 'pc') {
                $invoice['invoice_type_text'] = '普通发票';
            } else if ($invoice['invoice_type'] == 'bs') {
                $invoice['invoice_type_text'] = '专用发票';
            } else {
                $invoice['invoice_type_text'] = '未知类型';
            }

            // 格式化时间
            if (!empty($invoice['create_time'])) {
                $invoice['create_time_text'] = date('Y-m-d H:i:s', $invoice['create_time']);
            }

            if (!empty($invoice['update_time'])) {
                $invoice['update_time_text'] = date('Y-m-d H:i:s', $invoice['update_time']);
            }

            // 格式化金额
            $invoice['formatted_amount'] = number_format($invoice['amount'], 2);

            // 检查是否可编辑（只有待审核状态的发票可以编辑）
            $invoice['is_editable'] = ($invoice['status'] == \Common\Lib\InvoiceStatusConstants::INVOICE_PENDING);

            return ['status' => true, 'message' => '获取发票详情成功', 'data' => $invoice];
        } catch (\Exception $e) {
            \Think\Log::write('获取发票详情失败：' . $e->getMessage(), 'ERROR');
            return ['status' => false, 'message' => '获取发票详情失败：' . $e->getMessage()];
        }
    }

    /**
     * 删除发票
     *
     * @param int $invoiceId 发票ID
     * @return array 包含状态和消息的结果数组
     */
    public function deleteInvoice($invoiceId)
    {
        try {
            if (empty($invoiceId)) {
                return ['status' => false, 'message' => '参数错误：发票ID不能为空'];
            }

            // 实例化发票模型
            $invoiceModel = D('OaInvoice', 'OA');

            // 查询发票记录
            $invoice = $invoiceModel->find($invoiceId);

            if (empty($invoice)) {
                return ['status' => false, 'message' => '未找到相关的发票记录'];
            }

            // 检查发票状态，只有待审核状态的发票可以删除
            if ($invoice['status'] != \Common\Lib\InvoiceStatusConstants::INVOICE_PENDING) {
                return ['status' => false, 'message' => '只有待审核状态的发票可以删除'];
            }

            // 开始事务
            $invoiceModel->startTrans();

            try {
                // 删除发票记录
                $result = $invoiceModel->where(['id' => $invoiceId])->delete();

                if ($result === false) {
                    // 删除失败，回滚事务
                    $invoiceModel->rollback();
                    return ['status' => false, 'message' => '删除发票失败：' . $invoiceModel->getError()];
                }

                // 提交事务
                $invoiceModel->commit();

                // 记录操作日志
                \Think\Log::write('删除发票成功，ID=' . $invoiceId, 'INFO');

                return ['status' => true, 'message' => '删除发票成功'];
            } catch (\Exception $e) {
                // 发生异常，回滚事务
                $invoiceModel->rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            \Think\Log::write('删除发票失败：' . $e->getMessage(), 'ERROR');
            return ['status' => false, 'message' => '删除发票失败：' . $e->getMessage()];
        }
    }

    /**
     * 驳回发票申请
     * @param int $invoiceId 发票ID
     * @param string $refusal 驳回原因
     * @return array 驳回结果
     */
    public function rejectInvoice($invoiceId, $refusal)
    {
        if (empty($invoiceId)) {
            throw new \Exception('参数错误：缺少发票ID');
        }

        if (empty($refusal)) {
            throw new \Exception('请填写驳回原因');
        }

        // 实例化发票模型
        $invoiceModel = D('OaInvoice', 'OA');

        // 查询发票记录
        $invoice = $invoiceModel->find($invoiceId);

        if (empty($invoice)) {
            throw new \Exception('未找到相关的发票记录');
        }

        // 检查发票状态
        if ($invoice['status'] != \Common\Lib\InvoiceStatusConstants::INVOICE_PENDING) {
            throw new \Exception('当前发票状态不允许驳回');
        }

        // 准备更新数据
        $updateData = [
            'status' => \Common\Lib\InvoiceStatusConstants::INVOICE_REJECTED,
            'refusal' => $refusal,
            'update_time' => time()
        ];

        // 更新发票状态
        $result = $invoiceModel->where(['id' => $invoiceId])->save($updateData);

        if ($result === false) {
            throw new \Exception('驳回发票申请失败');
        }

        // 返回结果
        return ['status' => true, 'message' => '发票申请已驳回'];
    }

    /**
     * 提交多张发票到API
     * 成功提交后直接将状态更新为等待开票(INVOICE_WAITING)
     * @param array $invoiceIds 发票ID数组
     * @return array 提交结果
     * @throws \Common\Exception\LocalOperationException 当本地操作失败时抛出
     * @throws \Common\Exception\RemoteApiException 当远程API调用失败时抛出
     */
    public function submitInvoicesToApi($invoiceIds)
    {
        if (empty($invoiceIds)) {
            throw new \Common\Exception\LocalOperationException('参数错误：缺少发票ID');
        }

        // 实例化发票模型
        $invoiceModel = D('OaInvoice', 'OA');

        // 查询发票记录
        $invoices = $invoiceModel->where(['id' => ['in', $invoiceIds]])->select();

        if (empty($invoices)) {
            throw new \Common\Exception\LocalOperationException('未找到相关的发票记录');
        }

        // 检查发票编号是否存在
        $missingNoInvoices = [];
        foreach ($invoices as $invoice) {
            if (empty($invoice['no'])) {
                $missingNoInvoices[] = $invoice['id'];
            }
        }

        // 如果有发票编号缺失，抛出异常
        if (!empty($missingNoInvoices)) {
            throw new \Common\Exception\LocalOperationException('以下ID的发票缺少发票编号，无法提交：' . implode(', ', $missingNoInvoices));
        }

        // 检查发票状态
        foreach ($invoices as $invoice) {
            if ($invoice['status'] != InvoiceStatusConstants::INVOICE_PENDING) {
                throw new \Common\Exception\LocalOperationException('发票ID为' . $invoice['id'] . '的记录状态不允许提交');
            }
        }

        // 获取用户OA信息
        $userOaInfo = $this->getUserOaInfo(session('userid'));

        // 准备API提交数据
        $apiData = [];
        foreach ($invoices as $invoice) {
            // 准备单条发票数据
            $apiData[] = [
                'userId' => intval($userOaInfo['userId']),
                'deptId' => intval($userOaInfo['deptId']),
                'submitName' => $userOaInfo['workId'] . '#' . $userOaInfo['name'],
                'submitDept' => $userOaInfo['deptName'],
                'no' => $invoice['no'],
                'orderNum' => $invoice['oa_id'],
                'invoiceTitle' => $invoice['invoice_title'],
                'buyerTaxNum' => $invoice['buyer_tax_num'],
                'amount' => floatval($invoice['amount']),
                'buyerPhone' => $invoice['buyer_phone'],
                'buyerEmail' => $invoice['buyer_email'],
                'buyerAddress' => $invoice['buyer_address'],
                'buyerAccount' => $invoice['buyer_account'],
                'buyerAccountName' => $invoice['buyer_account_name'],
                'salerCompany' => $invoice['saler_company'],
                'goodsInfo' => $invoice['goods_info'],
                'invoiceType' => $invoice['invoice_type'],
                'submitReturnApi' => $this->getCallbackUrl($invoice['no']),
                'remark' => $invoice['remark']
            ];
        }

        // 开始事务
        $invoiceModel->startTrans();

        try {
            // 调用API提交发票
            $result = $this->post($apiData);

            // 如果提交成功，更新发票状态
            if ($result['status']) {
                // 更新所有发票状态为等待开票
                $updateResult = $invoiceModel->where(['id' => ['in', $invoiceIds]])->save([
                    'status' => InvoiceStatusConstants::INVOICE_WAITING,
                    'update_time' => time()
                ]);

                if ($updateResult === false) {
                    // 数据库更新失败，抛出本地操作异常
                    $invoiceModel->rollback();
                    throw new \Common\Exception\LocalOperationException('更新发票状态失败: ' . $invoiceModel->getError());
                }

                // 更新关联的查账记录的发票状态
                $this->updateRelatedAuditAccountInvoiceStatus($invoiceIds);

                // 提交事务
                $invoiceModel->commit();

                return [
                    'status' => true,
                    'message' => '发票提交成功',
                    'data' => $result['data']
                ];
            } else {
                // 提交失败，回滚事务
                $invoiceModel->rollback();

                // 抛出远程API异常，让调用者处理
                throw new \Common\Exception\RemoteApiException(
                    isset($result['msg']) ? $result['msg'] : '发票提交到OA系统失败'
                );
            }
        } catch (\Common\Exception\RemoteApiException $e) {
            // 远程API异常，回滚事务并向上传递异常
            $invoiceModel->rollback();
            throw $e;
        } catch (\Common\Exception\LocalOperationException $e) {
            // 本地操作异常，回滚事务并向上传递异常
            $invoiceModel->rollback();
            throw $e;
        } catch (\Exception $e) {
            // 其他未预期的异常，回滚事务并转换为本地操作异常
            $invoiceModel->rollback();
            throw new \Common\Exception\LocalOperationException('发票提交过程中发生错误: ' . $e->getMessage());
        }
    }
}
