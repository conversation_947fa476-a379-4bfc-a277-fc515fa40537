<?php

namespace OA\Service;

/**
 * OA汇款查询服务类
 * 处理查账相关的业务逻辑
 */
class RemittanceService extends BaseService
{


    /**
     * 通用汇款数据提交方法
     * 
     * @param array $data 汇款数据
     * @param string $type 提交类型：'online'(在线支付) 或 'offline'(线下转账)
     * @return array 提交结果
     */
    public function submitRemitData($data, $type = 'online')
    {
        // 验证参数
        $validateResult = $this->validate(
            $data,
            array(
                'userId' => array(
                    'required' => true,
                    'message' => '用户ID不能为空',
                ),
                'deptId' => array(
                    'required' => true,
                    'message' => '部门ID不能为空',
                ),
                'submitName' => array(
                    'required' => true,
                    'message' => '提交人姓名不能为空',
                ),
                'submitDept' => array(
                    'required' => true,
                    'message' => '提交部门不能为空',
                ),
                'orderNum' => array(
                    'required' => true,
                    'message' => '订单号不能为空',
                ),
                'confName' => array(
                    'required' => true,
                    'message' => '会议名称不能为空',
                ),
                'remitterInfo' => array(
                    'required' => true,
                    'message' => '汇款人信息不能为空',
                ),
                'amount' => array(
                    'required' => true,
                    'message' => '金额不能为空',
                    'validator' => function ($value) {
                        return is_numeric($value) && floatval($value) > 0;
                    },
                    'validator_message' => '金额必须为大于0的数字',
                ),
                'unit' => array(
                    'required' => true,
                    'message' => '货币单位不能为空',
                ),
                'remitTime' => array(
                    'required' => true,
                    'message' => '汇款时间不能为空',
                    'validator' => function ($value) {
                        return preg_match('/^\d{4}-\d{2}-\d{2}/', $value);
                    },
                    'validator_message' => '汇款时间格式不正确，应为YYYY-MM-DD格式',
                ),
                'account' => array(
                    'required' => true,
                    'message' => '账户不能为空',
                ),
                'callbackUrl' => array(
                    'required' => true,
                    'message' => '回调地址不能为空',
                ),
            )
        );

        if (!$validateResult['status']) {
            throw new \Common\Exception\LocalOperationException($validateResult['msg']);
        }


        // 构建请求数据前先清理原始数据，移除所有换行符和回车符等特殊字符
        $cleanData = $this->sanitizeData($data);
        
        $requestData = array(
            'no' => null,
            'userId' => intval($cleanData['userId']), //提交用户的信息 必填
            'deptId' => intval($cleanData['deptId']), //提交用户的信息 必填
            'submitName' => $cleanData['submitName'], //提交用户的信息 必填
            'submitDept' => $cleanData['submitDept'], //提交用户的信息 必填
            'orderNum' => $cleanData['orderNum'], // OA 生成的订单号，必填
            'confName' => $cleanData['confName'], // 会议简称 必填 
            'paperCode' => isset($cleanData['paperCode']) ? $cleanData['paperCode'] : null, // 文章编号 必填
            'creditSerial' => isset($cleanData['creditSerial']) ? $cleanData['creditSerial'] : null,
            'remitterInfo' => $cleanData['remitterInfo'], //汇款人信息 必填
            'amount' => floatval($cleanData['amount']), // 付款金额，必须为大于0 ，保留两位小数，比如400.32
            'unit' => $cleanData['unit'], // 付款货币  RMB USD 等
            'remitTime' => $cleanData['remitTime'], // 付款时间，必填 年月日
            'account' => $cleanData['account'], //  收款账户 必填
            'remark' => isset($cleanData['remark']) ? $cleanData['remark'] : null, // 备注，可以为空
            'annex' => isset($cleanData['annex']) ? $cleanData['annex'] : null, //查账截图附件 可以为空
            'callbackUrl' =>  $cleanData['callbackUrl'], //回调地址 必填
            'receipt' => isset($cleanData['receipt']) ? $cleanData['receipt'] : array(), // 发票信息可以为空，但必须为数组
            // 2025年4月9日 新增 annexList 信息可以为空，但必须为数组格式
            'annexList' => isset($cleanData['annexList']) ? $cleanData['annexList'] : array(),
        );
        
        // 最后再次确保整个请求数据没有特殊字符
        $requestData = $this->sanitizeData($requestData);

        // 根据类型确定API路径
        $apiPath = ($type == 'online') ? 'addRemitData/online' : 'addRemitData/offline';

        // 调用API提交汇款数据
        $result = send_oa_api_request($apiPath, $requestData);

        // 处理API响应
        if (!isset($result['code']) || $result['code'] != 200) {
            $errorMessage = isset($result['msg']) ? $result['msg'] : '远程API请求失败或返回错误';
            throw new \Common\Exception\RemoteApiException($errorMessage);
        }

        return $result;
    }
}
