<extend name="public:guest_mini" />

<block name="title">
    <title>电子发票快速申请通道 <?php echo SITE_FULL_NAME ?></title>
</block>

<block name="main">
    <div class="container">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="font-weight-bold">
                    <i class="fas fa-file-invoice-dollar mr-2"></i> 电子发票快速申请
                </h2>
            </div>
            <div class="col-12 text-center my-3">
                <!-- 进度条 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-tasks mr-2"></i>申请流程</h5>
                    </div>
                    <div class="card-body">
                        <div class="holder">
                            <ul class="SteppedProgress">
                                <li class="complete"><span>验证订单</span></li>
                                <li><span>填写开票信息</span></li>
                                <li><span>等待开票</span></li>
                                <li><span>查看结果</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-12">
                <!-- 订单输入卡片 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <h4>输入您的订单号</h4>
                            <p class="text-muted">请输入您的支付订单号码以申请发票</p>
                        </div>

                        <form id="orderForm">
                            <div class="form-group">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                            <i class="fas fa-receipt"></i>
                                        </span>
                                    </div>
                                    <input type="text"
                                        class="form-control"
                                        id="order_id"
                                        name="order_id"
                                        required
                                        placeholder="请输入订单号"
                                        value="{$order_id}">
                                </div>
                                <small class="form-text text-muted mt-2">
                                    您可以在支付确认邮件中找到订单号
                                </small>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg px-4">
                                    <i class="fas fa-search mr-1"></i> 开始验证
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 重要提示卡片 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-info-circle mr-2"></i>重要提示</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-3 d-flex">
                                <i class="fas fa-info-circle text-info mt-1 mr-3"></i>
                                <span>本页面适用于未登录情况下，快速付款的用户</span>
                            </li>

                            <li class="mb-3 d-flex">
                                <i class="fas fa-info-circle text-info mt-1 mr-3"></i>
                                <span>只有已关联到会议的订单才能申请发票，若有疑问，请联系会议秘书</span>
                            </li>
                            <li class="d-flex">
                                <i class="fas fa-info-circle text-info mt-1 mr-3"></i>
                                <span>目前仅支持人民币支付的发票申请</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- 查询状态链接 -->
                <div class="text-center mt-4 mb-4">
                    <p class="mb-2">已经申请过发票？</p>
                    <a href="{:U('Home/GuestInvoice/status')}" class="btn btn-outline-primary btn-lg px-4">
                        <i class="fas fa-search mr-1"></i> 查询发票状态
                    </a>
                </div>
            </div>
        </div>
    </div>
</block>

<block name="scripts">
    <script>
        $(document).ready(function() {
            $('#orderForm').on('submit', function(e) {
                e.preventDefault();
                const orderId = $('#order_id').val();

                if (!orderId) {
                    Swal.fire({
                        title: '错误',
                        text: '请输入订单号',
                        icon: 'error',
                        confirmButtonText: '确定'
                    });
                    return;
                }

                // 验证订单
                $.ajax({
                    url: '{:U("Home/GuestInvoice/validateOrder")}',
                    type: 'POST',
                    data: { order_id: orderId },
                    success: function(response) {
                        if (response.status === 1) {
                            // 验证成功
                            Swal.fire({
                                title: '验证成功',
                                text: '订单信息验证通过，是否继续申请发票？',
                                icon: 'success',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: '继续申请',
                                cancelButtonText: '取消'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    // 创建隐藏表单并提交
                                    var form = $('<form action="{:U("Home/GuestInvoice/query")}" method="post">' +
                                        '<input type="hidden" name="order_id" value="' + orderId + '">' +
                                        '<input type="hidden" name="validated_data" value=\'' + JSON.stringify(response.data) + '\'>' +
                                        '</form>');
                                    $('body').append(form);
                                    form.submit();
                                }
                            });
                        } else {
                            // 验证失败
                            Swal.fire({
                                title: '错误',
                                text: response.info || '订单验证失败',
                                icon: 'error',
                                confirmButtonText: '确定'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: '错误',
                            text: '网络错误，请稍后重试',
                            icon: 'error',
                            confirmButtonText: '确定'
                        });
                    }
                });
            });
        });
    </script>
</block>
