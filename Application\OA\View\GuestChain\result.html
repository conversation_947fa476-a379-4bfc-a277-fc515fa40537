<extend name="Mpanel@Base/admin_base" />
<block name="title">
    <title>（游客付款）查账结果</title>
</block>
<block name="breadcrumb">
    <ol class="breadcrumb">
        <li><a href="{:U('Mpanel/Index/index')}"><i class="fa fa-home"></i> 首页</a></li>
        <li class="active"><i class="fa fa-check-circle"></i> 查账结果</li>
    </ol>
</block>

<block name="content">
    <link rel="stylesheet" href="/Public/statics/css/steps_admin_enhanced.css">
    <div class="container-fluid">
        <!-- 步骤指示器 -->
        <div class="row">
            <div class="col-md-12">
                <ul class="admin-stepped-progress">
                    <li class="admin-complete"><span>确认付款信息</span></li>
                    <li class="admin-complete"><span>确认查账和发票信息</span></li>
                    <li class="admin-current"><span>提交到OA</span></li>
                </ul>
            </div>
        </div>

        <!-- 操作结果 -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title"><i class="fa fa-check-circle"></i> 操作结果</h4>
                    </div>
                    <div class="panel-body">
                        <div class="alert alert-success">
                            <p><i class="fa fa-check-circle"></i> 一键查账和开票操作已成功提交！请关注OA处理的结果</p>

                        </div>

                        <!-- 查账信息 -->
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h5 class="panel-title"><i class="fa fa-search"></i> 查账信息</h5>
                            </div>
                            <div class="panel-body">
                                <empty name="auditRecord">
                                    <div class="alert alert-warning">
                                        <p><i class="fa fa-exclamation-triangle"></i> 未找到查账记录，请稍后刷新页面查看。</p>
                                    </div>
                                    <else />
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-bordered table-striped">
                                                <tr>
                                                    <th width="30%">查账状态：</th>
                                                    <td>
                                                        <php>
                                                            // 使用OaAuditStatusConstants类获取状态文本和样式
                                                            $statusText = \Common\Lib\OaAuditStatusConstants::getStatusText($auditRecord['status']);
                                                            $statusClass = \Common\Lib\OaAuditStatusConstants::getStatusClass($auditRecord['status']);
                                                            echo '<span class="label label-'.$statusClass.'">'.$statusText.'</span>';
                                                        </php>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>OA订单号：</th>
                                                    <td>{$auditRecord.oa_id|default='暂无'}</td>
                                                </tr>
                                                <tr>
                                                    <th>会议名称：</th>
                                                    <td>{$auditRecord.conf_name|default='暂无'}</td>
                                                </tr>
                                                <tr>
                                                    <th>文章ID：</th>
                                                    <td>{$auditRecord.paper_code|default='暂无'}</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-bordered table-striped">
                                                <tr>
                                                    <th width="30%">金额：</th>
                                                    <td><span class="text-danger">{$auditRecord.total|default='0.00'}</span> {$auditRecord.unit|default='CNY'}</td>
                                                </tr>
                                                <tr>
                                                    <th>汇款时间：</th>
                                                    <td>{$auditRecord.remit_time|default='暂无'}</td>
                                                </tr>
                                                <tr>
                                                    <th>提交时间：</th>
                                                    <td>{$auditRecord.create_time_text|default='暂无'}</td>
                                                </tr>
                                                <tr>
                                                    <th>更新时间：</th>
                                                    <td>{$auditRecord.update_time_text|default='暂无'}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </empty>
                            </div>
                        </div>

                        <!-- 发票信息 -->
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h5 class="panel-title"><i class="fa fa-file-text-o"></i> 发票信息</h5>
                            </div>
                            <div class="panel-body">
                                <empty name="invoices">
                                    <div class="alert alert-info">
                                        <p><i class="fa fa-info-circle"></i> 未找到发票记录，可能是您选择了只执行查账操作，或者发票尚未创建。</p>
                                    </div>
                                    <else />
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th width="5%">#</th>
                                                    <th width="15%">发票抬头</th>
                                                    <th width="15%">纳税人识别号</th>
                                                    <th width="10%">发票类型</th>
                                                    <th width="10%">金额</th>
                                                    <th width="15%">发票项目</th>
                                                    <th width="10%">状态</th>
                                                    <th width="20%">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <foreach name="invoices" item="invoice" key="k">
                                                    <tr>
                                                        <td>{$k+1}</td>
                                                        <td>{$invoice.invoice_title}</td>
                                                        <td>{$invoice.buyer_tax_num}</td>
                                                        <td>
                                                            <switch name="invoice.invoice_type">
                                                                <case value="pc"><span class="label label-info">普通发票</span></case>
                                                                <case value="bs"><span class="label label-primary">专用发票</span></case>
                                                                <default><span class="label label-default">{$invoice.invoice_type}</span></default>
                                                            </switch>
                                                        </td>
                                                        <td><span class="text-danger">{$invoice.amount}</span></td>
                                                        <td>
                                                            <php>
                                                                $goodsInfoText = $invoice['goods_info'];
                                                                // 获取发票项目选项
                                                                $invoiceItemService = new \Common\Service\InvoiceItemService();
                                                                $invoiceItemOptions = $invoiceItemService->getInvoiceItemOptions();

                                                                // 检查是否是键值形式
                                                                if (isset($invoiceItemOptions[$invoice['goods_info']])) {
                                                                    $goodsInfoText = $invoiceItemOptions[$invoice['goods_info']];
                                                                }
                                                                echo $goodsInfoText;
                                                            </php>
                                                        </td>
                                                        <td>
                                                            <php>
                                                                // 使用InvoiceStatusConstants类获取状态文本和样式
                                                                $statusText = \Common\Lib\InvoiceStatusConstants::getStatusText($invoice['status']);
                                                                $statusClass = \Common\Lib\InvoiceStatusConstants::getStatusClass($invoice['status']);
                                                                echo '<span class="label label-'.$statusClass.'">'.$statusText.'</span>';
                                                            </php>
                                                        </td>
                                                        <td>
                                                            <if condition="$invoice.status eq 30">
                                                                <a href="{:U('OA/Invoice/download', array('id' => $invoice['id']))}" class="btn btn-xs btn-success" target="_blank">
                                                                    <i class="fa fa-download"></i> 下载
                                                                </a>
                                                            </if>
                                                            <a href="{:U('OA/Invoice/detail', array('id' => $invoice['id']))}" class="btn btn-xs btn-info" target="_blank">
                                                                <i class="fa fa-search"></i> 详情
                                                            </a>
                                                        </td>
                                                    </tr>
                                                </foreach>
                                            </tbody>
                                        </table>
                                    </div>
                                </empty>
                            </div>
                        </div>

                        <!-- 支付信息 -->
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h5 class="panel-title"><i class="fa fa-credit-card"></i> 支付信息</h5>
                            </div>
                            <div class="panel-body">
                                <empty name="payInfo">
                                    <div class="alert alert-warning">
                                        <p><i class="fa fa-exclamation-triangle"></i> 未找到支付信息。</p>
                                    </div>
                                    <else />
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-bordered table-striped">
                                                <tr>
                                                    <th width="30%">订单号：</th>
                                                    <td>{$payInfo.orderid|default='暂无'}</td>
                                                </tr>
                                                <tr>
                                                    <th>金额：</th>
                                                    <td><span class="text-danger">{$payInfo.total|default='0.00'}</span>
                                                        <eq name="payInfo.moneytype" value="0">CNY<else />USD</eq>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>支付状态：</th>
                                                    <td>
                                                        <php>
                                                            // 使用OnlinePaymentStatusConstants类获取状态文本和样式
                                                            $statusText = \Common\Lib\OnlinePaymentStatusConstants::getStatusText($payInfo['status']);
                                                            $statusClass = \Common\Lib\OnlinePaymentStatusConstants::getStatusClass($payInfo['status']);
                                                            echo '<span class="label label-'.$statusClass.'">'.$statusText.'</span>';
                                                        </php>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-bordered table-striped">
                                                <tr>
                                                    <th width="30%">支付时间：</th>
                                                    <td>{$payInfo.ordetime|date='Y-m-d H:i:s',###}</td>
                                                </tr>
                                                <tr>
                                                    <th>支付方式：</th>
                                                    <td>
                                                        在线支付
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>支付人：</th>
                                                    <td>{$payInfo.fullname|default='暂无'}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </empty>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</block>
