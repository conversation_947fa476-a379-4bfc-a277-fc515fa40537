<?php

namespace Home\Controller;

use Think\Controller;
use Common\Lib\InvoiceStatusConstants as Status;

/**
 * 游客发票申请控制器
 * 不需要登录即可访问
 */
class GuestInvoiceController extends Controller
{
    /**
     * 初始化方法
     */
    public function _initialize()
    {
        // 加载发票服务类
        $this->invoiceService = D('Home/Invoice', 'Service');
        // 加载发票项目服务类
        $this->invoiceItemService = new \Common\Service\InvoiceItemService();
        // 加载支付同步服务类
        $this->paymentSyncService = new \Home\Service\PaymentSyncService();
        // 加载发票策略服务类
        $this->invoiceStrategyService = new \Home\Service\InvoiceStrategyService();
        // 加载发票资格判断服务类
        $this->invoiceEligibilityService = new \Common\Service\InvoiceEligibilityService();
    }

    /**
     * 游客发票申请入口页面
     * 显示订单号输入表单
     * @param string $order_id 可选，从URL传入的订单号
     */
    public function index()
    {
        // 获取URL中传递的订单号参数
        $order_id = I('get.order_id');
        
        // 如果存在订单号，传递给视图
        if (!empty($order_id)) {
            $this->assign('order_id', $order_id);
        }
        
        $this->display();
    }



    /**
     * 准备发票类型和发票项目数据
     * 用于在视图中显示
     * @return array 包含发票类型和发票项目数据的数组
     */
    private function prepareInvoiceFormData()
    {
        // 准备发票类型数据
        $invoiceTypeData = [];
        $invoiceTypes = \Common\Lib\InvoiceTypeConstants::getAllTypes();
        foreach ($invoiceTypes as $type) {
            $invoiceTypeData[$type] = [
                'value' => $type,
                'text' => \Common\Lib\InvoiceTypeConstants::getTypeText($type),
                'description' => \Common\Lib\InvoiceTypeConstants::getTypeDescription($type),
                'class' => \Common\Lib\InvoiceTypeConstants::getTypeClass($type)
            ];
        }

        // 准备发票项目数据
        $invoiceItemOptions = $this->invoiceItemService->getInvoiceItemOptions();

        // 准备JavaScript使用的数据
        $invoiceTypeMapJS = [];
        foreach ($invoiceTypes as $type) {
            $invoiceTypeMapJS[] = "$type: '".\Common\Lib\InvoiceTypeConstants::getTypeText($type)."'";
        }
        $invoiceTypeMapJSString = implode(",\n                    ", $invoiceTypeMapJS);

        // 准备发票项目映射的JavaScript数据
        $invoiceItemMapJS = [];
        foreach ($invoiceItemOptions as $key => $text) {
            $invoiceItemMapJS[] = "'$key': '$text'";
        }
        $invoiceItemMapJSString = implode(",\n                ", $invoiceItemMapJS);

        return [
            'invoiceTypeData' => $invoiceTypeData,
            'invoiceItemOptions' => $invoiceItemOptions,
            'invoiceTypeMapJSString' => $invoiceTypeMapJSString,
            'invoiceItemMapJSString' => $invoiceItemMapJSString
        ];
    }

    /**
     * 处理订单查询并显示发票申请表单
     * Ajax验证订单状态
     */
    public function validateOrder()
    {
        if (!IS_AJAX) {
            $this->error('非法请求');
        }

        $order_id = I('post.order_id');
        if (empty($order_id)) {
            $this->ajaxReturn(['status' => 0, 'info' => '订单号不能为空']);
        }

        // 去除订单号中的所有空格
        $order_id = str_replace(' ', '', $order_id);

        // 使用支付同步服务获取完整的订单信息
        $result = $this->paymentSyncService->syncOrderInfo($order_id);

        if (!$result['success']) {
            $this->ajaxReturn(['status' => 0, 'info' => $result['message']]);
        }

        $orderInfo = $result['data'];
        if (empty($orderInfo)) {
            $this->ajaxReturn(['status' => 0, 'info' => '未找到订单信息']);
        }

        // 检查是否全额退款
        $total = floatval($orderInfo['total']);
        $refund_total = floatval($orderInfo['refund_total']);
        $refund_count = intval($orderInfo['refund_count']);

        // 检查是否全额退款
        if ($refund_count > 0 && $refund_total >= $total * 0.999) {
            \Think\Log::record('游客订单全额退款检查: ' . $order_id . ', 订单总额=' . $total . ', 退款金额=' . $refund_total, 'INFO');

            // 更新数据库中的标记
            $model_pay = M('subPay');
            $updateData = [
                'is_refund' => 1,
                'refund_total' => number_format($refund_total, 2, '.', ''),
                'invoice_total' => '0.00',
                'refund_count' => $refund_count,
                'is_full_refund' => 1
            ];

            $result = $model_pay->where(['orderid' => $order_id])->save($updateData);
            \Think\Log::record('游客订单全额退款标记更新结果: ' . ($result !== false ? '成功' : '失败') . ', 订单号: ' . $order_id, 'INFO');

            // 返回全额退款的数据
            $this->ajaxReturn([
                'status' => 0,
                'info' => 'This order has been fully refunded and cannot be invoiced',
                'data' => [
                    'order_id' => $order_id,
                    'total' => number_format($total, 2, '.', ''),
                    'refund_total' => number_format($refund_total, 2, '.', ''),
                    'invoice_total' => '0.00',
                    'refund_count' => $refund_count,
                    'is_full_refund' => 1,
                    'is_refund' => 1
                ]
            ]);
            return;
        }

        // 计算可开票金额
        $invoice_total = $total - $refund_total;
        $invoice_total = max(0, $invoice_total); // 确保不会出现负数
        $invoice_total = number_format($invoice_total, 2, '.', '');

        // 更新数据库
        $model_pay = M('subPay');
        $updateData = [
            'is_refund' => $refund_count > 0 ? 1 : 0,
            'refund_total' => number_format($refund_total, 2, '.', ''),
            'invoice_total' => $invoice_total,
            'refund_count' => $refund_count,
            'is_full_refund' => 0
        ];

        $result = $model_pay->where(['orderid' => $order_id])->save($updateData);
        \Think\Log::record('游客订单状态更新结果: ' . ($result !== false ? '成功' : '失败') . ', 订单号: ' . $order_id, 'INFO');

        // 返回成功数据
        $this->ajaxReturn([
            'status' => 1,
            'info' => 'Order validation successful',
            'data' => [
                'order_id' => $order_id,
                'total' => number_format($total, 2, '.', ''),
                'refund_total' => number_format($refund_total, 2, '.', ''),
                'invoice_total' => $invoice_total,
                'refund_count' => $refund_count,
                'is_full_refund' => 0,
                'is_refund' => $refund_count > 0 ? 1 : 0
            ]
        ]);
    }

    /**
     * 处理订单查询并显示发票申请表单
     * 支持POST和GET请求，GET请求用于"继续开票"功能
     */
    public function query()
    {
        try {
            // 优先从POST获取订单号，如果没有则从GET获取（用于"继续开票"功能）
            $order_id = I('post.order_id');
            if (empty($order_id)) {
                $order_id = I('get.order_id');
            }

            $is_validated = I('post.is_validated');
            $validated_data = I('post.validated_data');

            if (empty($order_id)) {
                $this->error('Please enter a valid order number');
            }

            // 去除订单号中的所有空格
            $order_id = str_replace(' ', '', $order_id);

            // 如果有验证数据，直接使用
            if ($is_validated && !empty($validated_data)) {
                \Think\Log::record('使用验证数据: ' . $validated_data, 'DEBUG');
                $validated_data = json_decode($validated_data, true);
                if (!empty($validated_data)) {
                    \Think\Log::record('解析验证数据成功: ' . json_encode($validated_data, JSON_UNESCAPED_UNICODE), 'DEBUG');
                    // 构建发票数据
                    $invoice_data = [
                        'pay_info' => [
                            'total' => $validated_data['total'],
                            'refund_total' => $validated_data['refund_total'],
                            'invoice_total' => $validated_data['invoice_total'],
                            'refund_count' => $validated_data['refund_count'],
                            'orderid' => $validated_data['order_id']
                        ],
                        'remaining_amount' => $validated_data['invoice_total']
                    ];

                    // 使用服务类获取发票项目选项
                    $invoice_item_options = $this->invoiceItemService->getInvoiceItemOptions();

                    // 将所有数据传递给视图
                    $this->assign('pay_info', $invoice_data['pay_info']);
                    $this->assign('invoice_history', []);
                    $this->assign('total_invoiced', '0.00');
                    $this->assign('remaining_amount', $invoice_data['remaining_amount']);
                    $this->assign('is_guest', true);
                    $this->assign('invoice_item_options', $invoice_item_options);

                    $this->display('form');
                    return;
                } else {
                    \Think\Log::record('解析验证数据失败', 'WARN');
                }
            } else {
                \Think\Log::record('未找到验证数据: is_validated=' . ($is_validated ? 'true' : 'false'), 'DEBUG');
            }

            // 使用支付同步服务获取完整的订单信息，包括同步最新状态和计算退款金额
            try {
                $invoice_data = $this->paymentSyncService->getCompleteOrderInfo($order_id, true);
                \Think\Log::record('游客订单信息获取成功: ' . json_encode($invoice_data['pay_info'], JSON_UNESCAPED_UNICODE), 'DEBUG');
            } catch (\Exception $e) {
                \Think\Log::record('游客订单信息获取失败: ' . $e->getMessage(), 'ERROR');
                $this->error($e->getMessage());
                return;
            }

            // 获取订单基本信息
            $total = floatval($invoice_data['pay_info']['total']);
            $refund_total = isset($invoice_data['pay_info']['refund_total']) ? floatval($invoice_data['pay_info']['refund_total']) : 0;
            $refund_count = isset($invoice_data['pay_info']['refund_count']) ? intval($invoice_data['pay_info']['refund_count']) : 0;
            $is_refund = isset($invoice_data['pay_info']['is_refund']) ? intval($invoice_data['pay_info']['is_refund']) : 0;

            // 检查是否全额退款
            if ($refund_count > 0) {
                // 如果退款金额接近或等于订单总金额，视为全额退款
                if ($refund_total >= $total * 0.999) {
                    \Think\Log::record('游客订单全额退款检查: ' . $order_id . ', 订单总额=' . $total . ', 退款金额=' . $refund_total, 'INFO');

                    // 更新数据库中的标记
                    $model_pay = M('subPay');
                    $updateData = [
                        'is_refund' => 1,
                        'refund_total' => number_format($refund_total, 2, '.', ''),
                        'invoice_total' => '0.00',
                        'refund_count' => $refund_count,
                        'is_full_refund' => 1
                    ];

                    $model_pay->where(['orderid' => $order_id])->save($updateData);
                    \Think\Log::record('游客订单全额退款标记已更新: ' . $order_id, 'INFO');

                    $this->error('This order has been fully refunded and cannot be invoiced');
                } else {
                    // 部分退款 - 更新退款信息，计算可开票金额
                    $invoice_total = $total - $refund_total;
                    $model_pay = M('subPay');
                    $updateData = [
                        'is_refund' => 1,
                        'refund_total' => number_format($refund_total, 2, '.', ''),
                        'invoice_total' => number_format($invoice_total, 2, '.', ''),
                        'refund_count' => $refund_count,
                        'is_full_refund' => 0
                    ];

                    $model_pay->where(['orderid' => $order_id])->save($updateData);
                    \Think\Log::record('游客订单部分退款更新: ' . $order_id . ', 退款金额=' . $refund_total . ', 可开票金额=' . $invoice_total, 'INFO');

                    // 更新内存中的数据
                    $invoice_data['pay_info']['invoice_total'] = number_format($invoice_total, 2, '.', '');
                    $invoice_data['remaining_amount'] = number_format($invoice_total - floatval($invoice_data['total_invoiced']), 2, '.', '');
                }
            }

            // 3. 检查invoice_total是否为0
            if (isset($invoice_data['pay_info']['invoice_total']) && floatval($invoice_data['pay_info']['invoice_total']) <= 0) {
                // 如果invoice_total为0，但订单总金额大于0且没有退款记录，可能是invoice_total字段未被正确设置
                // 在这种情况下，尝试重新计算并更新invoice_total
                $total = floatval($invoice_data['pay_info']['total']);
                $refund_total = isset($invoice_data['pay_info']['refund_total']) ? floatval($invoice_data['pay_info']['refund_total']) : 0;
                $is_refund = isset($invoice_data['pay_info']['is_refund']) ? $invoice_data['pay_info']['is_refund'] : 0;

                if ($total > 0 && ($refund_total <= 0 || $refund_total < $total * 0.999)) {
                    // 计算可开票金额 = 订单总金额 - 退款金额
                    $invoice_total = $total - $refund_total;

                    if ($invoice_total > 0) {
                        // 更新数据库
                        $model_pay = M('subPay');
                        $updateData = [
                            'invoice_total' => number_format($invoice_total, 2, '.', '')
                        ];

                        if ($is_refund == 1) {
                            $updateData['is_refund'] = 1;
                            $updateData['refund_total'] = number_format($refund_total, 2, '.', '');
                        }

                        $model_pay->where(['orderid' => $order_id])->save($updateData);

                        \Think\Log::record('修复游客订单可开票金额: ' . $order_id . ', 订单总额=' . $total .
                            ', 退款金额=' . $refund_total . ', 可开票金额=' . $invoice_total, 'INFO');

                        // 更新内存中的数据
                        $invoice_data['pay_info']['invoice_total'] = number_format($invoice_total, 2, '.', '');
                        $invoice_data['remaining_amount'] = number_format($invoice_total - floatval($invoice_data['total_invoiced']), 2, '.', '');
                    } else {
                        \Think\Log::record('游客订单可开票金额计算为0: ' . $order_id, 'INFO');
                        $this->error('This order has no available amount for invoice');
                    }
                } else {
                    \Think\Log::record('游客订单可开票金额为0: ' . $order_id, 'INFO');
                    $this->error('This order has no available amount for invoice');
                }
            }

            // 4. 使用发票资格判断服务检查订单是否可以申请发票
            $eligibilityResult = $this->invoiceEligibilityService->checkOnlinePaymentEligibility($invoice_data['pay_info']);

            if (!$eligibilityResult['eligible']) {
                \Think\Log::record('游客订单不符合发票申请资格: ' . $order_id . ', 原因: ' . $eligibilityResult['reason'], 'INFO');
                $this->error($eligibilityResult['reason']);
                return;
            }

            // 5. 使用发票策略服务检查是否可以继续申请发票
            $strategyResult = $this->invoiceStrategyService->checkCanApplyMore(
                $invoice_data['remaining_amount'],
                $invoice_data['invoice_history']
            );

            if (!$strategyResult['can_apply']) {
                \Think\Log::record('游客订单不符合发票策略要求: ' . $order_id . ', 原因: ' . $strategyResult['reason'], 'INFO');
                $this->error($strategyResult['reason']);
                return;
            }

            // 6. 检查可开票金额
            if (floatval($invoice_data['remaining_amount']) <= 0) {
                \Think\Log::record('游客订单剩余可开票金额为0: ' . $order_id, 'INFO');
                $this->error('订单金额已达到最大开票金额或已全额退款');
            }

            // 准备发票类型和发票项目数据
            $formData = $this->prepareInvoiceFormData();

            // 调试信息
            \Think\Log::record('游客发票项目选项: ' . json_encode($formData['invoiceItemOptions'], JSON_UNESCAPED_UNICODE), 'DEBUG');

            // 将所有数据传递给视图
            $this->assign('pay_info', $invoice_data['pay_info']);
            $this->assign('invoice_history', $invoice_data['invoice_history']);
            $this->assign('total_invoiced', $invoice_data['total_invoiced']);
            $this->assign('remaining_amount', $invoice_data['remaining_amount']);
            $this->assign('is_guest', true); // 标记为游客模式
            $this->assign('invoice_item_options', $formData['invoiceItemOptions']); // 传递开票项目选项
            $this->assign('invoice_type_data', $formData['invoiceTypeData']); // 传递发票类型数据
            $this->assign('invoice_type_map_js', $formData['invoiceTypeMapJSString']); // 传递发票类型映射JS字符串
            $this->assign('invoice_item_map_js', $formData['invoiceItemMapJSString']); // 传递发票项目映射JS字符串

            $this->display('form');
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 验证和处理发票申请表单数据
     * @param array $post 表单数据
     * @return array 处理结果
     * @throws \Exception 当验证失败或处理出错时抛出异常
     */
    private function validateAndProcessInvoiceForm($post)
    {
        if (empty($post)) {
            throw new \Exception('未收到表单数据，请填写完整的表单。');
        }

        // 检查必填字段
        $required_fields = ['order_id', 'invoice_title', 'buyer_tax_num', 'amount', 'goods_info', 'buyer_email', 'invoice_type'];
        $field_names = [
            'order_id' => '订单号',
            'invoice_title' => '发票抬头',
            'buyer_tax_num' => '税号',
            'amount' => '金额',
            'goods_info' => '发票内容',
            'buyer_email' => '邮箱地址',
            'invoice_type' => '发票类型'
        ];

        foreach ($required_fields as $field) {
            if (empty($post[$field])) {
                throw new \Exception("缺少必填字段：{$field_names[$field]}");
            }
        }

        // 增强输入过滤
        $post['invoice_title'] = htmlspecialchars(trim($post['invoice_title']));
        $post['buyer_tax_num'] = preg_replace('/[^0-9A-Z]/', '', $post['buyer_tax_num']);
        $post['goods_info'] = htmlspecialchars(trim($post['goods_info']));
        $post['buyer_email'] = filter_var(trim($post['buyer_email']), FILTER_SANITIZE_EMAIL);
        $post['is_guest'] = true; // 标记为游客申请

        // 如果是专票，检查专票必填字段
        if ($post['invoice_type'] == 'bs') {
            $special_fields = ['buyer_address', 'buyer_phone', 'buyer_bank', 'buyer_account'];
            $special_field_names = [
                'buyer_address' => '注册地址',
                'buyer_phone' => '注册电话',
                'buyer_bank' => '开户银行',
                'buyer_account' => '银行账号'
            ];

            foreach ($special_fields as $field) {
                if (empty($post[$field])) {
                    throw new \Exception("缺少专票必填字段：{$special_field_names[$field]}");
                }
            }
        }

        // 使用模型处理发票申请
        $model = D('MemberOaInvoice');
        $result = $model->addNewGuestPay($post);

        return $result;
    }

    /**
     * 保存游客发票申请
     */
    public function save()
    {
        try {
            $post = I('post.');
            $result = $this->validateAndProcessInvoiceForm($post);

            // 处理返回结果
            if ($result['status'] == 1) {
                // 成功提交
                $this->success($result['info'], U('Home/GuestInvoice/successPage', ['id' => $result['id']]));
            } else {
                // 提交失败
                $this->error($result['info']);
            }
        } catch (\Exception $e) {
            // 异常处理
            $this->error('发票申请失败: ' . $e->getMessage());
        }
    }

    /**
     * AJAX方式保存游客发票申请信息
     * 返回JSON格式的响应，而不是重定向
     */
    public function saveAjax()
    {
        // 禁止直接访问该方法
        if (!IS_AJAX) {
            $this->ajaxReturn(['status' => 0, 'message' => '非法请求']);
            return;
        }

        try {
            $post = I('post.');
            $result = $this->validateAndProcessInvoiceForm($post);

            // 处理返回结果
            if ($result['status'] == 1) {
                // 记录日志
                \Think\Log::record('游客发票申请成功（AJAX），ID: ' . $result['id'], 'INFO');

                // 直接返回成功响应，不再重新查询订单信息
                $this->ajaxReturn([
                    'status' => 1,
                    'message' => $result['info'],
                    'invoice_id' => $result['id'],
                    'order_id' => $post['order_id']
                ]);
            } else {
                // 返回失败响应
                $this->ajaxReturn(['status' => 0, 'message' => $result['info']]);
            }
        } catch (\Exception $e) {
            // 异常处理
            \Think\Log::record('游客发票申请异常: ' . $e->getMessage(), 'ERROR');
            $this->ajaxReturn(['status' => 0, 'message' => '发票申请失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 发票申请成功页面
     */
    public function successPage()
    {
        $invoice_id = I('get.id', 0, 'intval');

        if (empty($invoice_id)) {
            $this->error('无效的发票申请ID');
        }

        // 获取发票申请信息
        $model = D('MemberOaInvoice');
        $invoice = $model->where(['id' => $invoice_id])->find();

        if (empty($invoice)) {
            $this->error('未找到发票申请记录');
        }

        $this->assign('invoice', $invoice);
        $this->display();
    }

    /**
     * 查询发票申请状态入口
     */
    public function status()
    {
        $this->display();
    }

    /**
     * 处理发票状态查询
     */
    public function checkStatus()
    {
        try {
            $order_id = I('post.order_id');
            $email = I('post.email');

            if (empty($order_id) || empty($email)) {
                $this->error('Please enter both order number and email');
            }

            // 查询发票申请记录
            $model = D('MemberOaInvoice');
            $invoices = $model->where([
                'order_id' => $order_id,
                'buyer_email' => $email
            ])->select();

            if (empty($invoices)) {
                $this->error('No invoice applications found with the provided information');
            }

            // 处理状态信息
            foreach ($invoices as &$invoice) {
                // 处理状态为0的特殊情况（历史数据兼容）
                if ($invoice['status'] === 0 || $invoice['status'] === '0') {
                    $invoice['status'] = Status::INVOICE_PENDING; // 转换为新的状态码
                }

                // 添加状态文本和样式类
                $invoice['status_text'] = Status::getStatusText($invoice['status']);
                $invoice['status_class'] = Status::getStatusClass($invoice['status']);
            }
            unset($invoice); // 释放引用

            $this->assign('invoices', $invoices);
            $this->display('statusResult');
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }
}