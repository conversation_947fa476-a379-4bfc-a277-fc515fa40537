<?php

namespace OA\Service;

/**
 * OA汇款查询服务类
 * 处理查账相关的业务逻辑
 */
class SendAuditToOAService extends BaseService
{
    /**
     * 获取回调URL
     * 从配置文件中获取当前环境的回调URL，不添加任何参数
     *
     * @return string 回调URL
     */
    protected function getCallbackUrl()
    {
        // 从配置文件中获取当前环境
        $environment = C('OA_ENVIRONMENT');

        // 从配置文件中获取回调URL
        $baseUrl = C('OA_CALLBACK_URLS.' . $environment);

        // 如果未配置回调URL，记录警告并返回空字符串
        if (empty($baseUrl)) {
            \Think\Log::write('未配置回调URL，当前环境：' . $environment, 'WARN');
            return '';
        }

        return $baseUrl;
    }


    /**
     * 通用汇款数据提交方法
     *
     * @param array $data 汇款数据
     * @param string $type 提交类型：'online'(在线支付) 或 'offline'(线下转账)，默认为线下转账
     * @return array 提交结果
     */
    public function send($data, $type = 'offline')
    {
        // 验证参数
        $validateResult = $this->validate(
            $data,
            array(
                'userId' => array(
                    'required' => true,
                    'message' => '用户ID不能为空',
                ),
                'deptId' => array(
                    'required' => true,
                    'message' => '部门ID不能为空',
                ),
                'submitName' => array(
                    'required' => true,
                    'message' => '提交人姓名不能为空',
                ),
                'submitDept' => array(
                    'required' => true,
                    'message' => '提交部门不能为空',
                ),
                'orderNum' => array(
                    'required' => true,
                    'message' => '订单号不能为空',
                ),
                'confName' => array(
                    'required' => true,
                    'message' => '会议名称不能为空',
                ),
                'paperCode' => array(
                    'required' => true,
                    'message' => '文章编号不能为空',
                ),
                'remitterInfo' => array(
                    'required' => true,
                    'message' => '汇款人信息不能为空',
                ),
                'amount' => array(
                    'required' => true,
                    'message' => '金额不能为空',
                    'validator' => function ($value) {
                        return is_numeric($value) && floatval($value) > 0;
                    },
                    'validator_message' => '金额必须为大于0的数字',
                ),
                'unit' => array(
                    'required' => true,
                    'message' => '货币单位不能为空',
                ),
                'remitTime' => array(
                    'required' => true,
                    'message' => '汇款时间不能为空',
                    'validator' => function ($value) {
                        return preg_match('/^\d{4}-\d{2}-\d{2}/', $value);
                    },
                    'validator_message' => '汇款时间格式不正确，应为YYYY-MM-DD格式',
                ),
                'account' => array(
                    'required' => true,
                    'message' => '查账的收款账户不能为空',
                ),
                'callbackUrl' => array(
                    'required' => true,
                    'message' => '回调地址不能为空',
                ),
            )
        );

        if (!$validateResult['status']) {
            throw new \Common\Exception\LocalOperationException($validateResult['msg']);
        }


        $requestData = array(
            'no' => null,
            'userId' => intval($data['userId']), //提交用户的信息 必填
            'deptId' => intval($data['deptId']), //提交用户的信息 必填
            'submitName' => $data['submitName'], //提交用户的信息 必填
            'submitDept' => $data['submitDept'], //提交用户的信息 必填
            'orderNum' => $data['orderNum'], // OA 生成的订单号，必填
            'confName' => str_replace(' ', '', $data['confName']), // 会议简称 必填
            'paperCode' => $data['paperCode'], // 文章编号 必填
            'creditSerial' => isset($data['creditSerial']) ? $data['creditSerial'] : null,
            'remitterInfo' => $data['remitterInfo'], //汇款人信息 必填
            'amount' => floatval($data['amount']), // 付款金额，必须为大于0 ，保留两位小数，比如400.32
            'unit' => $data['unit'], // 付款货币  RMB USD 等
            'remitTime' => $data['remitTime'], // 付款时间，必填 年月日
            'account' => $data['account'], //  收款账户 必填
            'remark' => isset($data['remark']) ? $data['remark'] : null, // 备注，可以为空
            'annex' => isset($data['annex']) ? $data['annex'] : null, //查账截图附件 可以为空
            'callbackUrl' =>  $this->getCallbackUrl(), //回调地址 必填
            'receipt' => isset($data['receipt']) ? $data['receipt'] : array(), // 发票信息可以为空，但必须为数组
            // 2025年4月9日 新增 annexList 信息可以为空，但必须为数组格式
            'annexList' => isset($data['annexList']) ? $data['annexList'] : array(),
        );
        // // 调试代码已移除
        // dump($requestData);
        // exit;

        // 根据类型确定API路径
        $apiPath = ($type == 'online') ? 'addRemitData/online' : 'addRemitData/offline';
        // dump($apiPath);
        // exit;


        // 调用API提交汇款数据
        $result = send_oa_api_request($apiPath, $requestData);

        // 处理API响应
        if (!isset($result['code']) || $result['code'] != 200) {
            $errorMessage = isset($result['msg']) ? $result['msg'] : '远程API请求失败或返回错误';
            throw new \Common\Exception\RemoteApiException($errorMessage);
        }

        return $result;
    }

    /**
     * 游客查账数据提交方法
     * 使用专用的游客查账API路径
     *
     * @param array $data 汇款数据
     * @return array 提交结果
     */
    public function sendGuest($data)
    {
        // 验证参数
        $validateResult = $this->validate(
            $data,
            array(
                'userId' => array(
                    'required' => true,
                    'message' => '用户ID不能为空',
                ),
                'deptId' => array(
                    'required' => true,
                    'message' => '部门ID不能为空',
                ),
                'submitName' => array(
                    'required' => true,
                    'message' => '提交人姓名不能为空',
                ),
                'submitDept' => array(
                    'required' => true,
                    'message' => '提交部门不能为空',
                ),
                'orderNum' => array(
                    'required' => true,
                    'message' => '订单号不能为空',
                ),
                'confName' => array(
                    'required' => true,
                    'message' => '会议名称不能为空',
                ),
                'paperCode' => array(
                    'required' => true,
                    'message' => '文章编号不能为空',
                ),
                'remitterInfo' => array(
                    'required' => true,
                    'message' => '汇款人信息不能为空',
                ),
                'amount' => array(
                    'required' => true,
                    'message' => '金额不能为空',
                    'validator' => function ($value) {
                        return is_numeric($value) && floatval($value) > 0;
                    },
                    'validator_message' => '金额必须为大于0的数字',
                ),
                'unit' => array(
                    'required' => true,
                    'message' => '货币单位不能为空',
                ),
                'remitTime' => array(
                    'required' => true,
                    'message' => '汇款时间不能为空',
                    'validator' => function ($value) {
                        return preg_match('/^\d{4}-\d{2}-\d{2}/', $value);
                    },
                    'validator_message' => '汇款时间格式不正确，应为YYYY-MM-DD格式',
                ),
                'account' => array(
                    'required' => true,
                    'message' => '查账的收款账户不能为空',
                ),
                'callbackUrl' => array(
                    'required' => true,
                    'message' => '回调地址不能为空',
                ),
            )
        );

        if (!$validateResult['status']) {
            throw new \Common\Exception\LocalOperationException($validateResult['msg']);
        }

        $requestData = array(
            'no' => null,
            'userId' => intval($data['userId']), //提交用户的信息 必填
            'deptId' => intval($data['deptId']), //提交用户的信息 必填
            'submitName' => $data['submitName'], //提交用户的信息 必填
            'submitDept' => $data['submitDept'], //提交用户的信息 必填
            'orderNum' => $data['orderNum'], // OA 生成的订单号，必填
            'confName' => str_replace(' ', '', $data['confName']), // 会议简称 必填
            'paperCode' => $data['paperCode'], // 文章编号 必填
            'creditSerial' => isset($data['creditSerial']) ? $data['creditSerial'] : null,
            'remitterInfo' => $data['remitterInfo'], //汇款人信息 必填
            'amount' => floatval($data['amount']), // 付款金额，必须为大于0 ，保留两位小数，比如400.32
            'unit' => $data['unit'], // 付款货币  RMB USD 等
            'remitTime' => $data['remitTime'], // 付款时间，必填 年月日
            'account' => $data['account'], //  收款账户 必填
            'remark' => isset($data['remark']) ? $data['remark'] : null, // 备注，可以为空
            'annex' => isset($data['annex']) ? $data['annex'] : null, //查账截图附件 可以为空
            'callbackUrl' =>  $this->getCallbackUrl(), //回调地址 必填
            'receipt' => isset($data['receipt']) ? $data['receipt'] : array(), // 发票信息可以为空，但必须为数组
            // 2025年4月9日 新增 annexList 信息可以为空，但必须为数组格式
            'annexList' => isset($data['annexList']) ? $data['annexList'] : array(),
        );

        // 使用游客查账专用API路径
        $apiPath = 'addRemitData/guest';
        \Think\Log::write('使用游客查账专用API路径: ' . $apiPath, 'INFO');

        // 调用API提交汇款数据
        $result = send_oa_api_request($apiPath, $requestData);

        // 处理API响应
        if (!isset($result['code']) || $result['code'] != 200) {
            $errorMessage = isset($result['msg']) ? $result['msg'] : '远程API请求失败或返回错误';
            throw new \Common\Exception\RemoteApiException($errorMessage);
        }

        return $result;
    }
}
