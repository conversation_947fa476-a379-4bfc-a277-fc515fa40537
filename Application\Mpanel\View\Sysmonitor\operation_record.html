<extend name="sysmonitor_base" />
<block name="header">
    <!-- 添加Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</block>
<block name="content">
    <style>
        body {
            background-color: #f8f9fa;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .table th {
            background-color: #f8f9fa;
        }

        /* JSON 样式美化 */
        .json-content {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.5;
            background: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            max-height: 500px;
            overflow: auto;
        }

        .json-key {
            color: #0550ae;
            font-weight: bold;
        }

        .json-string {
            color: #22863a;
        }

        .json-number {
            color: #e36209;
        }

        .json-boolean {
            color: #005cc5;
        }

        .json-null {
            color: #6a737d;
        }

        .json-mark {
            color: #24292e;
        }

        .json-content pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        /* 分页样式美化 */
        .pagination {
            margin: 0;
            padding: 10px 0;
        }

        .pagination>div {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .pagination a,
        .pagination span {
            padding: 5px 10px;
            border: 1px solid #ddd;
            color: #666;
            border-radius: 3px;
            text-decoration: none;
            font-size: 13px;
            min-width: 34px;
            text-align: center;
            background: #fff;
        }

        .pagination .num {
            padding: 5px 0;
        }

        .pagination a:hover {
            color: #0d6efd;
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }

        .pagination .current {
            background: #0d6efd;
            color: #fff;
            border-color: #0d6efd;
        }

        .pagination .prev,
        .pagination .next,
        .pagination .end {
            min-width: 60px;
        }

        .pagination .total {
            margin-left: 10px;
            color: #666;
            font-size: 13px;
            border: none;
            padding: 5px 0;
            min-width: auto;
        }

        .pagination .total b {
            color: #0d6efd;
            font-weight: 600;
            margin: 0 3px;
        }

        /* 添加日期输入框样式 */
        input[type="date"] {
            cursor: pointer;
        }
        .input-group input[type="date"]::-webkit-calendar-picker-indicator {
            margin-left: 0;
        }
    </style>
</head>

<boc>
    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history"></i> 操作日志
                        </h5>
                    </div>

                    <!-- 搜索框 -->
                    <div class="card-body border-bottom">
                        <form class="row g-3" method="get" action="{:U('operation_record')}">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-person"></i></span>
                                    <input type="text" class="form-control" name="nickname" value="{$nickname}" placeholder="管理员姓名">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="type">
                                    <option value="0">所有类型</option>
                                    <volist name="type_list" id="type_name" key="type_id">
                                        <option value="{$type_id}" <if condition="$type eq $type_id">selected</if>>{$type_name}</option>
                                    </volist>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-calendar3"></i></span>
                                    <input type="date" class="form-control" id="start_time" name="start_time" value="{$start_time}" placeholder="开始日期">
                                    <span class="input-group-text"><i class="bi bi-arrow-right"></i></span>
                                    <input type="date" class="form-control" id="end_time" name="end_time" value="{$end_time}" placeholder="结束日期">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i> 搜索
                                </button>
                                <button type="button" class="btn btn-danger ms-2" onclick="deleteOldRecords()">
                                    <i class="bi bi-trash"></i> 删除90天前记录
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- 日志列表 -->
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>管理员</th>
                                        <th>操作类型</th>
                                        <th>操作标题</th>
                                        <th>操作时间</th>
                                        <th>IP地址</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <volist name="list" id="vo" key="k">
                                        <tr>
                                            <td>{$vo.id}</td>
                                            <td>
                                                <a href="javascript:void(0);" class="text-primary"
                                                    onclick="showAdminInfo(this)" data-fullname="{$vo.admin.fullname}"
                                                    data-username="{$vo.admin.username}" data-email="{$vo.admin.email}"
                                                    data-grade="{$vo.admin.grade}" data-parent="{$vo.admin.parent}">
                                                    {$vo.admin.fullname}
                                                </a>
                                            </td>
                                            <td>
                                                <span
                                                    class="badge bg-{$vo['type'] == 1 ? 'success' : ($vo['type'] == 2 ? 'info' : ($vo['type'] == 3 ? 'danger' : ($vo['type'] == 4 ? 'warning' : 'secondary')))}">
                                                    {$type_list[$vo['type']]}
                                                </span>
                                            </td>
                                            <td>{$vo.title}<if condition="$vo.url neq ''">
                                                <a href="javascript:void(0);" class="ms-2 text-primary" onclick="showUrl('{$vo.url}')" title="查看URL">
                                                    <i class="bi bi-link-45deg"></i>
                                                </a>
                                            </if></td>
                                            <td>{$vo.create_time|date="Y-m-d H:i:s",###}</td>
                                            <td>{$vo.ip}</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-info"
                                                    onclick="showContent(this)"
                                                    data-content="{$vo.content|base64_encode}">
                                                    <i class="bi bi-eye"></i> 详情
                                                </button>
                                            </td>
                                        </tr>
                                    </volist>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 分页 -->
                    <div class="card-footer py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted small">
                                提示：点击管理员姓名可查看详细信息
                            </div>
                            <div class="pagination">{$page}</div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作内容模态框 -->
    <div class="modal fade" id="contentModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">操作详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="json-content">
                        <pre id="jsonContent"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理员信息模态框 -->
    <div class="modal fade" id="adminModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">管理员详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="fw-bold">姓名：</label>
                        <span id="adminFullname"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">用户名：</label>
                        <span id="adminUsername"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">邮箱：</label>
                        <span id="adminEmail"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">等级：</label>
                        <span id="adminGrade"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">上级管理员：</label>
                        <span id="adminParent"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- URL显示模态框 -->
    <div class="modal fade" id="urlModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">操作URL</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="input-group">
                        <input type="text" class="form-control" id="urlInput" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyUrl()">
                            <i class="bi bi-clipboard"></i> 复制
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化日期范围
            initializeDateRange();
            
            // 初始化模态框
            window.contentModal = new bootstrap.Modal(document.getElementById('contentModal'));
            
            // 清理模态框内容
            document.getElementById('contentModal').addEventListener('hidden.bs.modal', function () {
                this.querySelector('.modal-body').innerHTML = '';
            });
        });

        // 初始化日期范围
        function initializeDateRange() {
            var endTime = document.getElementById('end_time');
            var startTime = document.getElementById('start_time');
            
            // 设置最大日期为今天
            var today = new Date().toISOString().split('T')[0];
            endTime.max = today;
            startTime.max = today;
            
            // 开始日期变化时更新结束日期最小值
            startTime.addEventListener('change', function() {
                endTime.min = this.value;
            });
            
            // 结束日期变化时更新开始日期最大值
            endTime.addEventListener('change', function() {
                startTime.max = this.value;
            });
        }

        // Base64解码
        function b64DecodeUnicode(str) {
            return decodeURIComponent(atob(str).split('').map(function(c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
        }

        // 格式化JSON
        function formatJSON(str) {
            try {
                let obj = JSON.parse(str);
                let formatted = JSON.stringify(obj, null, 2)
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                        let cls = 'json-number';
                        if (/^"/.test(match)) {
                            if (/:$/.test(match)) {
                                cls = 'json-key';
                            } else {
                                cls = 'json-string';
                            }
                        } else if (/true|false/.test(match)) {
                            cls = 'json-boolean';
                        } else if (/null/.test(match)) {
                            cls = 'json-null';
                        }
                        return '<span class="' + cls + '">' + match + '</span>';
                    });
                return formatted;
            } catch (e) {
                console.error('JSON格式化失败:', e);
                return str;
            }
        }

        // 显示管理员信息
        function showAdminInfo(element) {
            let data = element.dataset;
            let content = `
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tr><th>姓名</th><td>${data.fullname || '未设置'}</td></tr>
                        <tr><th>用户名</th><td>${data.username || '未设置'}</td></tr>
                        <tr><th>邮箱</th><td>${data.email || '未设置'}</td></tr>
                        <tr><th>级别</th><td>${data.grade || '未知'}</td></tr>
                        <tr><th>上级</th><td>${data.parent || '无'}</td></tr>
                    </table>
                </div>`;
            
            document.querySelector('#contentModal .modal-title').textContent = '管理员信息';
            document.querySelector('#contentModal .modal-body').innerHTML = content;
            window.contentModal.show();
        }

        // 显示操作内容
        function showContent(element) {
            let content = b64DecodeUnicode(element.dataset.content);
            try {
                content = formatJSON(content);
            } catch (e) {
                console.error('内容格式化失败:', e);
            }
            
            document.querySelector('#contentModal .modal-title').textContent = '操作内容';
            document.querySelector('#contentModal .modal-body').innerHTML = `<pre class="json-content">${content}</pre>`;
            window.contentModal.show();
        }

        // 显示URL
        function showUrl(url) {
            let decodedUrl = decodeURIComponent(url);
            document.querySelector('#contentModal .modal-title').textContent = 'URL信息';
            document.querySelector('#contentModal .modal-body').innerHTML = `
                <div class="input-group">
                    <input type="text" class="form-control" id="urlInput" value="${decodedUrl}" readonly>
                    <button class="btn btn-outline-secondary" type="button" onclick="copyUrl()">
                        复制
                    </button>
                </div>`;
            window.contentModal.show();
        }

        // 复制URL
        function copyUrl() {
            let urlInput = document.getElementById('urlInput');
            urlInput.select();
            document.execCommand('copy');
            
            let button = document.querySelector('#contentModal .btn-outline-secondary');
            button.textContent = '已复制';
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-success');
            
            setTimeout(() => {
                button.textContent = '复制';
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
            }, 2000);
        }

        // 删除90天前的记录
        function deleteOldRecords() {
            if (!confirm('确定要删除90天前的记录吗？此操作不可恢复！')) {
                return;
            }

            fetch('{:U("deleteOldRecords")}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status) {
                    alert('删除成功！');
                    location.reload();
                } else {
                    alert('删除失败：' + data.message);
                }
            })
            .catch(() => {
                alert('操作失败，请稍后重试');
            });
        }
    </script>
</block>