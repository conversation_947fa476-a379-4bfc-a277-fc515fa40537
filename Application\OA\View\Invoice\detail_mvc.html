<extend name="<PERSON><PERSON><PERSON>@Base/admin_base" />

<block name="style">
    <style>
        /* 简化样式，只保留必要的 */
        .label {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
        }

        .text-amount {
            color: #d9534f;
            font-weight: bold;
        }

        .section-title {
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        /* 基本信息区块样式 */
        .info-section {
            margin-bottom: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            position: relative;
        }

        .info-section-header {
            margin: -15px -15px 15px;
            padding: 10px 15px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
        }

        .info-section-header h4 {
            margin: 0;
            font-weight: 600;
        }

        .table-simple {
            margin-bottom: 0;
        }

        .table-simple th {
            background-color: #f9f9f9;
            width: 30%;
        }

        /* 区块类型样式 */
        .section-audit {
            border-left: 4px solid #5bc0de;
        }

        .section-audit .info-section-header {
            background-color: #f0f7fa;
        }

        .section-invoice {
            border-left: 4px solid #5cb85c;
        }

        .section-invoice .info-section-header {
            background-color: #f1f9f1;
        }

        .section-merchant {
            border-left: 4px solid #f0ad4e;
        }

        .section-merchant .info-section-header {
            background-color: #fcf8f2;
        }

        .section-register {
            border-left: 4px solid #d9534f;
        }

        .section-register .info-section-header {
            background-color: #fdf7f7;
        }

        /* 区块图标 */
        .section-icon {
            position: absolute;
            right: 15px;
            top: 12px;
            font-size: 18px;
            color: #ccc;
            opacity: 0.5;
        }
    </style>
</block>

<block name="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <!-- 页面标题和返回按钮 -->
                <div class="clearfix" style="margin-bottom: 15px;">
                    <h3 class="pull-left"><i class="fa fa-file-text-o"></i> 发票详情</h3>
                    <div class="pull-right">
                        <if condition="$invoice.status eq \Common\Lib\InvoiceStatusConstants::INVOICE_PENDING">
                            <button type="button" class="btn btn-sm btn-warning edit-invoice-btn"
                                style="margin-right: 5px;">
                                <i class="fa fa-edit"></i> 编辑发票
                            </button>
                            <button type="button" class="btn btn-sm btn-danger reject-invoice-btn"
                                style="margin-right: 5px;">
                                <i class="fa fa-times"></i> 驳回申请
                            </button>
                        </if>
                        <a href="{:U('OA/Invoice/index')}" class="btn btn-sm btn-default">
                            <i class="fa fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>

                <!-- 查账状态面板 -->
                <include file="Common/audit_status_panel" />

                <!-- 查账详细信息 -->
                <notempty name="auditInfo.has_record">
                    <div class="info-section section-audit" id="audit-record-details">
                        <span class="section-icon"><i class="fa fa-check-circle-o"></i></span>
                        <div class="info-section-header">
                            <h4><i class="fa fa-list-alt"></i> 查账详细信息</h4>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-simple">
                                    <tr>
                                        <th>OA订单号</th>
                                        <td>{$auditInfo.record.oa_id}</td>
                                    </tr>
                                    <tr>
                                        <th>查账状态</th>
                                        <td><span
                                                class="label label-{$auditInfo.record.status_class}">{$auditInfo.record.status_text}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>提交时间</th>
                                        <td>{$auditInfo.record.create_time_text}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-simple">
                                    <tr>
                                        <th>金额</th>
                                        <td><span class="text-amount">{$auditInfo.record.amount}
                                                {$auditInfo.record.unit}</span></td>
                                    </tr>
                                    <tr>
                                        <th>提交人</th>
                                        <td>{$auditInfo.record.submit_name}</td>
                                    </tr>
                                    <tr>
                                        <th>备注</th>
                                        <td>{$auditInfo.record.remark|default="无"}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </notempty>

                <!-- 发票基本信息 -->
                <div class="info-section section-invoice" id="invoice-basic-info">
                    <span class="section-icon"><i class="fa fa-file-text-o"></i></span>
                    <div class="info-section-header">
                        <h4><i class="fa fa-info-circle"></i> 发票基本信息</h4>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-simple">
                                <tr>
                                    <th>发票ID</th>
                                    <td>{$invoice.id}</td>
                                </tr>
                                <tr>
                                    <th>发票系统编号</th>
                                    <td>{$invoice.no|default="--"}</td>
                                </tr>
                                <tr>
                                    <th>发票抬头</th>
                                    <td>{$invoice.invoice_title}</td>
                                </tr>
                                <tr>
                                    <th>发票金额</th>
                                    <td><span class="text-amount">{$invoice.amount}</span></td>
                                </tr>
                                <tr>
                                    <th>发票类型</th>
                                    <td><span
                                            class="label label-{$invoice.invoice_type_class}">{$invoice.invoice_type_text}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>支付途径</th>
                                    <td><span
                                            class="label label-{$invoice.pay_type_class}">{$invoice.pay_type_text}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-simple">
                                <tr>
                                    <th>状态</th>
                                    <td><span class="label label-{$invoice.status_class}">{$invoice.status_text}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>创建时间</th>
                                    <td>{$invoice.create_time_text}</td>
                                </tr>
                                <tr>
                                    <th>更新时间</th>
                                    <td>{$invoice.update_time_text|default="--"}</td>
                                </tr>
                                <tr>
                                    <th>关联发票</th>
                                    <td>
                                        <button type="button" class="btn btn-xs btn-info" id="viewRelatedInvoicesBtn">
                                            <i class="fa fa-link"></i> 查看关联发票
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <th>OA订单号</th>
                                    <td>{$invoice.oa_id|default="--"}</td>
                                </tr>
                                <tr>
                                    <th>商品信息</th>
                                    <td>{$invoice.goods_info_text|default="--"}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- 专票特有信息 -->
                    <if condition="$invoice.invoice_type eq 'bs'">
                        <div class="row" style="margin-top: 15px;">
                            <div class="col-md-12">
                                <h5 class="section-title">专票信息</h5>
                                <div class="col-md-6">
                                    <table class="table table-simple">
                                        <tr>
                                            <th>单位地址</th>
                                            <td>{$invoice.buyer_address}</td>
                                        </tr>
                                        <tr>
                                            <th>开户行</th>
                                            <td>{$invoice.buyer_account_name}</td>
                                        </tr>
                                        <tr>
                                            <th>账号</th>
                                            <td>{$invoice.buyer_account}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </if>

                    <!-- 备注信息 -->
                    <!-- 发票附件信息 -->
                    <if condition="\Common\Lib\InvoiceStatusConstants::isDownloadable($invoice['status'])">
                        <div class="row" style="margin-top: 15px;">
                            <div class="col-md-12">
                                <h5 class="section-title">发票附件</h5>
                                <p>
                                    <a href="{:U('OA/Invoice/invoiceAttachments', array('id' => $invoice['id']))}"
                                        class="btn btn-primary">
                                        <i class="fa fa-download"></i> 下载发票附件
                                    </a>
                                    <span class="text-muted" style="margin-left: 10px;">可下载发票PDF文件</span>
                                </p>
                            </div>
                        </div>
                    </if>

                    <!-- 备注信息 -->
                    <div class="row" style="margin-top: 15px;">
                        <div class="col-md-12">
                            <h5 class="section-title">备注信息</h5>
                            <p>{$invoice.remark|default="无"}</p>
                        </div>
                    </div>
                </div>

                <!-- 买方信息 -->
                <div class="row" style="margin-top: 15px;">
                    <div class="col-md-12">
                        <h5 class="section-title"><i class="fa fa-user"></i> 买方信息</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-simple">
                                    <tr>
                                        <th>纳税人识别号</th>
                                        <td>{$invoice.buyer_tax_num}</td>
                                    </tr>
                                    <tr>
                                        <th>联系电话</th>
                                        <td>{$invoice.buyer_phone}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-simple">
                                    <tr>
                                        <th>电子邮箱</th>
                                        <td>{$invoice.buyer_email}</td>
                                    </tr>
                                    <tr>
                                        <th>买方名称</th>
                                        <td>{$invoice.buyer_name|default="--"}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 开票失败时显示处理结果 -->
                <if
                    condition="$invoice.status eq \Common\Lib\InvoiceStatusConstants::INVOICE_FAILED && !empty($invoice.result_msg)">
                    <div class="row" style="margin-top: 15px;">
                        <div class="col-md-12">
                            <div class="alert alert-danger">
                                <h5><i class="fa fa-exclamation-triangle"></i> 处理结果</h5>
                                <p>{$invoice.result_msg}</p>
                            </div>
                        </div>
                    </div>
                </if>

                <!-- 收款公司信息 -->
                <notempty name="invoice.merchant_name">
                    <div class="info-section section-merchant" id="merchant-info">
                        <span class="section-icon"><i class="fa fa-bank"></i></span>
                        <div class="info-section-header">
                            <h4><i class="fa fa-building"></i> 收款公司信息</h4>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-simple">
                                    <tr>
                                        <th>公司名称</th>
                                        <td>{$invoice.merchant_company}</td>
                                    </tr>
                                    <tr>
                                        <th>公司别称</th>
                                        <td>{$invoice.merchant_name}</td>
                                    </tr>
                                    <tr>
                                        <th>纳税人识别号</th>
                                        <td>{$invoice.merchant_tax_num}</td>
                                    </tr>
                                    <tr>
                                        <th>OA字典值</th>
                                        <td>{$invoice.merchant_oa_dict}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </notempty>

                <!-- 注册信息 -->
                <notempty name="registerInfo">
                    <div class="info-section section-register" id="register-info">
                        <span class="section-icon"><i class="fa fa-id-card-o"></i></span>
                        <div class="info-section-header">
                            <h4><i class="fa fa-user-circle"></i> 关联注册信息</h4>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-simple">
                                    <tr>
                                        <th>注册人</th>
                                        <td>
                                            <php>
                                                $fullname = $registerInfo['firstname'];
                                                if(!empty($registerInfo['middlename'])) {
                                                $fullname .= ' ' . $registerInfo['middlename'];
                                                }
                                                if(!empty($registerInfo['lastname'])) {
                                                $fullname .= ' ' . $registerInfo['lastname'];
                                                }
                                                echo $fullname;
                                            </php>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>邮箱</th>
                                        <td>{$registerInfo.email}</td>
                                    </tr>
                                    <tr>
                                        <th>单位</th>
                                        <td>
                                            <php>
                                                if(!empty($registerInfo['affiliation'])) {
                                                echo $registerInfo['affiliation'];
                                                } else if(!empty($registerInfo['position'])) {
                                                echo $registerInfo['position'];
                                                } else {
                                                echo "--";
                                                }
                                            </php>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-simple">
                                    <tr>
                                        <th>会议名称</th>
                                        <td>{$registerInfo.event}</td>
                                    </tr>
                                    <tr>
                                        <th>文章ID</th>
                                        <td>
                                            <php>
                                                if(!empty($registerInfo['paperid'])) {
                                                echo $registerInfo['paperid'];
                                                } else if(!empty($registerInfo['paper_id'])) {
                                                echo $registerInfo['paper_id'];
                                                } else {
                                                echo "--";
                                                }
                                            </php>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>注册费</th>
                                        <td>
                                            <php>
                                                $fee = "--";
                                                $currency = "";

                                                if(!empty($registerInfo['total'])) {
                                                $fee = $registerInfo['total'];
                                                if(!empty($registerInfo['currency'])) {
                                                $currency = $registerInfo['currency'];
                                                }
                                                } else if(!empty($registerInfo['fee'])) {
                                                $fee = $registerInfo['fee'];
                                                if(!empty($registerInfo['currency'])) {
                                                $currency = $registerInfo['currency'];
                                                }
                                                }

                                                echo '<span class="text-amount">'.$fee.' '.$currency.'</span>';
                                            </php>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </notempty>
            </div>
        </div>
    </div>

    <!-- 关联发票信息 -->
    <div class="modal fade" id="relatedInvoicesModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><i class="fa fa-link"></i> 关联发票信息</h4>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="relatedInvoicesTable">
                            <thead>
                                <tr>
                                    <th>发票ID</th>
                                    <th>发票抬头</th>
                                    <th>金额</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 关联发票数据将通过AJAX加载 -->
                                <tr>
                                    <td colspan="6" class="text-center">
                                        <i class="fa fa-spinner fa-spin"></i> 正在加载关联发票...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑发票模态框 -->
    <div class="modal fade" id="editInvoiceModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><i class="fa fa-edit"></i> 编辑发票信息</h4>
                </div>
                <div class="modal-body">
                    <form id="editInvoiceForm" class="form-horizontal">
                        <input type="hidden" id="edit_invoice_id" name="invoice_id" value="{$invoice.id}">

                        <div class="form-group">
                            <label class="col-sm-3 control-label">发票抬头：<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="edit_invoice_title" name="invoice_title"
                                    value="{$invoice.invoice_title}" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">税号：<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="edit_buyer_tax_num" name="buyer_tax_num"
                                    value="{$invoice.buyer_tax_num}" required>
                                <p class="help-block small">统一社会信用代码或纳税人识别号</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">买方邮箱：<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="email" class="form-control" id="edit_buyer_email" name="buyer_email"
                                    value="{$invoice.buyer_email}" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">买方电话：</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="edit_buyer_phone" name="buyer_phone"
                                    value="{$invoice.buyer_phone}">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveInvoiceBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 驳回发票模态框 -->
    <div class="modal fade" id="rejectInvoiceModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><i class="fa fa-times-circle"></i> 驳回发票申请</h4>
                </div>
                <div class="modal-body">
                    <form id="rejectInvoiceForm" class="form-horizontal">
                        <input type="hidden" id="reject_invoice_id" name="invoice_id" value="{$invoice.id}">

                        <div class="form-group">
                            <label class="col-sm-3 control-label">驳回原因：<span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="reject_reason" name="reject_reason" rows="4"
                                    required></textarea>
                                <p class="help-block small">请详细说明驳回原因，以便申请人了解并修正</p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmRejectBtn">确认驳回</button>
                </div>
            </div>
        </div>
    </div>
</block>

<block name="script">
    <script>
        $(document).ready(function () {
            // 初始化事件监听
            initEventListeners();
        });

        // 初始化所有事件监听
        function initEventListeners() {
            // 编辑发票按钮点击事件
            $('.edit-invoice-btn').click(function () {
                $('#editInvoiceModal').modal('show');
            });

            // 驳回发票按钮点击事件
            $('.reject-invoice-btn').click(function () {
                $('#rejectInvoiceModal').modal('show');
            });

            // 查看关联发票按钮点击事件
            $('#viewRelatedInvoicesBtn').click(function () {
                loadRelatedInvoices();
                $('#relatedInvoicesModal').modal('show');
            });

            // 保存发票信息按钮点击事件
            $('#saveInvoiceBtn').click(function () {
                saveInvoiceInfo();
            });

            // 确认驳回按钮点击事件
            $('#confirmRejectBtn').click(function () {
                rejectInvoice();
            });
        }

        // 加载关联发票信息
        function loadRelatedInvoices() {
            var invoiceId = '{$invoice.id}';
            var oaId = '{$invoice.oa_id}';
            var regId = '{$invoice.reg_id}';

            if (!oaId && !regId) {
                $('#relatedInvoicesTable tbody').html('<tr><td colspan="6" class="text-center">没有找到关联发票信息</td></tr>');
                return;
            }

            // 显示加载中
            $('#relatedInvoicesTable tbody').html('<tr><td colspan="6" class="text-center"><i class="fa fa-spinner fa-spin"></i> 正在加载关联发票...</td></tr>');

            // 发送AJAX请求获取关联发票
            $.ajax({
                url: '{:U("OA/Invoice/getRelatedInvoices")}',
                type: 'POST',
                dataType: 'json',
                data: {
                    invoice_id: invoiceId,
                    oa_id: oaId,
                    reg_id: regId
                },
                success: function (response) {
                    if (response.status && response.data && response.data.length > 0) {
                        renderRelatedInvoices(response.data);
                    } else {
                        $('#relatedInvoicesTable tbody').html('<tr><td colspan="6" class="text-center">没有找到关联发票信息</td></tr>');
                    }
                },
                error: function () {
                    $('#relatedInvoicesTable tbody').html('<tr><td colspan="6" class="text-center text-danger"><i class="fa fa-exclamation-circle"></i> 加载失败，请稍后重试</td></tr>');
                }
            });
        }

        // 渲染关联发票表格
        function renderRelatedInvoices(invoices) {
            var html = '';
            var currentInvoiceId = '{$invoice.id}';

            $.each(invoices, function (index, invoice) {
                var isCurrentInvoice = (invoice.id == currentInvoiceId);
                var rowClass = isCurrentInvoice ? 'info' : '';
                var statusLabel = getStatusLabel(invoice.status, invoice.status_text);

                html += '<tr class="' + rowClass + '">';
                html += '<td>' + invoice.id + (isCurrentInvoice ? ' <span class="label label-primary">当前</span>' : '') + '</td>';
                html += '<td>' + invoice.invoice_title + '</td>';
                html += '<td>' + invoice.amount + '</td>';
                html += '<td>' + statusLabel + '</td>';
                html += '<td>' + invoice.create_time_text + '</td>';
                html += '<td>';

                if (!isCurrentInvoice) {
                    html += '<a href="' + '{:U("OA/Invoice/detail")}' + '?id=' + invoice.id + '" class="btn btn-xs btn-default" target="_blank">';
                    html += '<i class="fa fa-eye"></i> 查看';
                    html += '</a>';
                } else {
                    html += '<span class="text-muted">当前查看</span>';
                }

                html += '</td>';
                html += '</tr>';
            });

            $('#relatedInvoicesTable tbody').html(html);
        }

        // 获取状态标签HTML
        function getStatusLabel(status, statusText) {
            var labelClass = 'default';

            switch (parseInt(status)) {
                case 10: // 待审核
                    labelClass = 'default';
                    break;
                case 20: // 已驳回
                    labelClass = 'warning';
                    break;
                case 30: // 等待开票
                    labelClass = 'info';
                    break;
                case 40: // 开票失败
                    labelClass = 'danger';
                    break;
                case 50: // 开票成功
                    labelClass = 'success';
                    break;
                case 90: // 已作废
                    labelClass = 'warning';
                    break;
            }

            return '<span class="label label-' + labelClass + '">' + statusText + '</span>';
        }

        // 保存发票信息
        function saveInvoiceInfo() {
            // 验证表单
            var invoiceId = $('#edit_invoice_id').val();
            var invoiceTitle = $('#edit_invoice_title').val();
            var buyerTaxNum = $('#edit_buyer_tax_num').val();
            var buyerEmail = $('#edit_buyer_email').val();
            var buyerPhone = $('#edit_buyer_phone').val();

            if (!invoiceTitle) {
                showError('发票抬头不能为空');
                return;
            }

            if (!buyerTaxNum) {
                showError('税号不能为空');
                return;
            }

            if (!buyerEmail) {
                showError('买方邮箱不能为空');
                return;
            }

            if (!validateEmail(buyerEmail)) {
                showError('邮箱格式不正确');
                return;
            }

            // 显示加载中
            showLoading('正在保存...');

            // 发送AJAX请求
            $.ajax({
                url: '{:U("OA/Invoice/editInvoiceInfo")}',
                type: 'POST',
                dataType: 'json',
                data: {
                    invoice_id: invoiceId,
                    invoice_title: invoiceTitle,
                    buyer_tax_num: buyerTaxNum,
                    buyer_email: buyerEmail,
                    buyer_phone: buyerPhone
                },
                success: function (response) {
                    if (response.status) {
                        // 关闭模态框
                        $('#editInvoiceModal').modal('hide');

                        // 显示成功消息
                        showSuccess(response.message || '发票信息更新成功', function () {
                            // 刷新页面
                            location.reload();
                        });
                    } else {
                        showError(response.message || '发票信息更新失败');
                    }
                },
                error: function () {
                    showError('网络错误，请稍后重试');
                }
            });
        }

        // 驳回发票
        function rejectInvoice() {
            // 验证表单
            var invoiceId = $('#reject_invoice_id').val();
            var rejectReason = $('#reject_reason').val();

            if (!rejectReason) {
                showError('请填写驳回原因');
                return;
            }

            // 显示加载中
            showLoading('正在提交...');

            // 发送AJAX请求
            $.ajax({
                url: '{:U("OA/Invoice/rejectInvoice")}',
                type: 'POST',
                dataType: 'json',
                data: {
                    invoice_id: invoiceId,
                    refusal: rejectReason
                },
                success: function (response) {
                    if (response.status) {
                        // 关闭模态框
                        $('#rejectInvoiceModal').modal('hide');

                        // 显示成功消息
                        showSuccess(response.message || '发票申请已驳回', function () {
                            // 刷新页面
                            location.reload();
                        });
                    } else {
                        showError(response.message || '驳回发票申请失败');
                    }
                },
                error: function () {
                    showError('网络错误，请稍后重试');
                }
            });
        }

        // 验证邮箱格式
        function validateEmail(email) {
            var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(String(email).toLowerCase());
        }

        // 显示加载中
        function showLoading(message) {
            Swal.fire({
                title: message || '处理中',
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        // 显示成功消息
        function showSuccess(message, callback) {
            Swal.fire({
                title: '成功',
                text: message,
                icon: 'success',
                confirmButtonText: '确定',
                timer: 2000,
                timerProgressBar: true
            }).then(function () {
                if (typeof callback === 'function') {
                    callback();
                }
            });
        }

        // 显示错误消息
        function showError(message) {
            Swal.fire({
                title: '错误',
                text: message,
                icon: 'error',
                confirmButtonText: '确定'
            });
        }
    </script>
</block>