<?php

namespace Mpanel\Model;

use Think\Model;

class SubConferenceModel extends Model
{

	protected $_validate = array(

		array('org', 'require', '会议分组未选择！'),

		array('titlte', 'require', '会议全称为必填！'),

		array('event', 'require', '会议简称为必填！'),

		array('event', '/^[A-Za-z0-9]+[0-9]{4}$/', '会议简称格式不正确！必须以大小写字母或数字开头，后跟4位数字，中间不要有空格', 0, 'regex'),

		array('title', '', '会议的全称不能重复，请重新检查', 0, 'unique', 1),

		array('place', 'require', '会议的地点为必填！'),

		array('start_date', 'require', '会议开始时间必填！'),

		array('end_date', 'require', '会议结束时间为必填！'),

		array('deadline', 'require', '会议投稿截止为必填！'),

		array('notice_date', 'require', '会议通知日期为必填！'),

		array('website', 'require', '会议网址为必填！'),

		array('email', 'require', 'email为必填！'),

	);

	protected $_auto = array(

		array('type', 'set_type', 3, 'callback'),

		array('pretype', 'set_pretype', 3, 'callback'),

		array('extras', 'set_extras', 3, 'callback'),

		array('userid', 'get_userid', 1, 'callback'),

		array('short_url', 'get_short_url', 1, 'callback'), // 只在新增时调用get_short_url方法

		array('addtime', 'time', 1, 'function'), // 对update_time字段在更新的时候写入当前时间戳

		array('ip', 'get_client_ip', 1, 'function'), // 对update_time字段在更新的时候写入当前时间戳

		array('start_date', 'strtotime', 3, 'function'),

		array('end_date', 'strtotime', 3, 'function'),

		array('deadline', 'strtotime', 3, 'function'),

		array('notice_date', 'strtotime', 3, 'function'),

		array('regend_date', 'strtotime', 3, 'function'),

		array('more', 'set_more', 3, 'callback'),

		array('paper_type', 'set_paper_type', 3, 'callback'),

	);



	function pwd()
	{

		$password = I('password');

		dump($password);



		if ($password) {

			$rs = strlen($password);

			if ($rs < 6) {

				exit('【错误】会议查看密码不小于6位');
			}

			return 0;
		}
	}



	function set_type()
	{

		$arr = I('type');

		//$arr = array_no_empty( $arr );

		$str = array2string($arr);

		return $str;
	}



	function set_extras()
	{

		$arr = I('extras');

		//$arr = array_no_empty( $arr );

		$str = array2string($arr);

		return $str;
	}



	function set_more()
	{

		$arr = I('more');

		//$arr = array_no_empty( $arr );

		$str = array2string($arr);

		return $str;
	}

	function set_pretype()
	{

		$arr = I('pretype');
		$str = array2string($arr);
		return $str;
	}

	function set_paper_type()
	{
		$paper_type = I('paper_type');
		// 去除首尾空格
		$paper_type = trim($paper_type);
		// 如果为空，返回空字符串
		if (empty($paper_type)) {
			return '';
		}
		// 返回处理后的字符串
		return $paper_type;
	}



	function get_client_time()
	{

		return time();
	}



	function get_userid()
	{

		$userid = session('userid');

		return $userid;
	}



	function start_date_mk()
	{

		$dates = I('start_date');

		return strtotime($dates);
	}



	/**
	 * 根据会议简称自动生成短网址
	 * @return string 返回生成的短网址
	 */
	function get_short_url() {
		$event = I( 'event' );
		$event = strtolower( $event );
		$event = del_space( $event );
		$map[ 'event' ] = array( 'eq', $event );
		$rs = $this->where( $map )->count();
		if ( $rs ) {
			$short_url = $event . '_' . $rs;
		} else {
			$short_url = $event;
		}
		return $short_url;
	}
}
